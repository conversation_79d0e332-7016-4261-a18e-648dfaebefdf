{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n  }).format(date)\n}\n\nexport interface ParsedError {\n  timestamp: string;\n  level: string;\n  message: string;\n  task?: string;\n  lineNumber?: number;\n  errorType?: string;\n  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  context?: string;\n  fileName?: string;\n}\n\nexport function parseErrorFile(content: string): ParsedError[] {\n  const lines = content.split('\\n').filter(line => line.trim());\n  return lines.map((line, index) => {\n    // Pattern principal pour les logs CAOFORS\n    const caoMatch = line.match(/^(\\d{2}\\.\\d{2}\\.\\d{4} \\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/);\n\n    if (caoMatch) {\n      const [, timestamp, level, message] = caoMatch;\n      const taskMatch = message.match(/task \\[([^\\]]+)\\]/);\n\n      // Patterns étendus pour extraire les numéros de ligne\n      const linePatterns = [\n        /(?:line|ligne)[:\\s]+(\\d+)/i,                    // line: 123, ligne: 123\n        /\\[.*(?:line|ligne)[:\\s]+(\\d+)/i,                // [something line: 123]\n        /caofors\\.ec line: (\\d+)/,                       // caofors.ec line: 123\n        /at line (\\d+)/i,                                // at line 123\n        /error on line (\\d+)/i,                          // error on line 123\n        /(\\d+):\\d+:/,                                    // 123:45: (format file:line:col)\n        /line (\\d+) column \\d+/i,                        // line 123 column 45\n        /\\((\\d+),\\d+\\)/,                                 // (123,45) format\n        /:\\s*(\\d+)\\s*:/,                                 // : 123 :\n        /ligne\\s+(\\d+)/i,                                // ligne 123\n        /row\\s+(\\d+)/i,                                  // row 123\n        /position\\s+(\\d+)/i                              // position 123\n      ];\n\n      let lineNumber: number | undefined;\n      for (const pattern of linePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          lineNumber = parseInt(match[1]);\n          break;\n        }\n      }\n\n      // Extraction du nom de fichier\n      const filePatterns = [\n        /([a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z]+)/,          // filename.ext\n        /in file ([^\\s]+)/i,                             // in file filename\n        /file \"([^\"]+)\"/i,                               // file \"filename\"\n        /([^\\s]+\\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions\n      ];\n\n      let fileName: string | undefined;\n      for (const pattern of filePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          fileName = match[1];\n          break;\n        }\n      }\n\n      // Détection du type d'erreur\n      const errorType = detectErrorType(message);\n\n      // Détection de la sévérité\n      const severity = detectSeverity(level, message);\n\n      return {\n        timestamp,\n        level,\n        message,\n        task: taskMatch ? taskMatch[1] : undefined,\n        lineNumber,\n        errorType,\n        severity,\n        fileName\n      };\n    }\n\n    // Patterns pour d'autres formats de logs\n    const genericPatterns = [\n      // Format standard: ERROR: message at line 123\n      /^(ERROR|WARNING|INFO|DEBUG):\\s*(.+?)(?:\\s+at\\s+line\\s+(\\d+))?$/i,\n      // Format avec timestamp ISO\n      /^(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/,\n      // Format simple: level message\n      /^(\\w+):\\s*(.+)$/\n    ];\n\n    for (const pattern of genericPatterns) {\n      const match = line.match(pattern);\n      if (match) {\n        const level = match[1] || match[2] || 'UNKNOWN';\n        const message = match[2] || match[3] || match[1] || line;\n        const lineNumber = match[3] ? parseInt(match[3]) : undefined;\n\n        return {\n          timestamp: match[1]?.includes('T') ? match[1] : '',\n          level,\n          message,\n          lineNumber,\n          errorType: detectErrorType(message),\n          severity: detectSeverity(level, message)\n        };\n      }\n    }\n\n    // Fallback pour les lignes non reconnues\n    return {\n      timestamp: '',\n      level: 'UNKNOWN',\n      message: line,\n      lineNumber: index + 1,\n      errorType: 'UNKNOWN',\n      severity: 'LOW'\n    };\n  });\n}\n\nfunction detectErrorType(message: string): string {\n  const errorPatterns = [\n    { pattern: /variable.*not.*(?:initialized|declared|defined)/i, type: 'VARIABLE_NOT_INITIALIZED' },\n    { pattern: /undefined.*variable/i, type: 'UNDEFINED_VARIABLE' },\n    { pattern: /syntax.*error/i, type: 'SYNTAX_ERROR' },\n    { pattern: /compilation.*error/i, type: 'COMPILATION_ERROR' },\n    { pattern: /runtime.*error/i, type: 'RUNTIME_ERROR' },\n    { pattern: /null.*pointer/i, type: 'NULL_POINTER' },\n    { pattern: /memory.*leak/i, type: 'MEMORY_LEAK' },\n    { pattern: /buffer.*overflow/i, type: 'BUFFER_OVERFLOW' },\n    { pattern: /division.*by.*zero/i, type: 'DIVISION_BY_ZERO' },\n    { pattern: /file.*not.*found/i, type: 'FILE_NOT_FOUND' },\n    { pattern: /permission.*denied/i, type: 'PERMISSION_DENIED' },\n    { pattern: /connection.*failed/i, type: 'CONNECTION_ERROR' },\n    { pattern: /timeout/i, type: 'TIMEOUT_ERROR' },\n    { pattern: /locked.*already.*runs/i, type: 'RESOURCE_LOCKED' },\n    { pattern: /sql.*error/i, type: 'SQL_ERROR' },\n    { pattern: /database.*error/i, type: 'DATABASE_ERROR' },\n    { pattern: /assertion.*failed/i, type: 'ASSERTION_FAILED' },\n    { pattern: /stack.*overflow/i, type: 'STACK_OVERFLOW' },\n    { pattern: /out.*of.*memory/i, type: 'OUT_OF_MEMORY' },\n    { pattern: /invalid.*argument/i, type: 'INVALID_ARGUMENT' },\n    { pattern: /type.*mismatch/i, type: 'TYPE_MISMATCH' }\n  ];\n\n  for (const { pattern, type } of errorPatterns) {\n    if (pattern.test(message)) {\n      return type;\n    }\n  }\n\n  return 'GENERAL_ERROR';\n}\n\nfunction detectSeverity(level: string, message: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {\n  // Sévérité basée sur le niveau\n  const levelSeverity: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {\n    'DEBUG': 'LOW',\n    'INFO': 'LOW',\n    'WARNING': 'MEDIUM',\n    'WARN': 'MEDIUM',\n    'ERROR': 'HIGH',\n    'FATAL': 'CRITICAL',\n    'CRITICAL': 'CRITICAL'\n  };\n\n  let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';\n\n  // Ajustement basé sur le contenu du message\n  const criticalPatterns = [\n    /crash/i, /fatal/i, /critical/i, /system.*failure/i,\n    /memory.*corruption/i, /security.*breach/i\n  ];\n\n  const highPatterns = [\n    /error/i, /exception/i, /failed/i, /abort/i,\n    /null.*pointer/i, /buffer.*overflow/i, /stack.*overflow/i\n  ];\n\n  const lowPatterns = [\n    /warning/i, /info/i, /debug/i, /notice/i\n  ];\n\n  if (criticalPatterns.some(pattern => pattern.test(message))) {\n    severity = 'CRITICAL';\n  } else if (highPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'LOW' ? 'HIGH' : severity;\n  } else if (lowPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'HIGH' ? 'MEDIUM' : severity;\n  }\n\n  return severity;\n}\n\nexport interface CodeContext {\n  targetLine: string;\n  contextLines: Array<{ number: number; content: string; isTarget: boolean }>;\n  analysis?: {\n    variables: string[];\n    functions: string[];\n    potentialIssues: string[];\n    suggestions: string[];\n  };\n}\n\nexport function extractLineFromProgram(programContent: string, lineNumber: number, context: number = 2): CodeContext {\n  const lines = programContent.split('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n\n  const contextLines = [];\n  for (let i = startLine; i < endLine; i++) {\n    contextLines.push({\n      number: i + 1,\n      content: lines[i] || '',\n      isTarget: i === lineNumber - 1\n    });\n  }\n\n  // Analyse contextuelle avancée\n  const analysis = analyzeCodeContext(lines, lineNumber, context);\n\n  return {\n    targetLine,\n    contextLines,\n    analysis\n  };\n}\n\nexport function analyzeCodeContext(lines: string[], lineNumber: number, context: number = 5): {\n  variables: string[];\n  functions: string[];\n  potentialIssues: string[];\n  suggestions: string[];\n} {\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n  const contextCode = lines.slice(startLine, endLine).join('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  // Extraction des variables\n  const variables = extractVariables(contextCode);\n\n  // Extraction des fonctions\n  const functions = extractFunctions(contextCode);\n\n  // Détection des problèmes potentiels\n  const potentialIssues = detectPotentialIssues(targetLine, contextCode);\n\n  // Génération de suggestions\n  const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);\n\n  return {\n    variables,\n    functions,\n    potentialIssues,\n    suggestions\n  };\n}\n\nfunction extractVariables(code: string): string[] {\n  const variables = new Set<string>();\n\n  // Patterns pour différents langages\n  const patterns = [\n    // C/C++: type var = value; ou type var;\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Variables avec déclaration explicite\n    /(?:var|let|const)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Assignations\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=/g,\n    // Paramètres de fonction\n    /\\(\\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\\s*\\)/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1]) {\n        // Séparer les variables multiples (pour les paramètres)\n        match[1].split(',').forEach(v => {\n          const varName = v.trim().split(/\\s+/).pop();\n          if (varName && varName.length > 1) {\n            variables.add(varName);\n          }\n        });\n      }\n    }\n  });\n\n  return Array.from(variables);\n}\n\nfunction extractFunctions(code: string): string[] {\n  const functions = new Set<string>();\n\n  const patterns = [\n    // Définitions de fonction C/C++\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Appels de fonction\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Fonctions JavaScript/TypeScript\n    /function\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Méthodes\n    /\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1] && match[1].length > 1) {\n        functions.add(match[1]);\n      }\n    }\n  });\n\n  return Array.from(functions);\n}\n\nfunction detectPotentialIssues(targetLine: string, contextCode: string): string[] {\n  const issues: string[] = [];\n\n  // Vérifications communes\n  const checks = [\n    {\n      pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=\\s*[^;]*$/,\n      message: \"Variable potentiellement non initialisée ou assignation incomplète\"\n    },\n    {\n      pattern: /\\*\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?![=])/,\n      message: \"Déréférencement de pointeur - vérifier si le pointeur est NULL\"\n    },\n    {\n      pattern: /\\[\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\]/,\n      message: \"Accès tableau - vérifier les limites d'index\"\n    },\n    {\n      pattern: /\\/\\*.*\\*\\//,\n      message: \"Code commenté - peut indiquer du code problématique\"\n    },\n    {\n      pattern: /TODO|FIXME|HACK|BUG/i,\n      message: \"Commentaire indiquant un problème connu\"\n    },\n    {\n      pattern: /malloc|calloc|free/,\n      message: \"Gestion mémoire manuelle - vérifier les fuites mémoire\"\n    },\n    {\n      pattern: /strcpy|strcat|sprintf/,\n      message: \"Fonction potentiellement dangereuse - risque de buffer overflow\"\n    }\n  ];\n\n  checks.forEach(check => {\n    if (check.pattern.test(targetLine)) {\n      issues.push(check.message);\n    }\n  });\n\n  // Vérifications contextuelles\n  if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {\n    issues.push(\"Requête SQL sans vérification d'erreur appropriée\");\n  }\n\n  if (targetLine.includes('=') && !targetLine.includes(';')) {\n    issues.push(\"Assignation sans point-virgule terminal\");\n  }\n\n  return issues;\n}\n\nfunction generateSuggestions(targetLine: string, contextCode: string, issues: string[]): string[] {\n  const suggestions: string[] = [];\n\n  // Suggestions basées sur les problèmes détectés\n  if (issues.some(issue => issue.includes('non initialisée'))) {\n    suggestions.push(\"Initialiser la variable avant utilisation\");\n    suggestions.push(\"Vérifier la déclaration de la variable\");\n  }\n\n  if (issues.some(issue => issue.includes('pointeur'))) {\n    suggestions.push(\"Ajouter une vérification NULL avant déréférencement\");\n    suggestions.push(\"Utiliser des pointeurs intelligents si possible\");\n  }\n\n  if (issues.some(issue => issue.includes('tableau'))) {\n    suggestions.push(\"Vérifier que l'index est dans les limites du tableau\");\n    suggestions.push(\"Utiliser des fonctions sécurisées pour l'accès aux tableaux\");\n  }\n\n  if (targetLine.includes('EXEC SQL')) {\n    suggestions.push(\"Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL\");\n    suggestions.push(\"Vérifier le code de retour SQL\");\n  }\n\n  // Suggestions générales\n  if (targetLine.trim().length === 0) {\n    suggestions.push(\"Ligne vide - vérifier si du code manque\");\n  }\n\n  if (targetLine.includes('//') || targetLine.includes('/*')) {\n    suggestions.push(\"Code commenté - vérifier si c'est intentionnel\");\n  }\n\n  return suggestions;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAcO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC1D,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,0CAA0C;QAC1C,MAAM,WAAW,KAAK,KAAK,CAAC;QAE5B,IAAI,UAAU;YACZ,MAAM,GAAG,WAAW,OAAO,QAAQ,GAAG;YACtC,MAAM,YAAY,QAAQ,KAAK,CAAC;YAEhC,sDAAsD;YACtD,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,oBAAiD,eAAe;aACjE;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,aAAa,SAAS,KAAK,CAAC,EAAE;oBAC9B;gBACF;YACF;YAEA,+BAA+B;YAC/B,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA,qDAAqD,oBAAoB;aAC1E;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,WAAW,KAAK,CAAC,EAAE;oBACnB;gBACF;YACF;YAEA,6BAA6B;YAC7B,MAAM,YAAY,gBAAgB;YAElC,2BAA2B;YAC3B,MAAM,WAAW,eAAe,OAAO;YAEvC,OAAO;gBACL;gBACA;gBACA;gBACA,MAAM,YAAY,SAAS,CAAC,EAAE,GAAG;gBACjC;gBACA;gBACA;gBACA;YACF;QACF;QAEA,yCAAyC;QACzC,MAAM,kBAAkB;YACtB,8CAA8C;YAC9C;YACA,4BAA4B;YAC5B;YACA,+BAA+B;YAC/B;SACD;QAED,KAAK,MAAM,WAAW,gBAAiB;YACrC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACtC,MAAM,UAAU,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACpD,MAAM,aAAa,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;gBAEnD,OAAO;oBACL,WAAW,KAAK,CAAC,EAAE,EAAE,SAAS,OAAO,KAAK,CAAC,EAAE,GAAG;oBAChD;oBACA;oBACA;oBACA,WAAW,gBAAgB;oBAC3B,UAAU,eAAe,OAAO;gBAClC;YACF;QACF;QAEA,yCAAyC;QACzC,OAAO;YACL,WAAW;YACX,OAAO;YACP,SAAS;YACT,YAAY,QAAQ;YACpB,WAAW;YACX,UAAU;QACZ;IACF;AACF;AAEA,SAAS,gBAAgB,OAAe;IACtC,MAAM,gBAAgB;QACpB;YAAE,SAAS;YAAoD,MAAM;QAA2B;QAChG;YAAE,SAAS;YAAwB,MAAM;QAAqB;QAC9D;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAmB,MAAM;QAAgB;QACpD;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAiB,MAAM;QAAc;QAChD;YAAE,SAAS;YAAqB,MAAM;QAAkB;QACxD;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAqB,MAAM;QAAiB;QACvD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAY,MAAM;QAAgB;QAC7C;YAAE,SAAS;YAA0B,MAAM;QAAkB;QAC7D;YAAE,SAAS;YAAe,MAAM;QAAY;QAC5C;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAoB,MAAM;QAAgB;QACrD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAmB,MAAM;QAAgB;KACrD;IAED,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,cAAe;QAC7C,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,KAAa,EAAE,OAAe;IACpD,+BAA+B;IAC/B,MAAM,gBAAwE;QAC5E,SAAS;QACT,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,YAAY;IACd;IAEA,IAAI,WAAW,aAAa,CAAC,MAAM,WAAW,GAAG,IAAI;IAErD,4CAA4C;IAC5C,MAAM,mBAAmB;QACvB;QAAU;QAAU;QAAa;QACjC;QAAuB;KACxB;IAED,MAAM,eAAe;QACnB;QAAU;QAAc;QAAW;QACnC;QAAkB;QAAqB;KACxC;IAED,MAAM,cAAc;QAClB;QAAY;QAAS;QAAU;KAChC;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,WAAW;IACb,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC9D,WAAW,aAAa,QAAQ,SAAS;IAC3C,OAAO,IAAI,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC7D,WAAW,aAAa,SAAS,WAAW;IAC9C;IAEA,OAAO;AACT;AAaO,SAAS,uBAAuB,cAAsB,EAAE,UAAkB,EAAE,UAAkB,CAAC;IACpG,MAAM,QAAQ,eAAe,KAAK,CAAC;IACnC,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IAEpD,MAAM,eAAe,EAAE;IACvB,IAAK,IAAI,IAAI,WAAW,IAAI,SAAS,IAAK;QACxC,aAAa,IAAI,CAAC;YAChB,QAAQ,IAAI;YACZ,SAAS,KAAK,CAAC,EAAE,IAAI;YACrB,UAAU,MAAM,aAAa;QAC/B;IACF;IAEA,+BAA+B;IAC/B,MAAM,WAAW,mBAAmB,OAAO,YAAY;IAEvD,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS,mBAAmB,KAAe,EAAE,UAAkB,EAAE,UAAkB,CAAC;IAMzF,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IACpD,MAAM,cAAc,MAAM,KAAK,CAAC,WAAW,SAAS,IAAI,CAAC;IACzD,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,qCAAqC;IACrC,MAAM,kBAAkB,sBAAsB,YAAY;IAE1D,4BAA4B;IAC5B,MAAM,cAAc,oBAAoB,YAAY,aAAa;IAEjE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,oCAAoC;IACpC,MAAM,WAAW;QACf,wCAAwC;QACxC;QACA,uCAAuC;QACvC;QACA,eAAe;QACf;QACA,yBAAyB;QACzB;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,wDAAwD;gBACxD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;oBAC1B,MAAM,UAAU,EAAE,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG;oBACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;wBACjC,UAAU,GAAG,CAAC;oBAChB;gBACF;YACF;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,MAAM,WAAW;QACf,gCAAgC;QAChC;QACA,qBAAqB;QACrB;QACA,kCAAkC;QAClC;QACA,WAAW;QACX;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;gBACnC,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,sBAAsB,UAAkB,EAAE,WAAmB;IACpE,MAAM,SAAmB,EAAE;IAE3B,yBAAyB;IACzB,MAAM,SAAS;QACb;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;KACD;IAED,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa;YAClC,OAAO,IAAI,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,8BAA8B;IAC9B,IAAI,YAAY,QAAQ,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,kBAAkB;QAC9E,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC,MAAM;QACzD,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,UAAkB,EAAE,WAAmB,EAAE,MAAgB;IACpF,MAAM,cAAwB,EAAE;IAEhC,gDAAgD;IAChD,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,qBAAqB;QAC3D,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,cAAc;QACpD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,aAAa;QACnD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,aAAa;QACnC,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,wBAAwB;IACxB,IAAI,WAAW,IAAI,GAAG,MAAM,KAAK,GAAG;QAClC,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,OAAO;QAC1D,YAAY,IAAI,CAAC;IACnB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Bug, Database, Sparkles, ArrowRight, Brain, C<PERSON>, Rocket } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"relative\">\n      {/* Hero Section avec animations */}\n      <div className=\"relative overflow-hidden\">\n        <div className=\"container mx-auto px-4 py-20\">\n          <div className=\"text-center mb-16\">\n            {/* Badge animé avec couleurs corporate */}\n            <div className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#002857]/10 to-[#ff7514]/10 rounded-full mb-8 animate-bounce border border-[#ff7514]/20\">\n              <Sparkles className=\"w-5 h-5 text-[#ff7514] mr-2\" />\n              <span className=\"text-sm font-semibold text-[#002857]\">Nouvelle Génération d&apos;IA</span>\n              <Rocket className=\"w-5 h-5 text-[#ff7514] ml-2\" />\n            </div>\n\n            {/* Titre principal avec gradient */}\n            <h1 className=\"text-6xl md:text-7xl font-black mb-6 leading-tight\">\n              <span className=\"bg-gradient-to-r from-[#002857] via-[#003d7a] to-[#ff7514] bg-clip-text text-transparent\">\n                Leoni Agents\n              </span>\n              <br />\n              <span className=\"text-[#002857] text-4xl md:text-5xl font-bold\">\n                Intelligence Artificielle\n              </span>\n            </h1>\n\n            {/* Sous-titre élégant */}\n            <p className=\"text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\">\n              Révolutionnez votre workflow avec nos agents IA de nouvelle génération.\n              <span className=\"text-[#ff7514] font-semibold\"> Analyse d&apos;erreurs intelligente</span> et\n              <span className=\"text-[#002857] font-semibold\"> génération SQL automatisée</span>\n              pour une productivité sans limites.\n            </p>\n\n            {/* Boutons CTA avec effets */}\n            <div className=\"flex flex-col sm:flex-row justify-center gap-6 mb-16\">\n              <Link href=\"/error-analysis\">\n                <Button size=\"lg\" className=\"group relative bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white px-8 py-4 rounded-2xl shadow-xl shadow-[#ff7514]/25 transform hover:scale-105 transition-all duration-300\">\n                  <Bug className=\"w-6 h-6 mr-3 group-hover:animate-pulse\" />\n                  Analyser les Erreurs\n                  <ArrowRight className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </Link>\n              <Link href=\"/sql-generation\">\n                <Button size=\"lg\" variant=\"outline\" className=\"group relative bg-white/90 backdrop-blur-sm border-2 border-[#002857]/30 hover:border-[#002857] text-[#002857] hover:text-[#001a3d] px-8 py-4 rounded-2xl shadow-xl hover:shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300\">\n                  <Database className=\"w-6 h-6 mr-3 group-hover:animate-pulse\" />\n                  Générer du SQL\n                  <ArrowRight className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </Link>\n            </div>\n\n            {/* Stats impressionnantes */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[#ff7514] mb-2\">99.9%</div>\n                <div className=\"text-gray-600\">Précision d&apos;analyse</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[#002857] mb-2\">&lt;2s</div>\n                <div className=\"text-gray-600\">Temps de réponse</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[#ff8c42] mb-2\">24/7</div>\n                <div className=\"text-gray-600\">Disponibilité</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Grid avec design moderne */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-[#002857] mb-4\">\n            Agents <span className=\"text-[#ff7514]\">Spécialisés</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Découvrez nos agents IA de pointe, conçus pour transformer votre façon de travailler\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 gap-8 mb-16\">\n          {/* Agent d'Analyse d'Erreurs */}\n          <Card className=\"group relative overflow-hidden bg-gradient-to-br from-[#ff7514]/5 to-[#ff7514]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-[#ff7514]/10 to-[#ff8c42]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            <CardHeader className=\"relative z-10\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"p-3 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-2xl shadow-lg shadow-[#ff7514]/30\">\n                  <Bug className=\"w-8 h-8 text-white\" />\n                </div>\n                <div className=\"px-3 py-1 bg-[#ff7514]/10 text-[#e6650f] rounded-full text-xs font-semibold\">\n                  EXPERT\n                </div>\n              </div>\n              <CardTitle className=\"text-2xl font-bold text-[#002857] mb-2\">\n                Agent d&apos;Analyse d&apos;Erreurs\n              </CardTitle>\n              <CardDescription className=\"text-gray-600 text-base\">\n                IA avancée pour la détection et résolution intelligente des erreurs de programme\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"relative z-10\">\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#ff7514] rounded-full mr-3\"></div>\n                  <span>Analyse automatique des fichiers d&apos;erreur</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#ff7514] rounded-full mr-3\"></div>\n                  <span>Localisation précise avec numéros de ligne</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#ff7514] rounded-full mr-3\"></div>\n                  <span>Solutions détaillées et contexte de code</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#ff7514] rounded-full mr-3\"></div>\n                  <span>Support multi-langages de programmation</span>\n                </div>\n              </div>\n              <Link href=\"/error-analysis\">\n                <Button className=\"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#ff7514]/25 transition-all duration-300\">\n                  <Brain className=\"w-5 h-5 mr-2\" />\n                  Commencer l&apos;Analyse\n                  <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          {/* Agent de Génération SQL */}\n          <Card className=\"group relative overflow-hidden bg-gradient-to-br from-[#002857]/5 to-[#002857]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-[#002857]/10 to-[#003d7a]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            <CardHeader className=\"relative z-10\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"p-3 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-2xl shadow-lg shadow-[#002857]/30\">\n                  <Database className=\"w-8 h-8 text-white\" />\n                </div>\n                <div className=\"px-3 py-1 bg-[#002857]/10 text-[#001a3d] rounded-full text-xs font-semibold\">\n                  INNOVANT\n                </div>\n              </div>\n              <CardTitle className=\"text-2xl font-bold text-[#002857] mb-2\">\n                Agent de Génération SQL\n              </CardTitle>\n              <CardDescription className=\"text-gray-600 text-base\">\n                Création automatique de scripts SQL optimisés à partir de spécifications\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"relative z-10\">\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#002857] rounded-full mr-3\"></div>\n                  <span>Génération automatique de scripts SQL</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#002857] rounded-full mr-3\"></div>\n                  <span>Support multi-bases de données</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#002857] rounded-full mr-3\"></div>\n                  <span>Upload de fichiers de spécification</span>\n                </div>\n                <div className=\"flex items-center text-gray-700\">\n                  <div className=\"w-2 h-2 bg-[#002857] rounded-full mr-3\"></div>\n                  <span>Optimisation et bonnes pratiques</span>\n                </div>\n              </div>\n              <Link href=\"/sql-generation\">\n                <Button className=\"w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#002857]/25 transition-all duration-300\">\n                  <Cpu className=\"w-5 h-5 mr-2\" />\n                  Générer du SQL\n                  <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;kDACvD,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;0CAIpB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAA2F;;;;;;kDAG3G,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAMlE,8OAAC;gCAAE,WAAU;;oCAA4E;kDAEvF,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAA2C;kDAC1F,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;oCAAkC;;;;;;;0CAKnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;;8DAC1B,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAA2C;8DAE1D,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;;8DAC5C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2C;8DAE/D,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAyC;kDAC9C,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEAA8E;;;;;;;;;;;;0DAI/F,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAyC;;;;;;0DAG9D,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAA0B;;;;;;;;;;;;kDAIvD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;sEAElC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEAA8E;;;;;;;;;;;;0DAI/F,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAyC;;;;;;0DAG9D,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAA0B;;;;;;;;;;;;kDAIvD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;sEAEhC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}