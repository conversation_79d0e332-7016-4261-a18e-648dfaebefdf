'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Bug, Database, Home, Sparkles } from 'lucide-react';

const navigation = [
  {
    name: 'Accueil',
    href: '/',
    icon: Home,
  },
  {
    name: "<PERSON><PERSON><PERSON> d'Erreurs",
    href: '/error-analysis',
    icon: Bug,
  },
  {
    name: 'Génération SQL',
    href: '/sql-generation',
    icon: Database,
  },
];

export default function Navigation() {
  const pathname = usePathname();

  return (
    <nav className="relative">
      {/* Navigation avec couleurs corporate Leoni */}
      <div className="bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20 items-center">
            {/* Logo + Title */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30">
                  <Sparkles className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Leoni Agents
                </h1>
                <p className="text-xs text-[#ff8c42] font-medium">Intelligence Artificielle</p>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="hidden sm:flex sm:space-x-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      'group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105',
                      isActive
                        ? 'bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white shadow-lg shadow-[#ff7514]/25'
                        : 'text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md'
                    )}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <Icon
                      className={cn(
                        'w-5 h-5 mr-2 transition-all duration-300',
                        isActive ? 'text-white' : 'text-white/70 group-hover:text-[#ff8c42]'
                      )}
                    />
                    {item.name}
                    {isActive && (
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff7514] to-[#ff8c42] opacity-20 animate-pulse"></div>
                    )}
                  </Link>
                );
              })}
            </div>

            
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="sm:hidden px-4 pt-2 pb-3 space-y-1">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'block pl-3 pr-4 py-2 border-l-4 text-base font-medium',
                isActive
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'
              )}
              aria-current={isActive ? 'page' : undefined}
            >
              <div className="flex items-center">
                <Icon className="w-4 h-4 mr-3" />
                {item.name}
              </div>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
