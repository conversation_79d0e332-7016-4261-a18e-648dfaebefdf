16.07.2025 14:30:15 ERROR test_program_with_issues.c line: 9 - Variable 'uninitialized_var' used without initialization
16.07.2025 14:30:15 WARNING test_program_with_issues.c line: 12 - Memory allocated but never freed (potential memory leak)
16.07.2025 14:30:15 CRITICAL test_program_with_issues.c line: 15 - Buffer overflow detected in strcpy operation
16.07.2025 14:30:15 ERROR test_program_with_issues.c line: 18 - Use of uninitialized variable 'uninitialized_var'
16.07.2025 14:30:15 WARNING test_program_with_issues.c line: 25 - SQL query without error checking
16.07.2025 14:30:15 CRITICAL test_program_with_issues.c line: 29 - Null pointer dereference
16.07.2025 14:30:15 ERROR test_program_with_issues.c line: 33 - Array index out of bounds
16.07.2025 14:30:15 WARNING test_program_with_issues.c line: 39 - sprintf usage may cause buffer overflow
16.07.2025 14:30:15 ERROR test_program_with_issues.c line: 47 - Function returns uninitialized variable
16.07.2025 14:30:15 WARNING test_program_with_issues.c line: 53 - malloc return value not checked
16.07.2025 14:30:15 INFO Compilation completed with 6 errors and 4 warnings
