'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertCircle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'

export default function SqlGenerationError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Error in sql-generation page:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertCircle className="w-8 h-8 text-white" />
          </div>
          
          <h1 className="text-2xl font-bold text-[#002857] mb-4">
            Erreur de Génération SQL
          </h1>
          
          <p className="text-gray-600 mb-6">
            Une erreur s&apos;est produite lors de la génération SQL. 
            Veuillez réessayer ou retourner à l&apos;accueil.
          </p>
          
          <div className="space-y-3">
            <Button 
              onClick={reset}
              className="w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Réessayer la génération
            </Button>
            
            <Link href="/">
              <Button 
                variant="outline" 
                className="w-full border-[#ff7514] text-[#ff7514] hover:bg-[#ff7514] hover:text-white"
              >
                <Home className="w-4 h-4 mr-2" />
                Retour à l&apos;accueil
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
