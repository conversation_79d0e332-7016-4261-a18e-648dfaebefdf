(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeCodeContext": ()=>analyzeCodeContext,
    "cn": ()=>cn,
    "extractLineFromProgram": ()=>extractLineFromProgram,
    "formatDate": ()=>formatDate,
    "parseErrorFile": ()=>parseErrorFile
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).format(date);
}
function parseErrorFile(content) {
    const lines = content.split('\n').filter((line)=>line.trim());
    return lines.map((line, index)=>{
        // Pattern principal pour les logs CAOFORS
        const caoMatch = line.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);
        if (caoMatch) {
            const [, timestamp, level, message] = caoMatch;
            const taskMatch = message.match(/task \[([^\]]+)\]/);
            // Patterns étendus pour extraire les numéros de ligne
            const linePatterns = [
                /(?:line|ligne)[:\s]+(\d+)/i,
                /\[.*(?:line|ligne)[:\s]+(\d+)/i,
                /caofors\.ec line: (\d+)/,
                /at line (\d+)/i,
                /error on line (\d+)/i,
                /(\d+):\d+:/,
                /line (\d+) column \d+/i,
                /\((\d+),\d+\)/,
                /:\s*(\d+)\s*:/,
                /ligne\s+(\d+)/i,
                /row\s+(\d+)/i,
                /position\s+(\d+)/i // position 123
            ];
            let lineNumber;
            for (const pattern of linePatterns){
                const match = message.match(pattern);
                if (match) {
                    lineNumber = parseInt(match[1]);
                    break;
                }
            }
            // Extraction du nom de fichier
            const filePatterns = [
                /([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z]+)/,
                /in file ([^\s]+)/i,
                /file "([^"]+)"/i,
                /([^\s]+\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions
            ];
            let fileName;
            for (const pattern of filePatterns){
                const match = message.match(pattern);
                if (match) {
                    fileName = match[1];
                    break;
                }
            }
            // Détection du type d'erreur
            const errorType = detectErrorType(message);
            // Détection de la sévérité
            const severity = detectSeverity(level, message);
            return {
                timestamp,
                level,
                message,
                task: taskMatch ? taskMatch[1] : undefined,
                lineNumber,
                errorType,
                severity,
                fileName
            };
        }
        // Patterns pour d'autres formats de logs
        const genericPatterns = [
            // Format standard: ERROR: message at line 123
            /^(ERROR|WARNING|INFO|DEBUG):\s*(.+?)(?:\s+at\s+line\s+(\d+))?$/i,
            // Format avec timestamp ISO
            /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,
            // Format simple: level message
            /^(\w+):\s*(.+)$/
        ];
        for (const pattern of genericPatterns){
            const match = line.match(pattern);
            if (match) {
                var _match_;
                const level = match[1] || match[2] || 'UNKNOWN';
                const message = match[2] || match[3] || match[1] || line;
                const lineNumber = match[3] ? parseInt(match[3]) : undefined;
                return {
                    timestamp: ((_match_ = match[1]) === null || _match_ === void 0 ? void 0 : _match_.includes('T')) ? match[1] : '',
                    level,
                    message,
                    lineNumber,
                    errorType: detectErrorType(message),
                    severity: detectSeverity(level, message)
                };
            }
        }
        // Fallback pour les lignes non reconnues
        return {
            timestamp: '',
            level: 'UNKNOWN',
            message: line,
            lineNumber: index + 1,
            errorType: 'UNKNOWN',
            severity: 'LOW'
        };
    });
}
function detectErrorType(message) {
    const errorPatterns = [
        {
            pattern: /variable.*not.*(?:initialized|declared|defined)/i,
            type: 'VARIABLE_NOT_INITIALIZED'
        },
        {
            pattern: /undefined.*variable/i,
            type: 'UNDEFINED_VARIABLE'
        },
        {
            pattern: /syntax.*error/i,
            type: 'SYNTAX_ERROR'
        },
        {
            pattern: /compilation.*error/i,
            type: 'COMPILATION_ERROR'
        },
        {
            pattern: /runtime.*error/i,
            type: 'RUNTIME_ERROR'
        },
        {
            pattern: /null.*pointer/i,
            type: 'NULL_POINTER'
        },
        {
            pattern: /memory.*leak/i,
            type: 'MEMORY_LEAK'
        },
        {
            pattern: /buffer.*overflow/i,
            type: 'BUFFER_OVERFLOW'
        },
        {
            pattern: /division.*by.*zero/i,
            type: 'DIVISION_BY_ZERO'
        },
        {
            pattern: /file.*not.*found/i,
            type: 'FILE_NOT_FOUND'
        },
        {
            pattern: /permission.*denied/i,
            type: 'PERMISSION_DENIED'
        },
        {
            pattern: /connection.*failed/i,
            type: 'CONNECTION_ERROR'
        },
        {
            pattern: /timeout/i,
            type: 'TIMEOUT_ERROR'
        },
        {
            pattern: /locked.*already.*runs/i,
            type: 'RESOURCE_LOCKED'
        },
        {
            pattern: /sql.*error/i,
            type: 'SQL_ERROR'
        },
        {
            pattern: /database.*error/i,
            type: 'DATABASE_ERROR'
        },
        {
            pattern: /assertion.*failed/i,
            type: 'ASSERTION_FAILED'
        },
        {
            pattern: /stack.*overflow/i,
            type: 'STACK_OVERFLOW'
        },
        {
            pattern: /out.*of.*memory/i,
            type: 'OUT_OF_MEMORY'
        },
        {
            pattern: /invalid.*argument/i,
            type: 'INVALID_ARGUMENT'
        },
        {
            pattern: /type.*mismatch/i,
            type: 'TYPE_MISMATCH'
        }
    ];
    for (const { pattern, type } of errorPatterns){
        if (pattern.test(message)) {
            return type;
        }
    }
    return 'GENERAL_ERROR';
}
function detectSeverity(level, message) {
    // Sévérité basée sur le niveau
    const levelSeverity = {
        'DEBUG': 'LOW',
        'INFO': 'LOW',
        'WARNING': 'MEDIUM',
        'WARN': 'MEDIUM',
        'ERROR': 'HIGH',
        'FATAL': 'CRITICAL',
        'CRITICAL': 'CRITICAL'
    };
    let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';
    // Ajustement basé sur le contenu du message
    const criticalPatterns = [
        /crash/i,
        /fatal/i,
        /critical/i,
        /system.*failure/i,
        /memory.*corruption/i,
        /security.*breach/i
    ];
    const highPatterns = [
        /error/i,
        /exception/i,
        /failed/i,
        /abort/i,
        /null.*pointer/i,
        /buffer.*overflow/i,
        /stack.*overflow/i
    ];
    const lowPatterns = [
        /warning/i,
        /info/i,
        /debug/i,
        /notice/i
    ];
    if (criticalPatterns.some((pattern)=>pattern.test(message))) {
        severity = 'CRITICAL';
    } else if (highPatterns.some((pattern)=>pattern.test(message))) {
        severity = severity === 'LOW' ? 'HIGH' : severity;
    } else if (lowPatterns.some((pattern)=>pattern.test(message))) {
        severity = severity === 'HIGH' ? 'MEDIUM' : severity;
    }
    return severity;
}
function extractLineFromProgram(programContent, lineNumber) {
    let context = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;
    const lines = programContent.split('\n');
    const targetLine = lines[lineNumber - 1] || '';
    const startLine = Math.max(0, lineNumber - context - 1);
    const endLine = Math.min(lines.length, lineNumber + context);
    const contextLines = [];
    for(let i = startLine; i < endLine; i++){
        contextLines.push({
            number: i + 1,
            content: lines[i] || '',
            isTarget: i === lineNumber - 1
        });
    }
    // Analyse contextuelle avancée
    const analysis = analyzeCodeContext(lines, lineNumber, context);
    return {
        targetLine,
        contextLines,
        analysis
    };
}
function analyzeCodeContext(lines, lineNumber) {
    let context = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5;
    const startLine = Math.max(0, lineNumber - context - 1);
    const endLine = Math.min(lines.length, lineNumber + context);
    const contextCode = lines.slice(startLine, endLine).join('\n');
    const targetLine = lines[lineNumber - 1] || '';
    // Extraction des variables
    const variables = extractVariables(contextCode);
    // Extraction des fonctions
    const functions = extractFunctions(contextCode);
    // Détection des problèmes potentiels
    const potentialIssues = detectPotentialIssues(targetLine, contextCode);
    // Génération de suggestions
    const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);
    return {
        variables,
        functions,
        potentialIssues,
        suggestions
    };
}
function extractVariables(code) {
    const variables = new Set();
    // Patterns pour différents langages
    const patterns = [
        // C/C++: type var = value; ou type var;
        /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Variables avec déclaration explicite
        /(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Assignations
        /([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g,
        // Paramètres de fonction
        /\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\)/g
    ];
    patterns.forEach((pattern)=>{
        let match;
        while((match = pattern.exec(code)) !== null){
            if (match[1]) {
                // Séparer les variables multiples (pour les paramètres)
                match[1].split(',').forEach((v)=>{
                    const varName = v.trim().split(/\s+/).pop();
                    if (varName && varName.length > 1) {
                        variables.add(varName);
                    }
                });
            }
        }
    });
    return Array.from(variables);
}
function extractFunctions(code) {
    const functions = new Set();
    const patterns = [
        // Définitions de fonction C/C++
        /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
        // Appels de fonction
        /([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
        // Fonctions JavaScript/TypeScript
        /function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Méthodes
        /\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g
    ];
    patterns.forEach((pattern)=>{
        let match;
        while((match = pattern.exec(code)) !== null){
            if (match[1] && match[1].length > 1) {
                functions.add(match[1]);
            }
        }
    });
    return Array.from(functions);
}
function detectPotentialIssues(targetLine, contextCode) {
    const issues = [];
    // Vérifications communes
    const checks = [
        {
            pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*$/,
            message: "Variable potentiellement non initialisée ou assignation incomplète"
        },
        {
            pattern: /\*\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(?![=])/,
            message: "Déréférencement de pointeur - vérifier si le pointeur est NULL"
        },
        {
            pattern: /\[\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\]/,
            message: "Accès tableau - vérifier les limites d'index"
        },
        {
            pattern: /\/\*.*\*\//,
            message: "Code commenté - peut indiquer du code problématique"
        },
        {
            pattern: /TODO|FIXME|HACK|BUG/i,
            message: "Commentaire indiquant un problème connu"
        },
        {
            pattern: /malloc|calloc|free/,
            message: "Gestion mémoire manuelle - vérifier les fuites mémoire"
        },
        {
            pattern: /strcpy|strcat|sprintf/,
            message: "Fonction potentiellement dangereuse - risque de buffer overflow"
        }
    ];
    checks.forEach((check)=>{
        if (check.pattern.test(targetLine)) {
            issues.push(check.message);
        }
    });
    // Vérifications contextuelles
    if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {
        issues.push("Requête SQL sans vérification d'erreur appropriée");
    }
    if (targetLine.includes('=') && !targetLine.includes(';')) {
        issues.push("Assignation sans point-virgule terminal");
    }
    return issues;
}
function generateSuggestions(targetLine, contextCode, issues) {
    const suggestions = [];
    // Suggestions basées sur les problèmes détectés
    if (issues.some((issue)=>issue.includes('non initialisée'))) {
        suggestions.push("Initialiser la variable avant utilisation");
        suggestions.push("Vérifier la déclaration de la variable");
    }
    if (issues.some((issue)=>issue.includes('pointeur'))) {
        suggestions.push("Ajouter une vérification NULL avant déréférencement");
        suggestions.push("Utiliser des pointeurs intelligents si possible");
    }
    if (issues.some((issue)=>issue.includes('tableau'))) {
        suggestions.push("Vérifier que l'index est dans les limites du tableau");
        suggestions.push("Utiliser des fonctions sécurisées pour l'accès aux tableaux");
    }
    if (targetLine.includes('EXEC SQL')) {
        suggestions.push("Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL");
        suggestions.push("Vérifier le code de retour SQL");
    }
    // Suggestions générales
    if (targetLine.trim().length === 0) {
        suggestions.push("Ligne vide - vérifier si du code manque");
    }
    if (targetLine.includes('//') || targetLine.includes('/*')) {
        suggestions.push("Code commenté - vérifier si c'est intentionnel");
    }
    return suggestions;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Navigation.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Navigation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bug.js [app-client] (ecmascript) <export default as Bug>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-client] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-client] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript) <export default as Sparkles>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const navigation = [
    {
        name: 'Accueil',
        href: '/',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"]
    },
    {
        name: "Analyse d'Erreurs",
        href: '/error-analysis',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__["Bug"]
    },
    {
        name: 'Génération SQL',
        href: '/sql-generation',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"]
    }
];
function Navigation() {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between h-20 items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"], {
                                                    className: "w-7 h-7 text-white"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/Navigation.tsx",
                                                    lineNumber: 39,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 38,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 41,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 37,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-2xl font-bold text-white",
                                                children: "Leoni Agents"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 44,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-[#ff8c42] font-medium",
                                                children: "Intelligence Artificielle"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 47,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 43,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 36,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden sm:flex sm:space-x-2",
                                children: navigation.map((item)=>{
                                    const Icon = item.icon;
                                    const isActive = pathname === item.href;
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: item.href,
                                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105', isActive ? 'bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white shadow-lg shadow-[#ff7514]/25' : 'text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md'),
                                        "aria-current": isActive ? 'page' : undefined,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('w-5 h-5 mr-2 transition-all duration-300', isActive ? 'text-white' : 'text-white/70 group-hover:text-[#ff8c42]')
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 69,
                                                columnNumber: 21
                                            }, this),
                                            item.name,
                                            isActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff7514] to-[#ff8c42] opacity-20 animate-pulse"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Navigation.tsx",
                                                lineNumber: 77,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, item.name, true, {
                                        fileName: "[project]/src/components/Navigation.tsx",
                                        lineNumber: 58,
                                        columnNumber: 19
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/src/components/Navigation.tsx",
                                lineNumber: 52,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Navigation.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/Navigation.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Navigation.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sm:hidden px-4 pt-2 pb-3 space-y-1",
                children: navigation.map((item)=>{
                    const Icon = item.icon;
                    const isActive = pathname === item.href;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: item.href,
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('block pl-3 pr-4 py-2 border-l-4 text-base font-medium', isActive ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'),
                        "aria-current": isActive ? 'page' : undefined,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                    className: "w-4 h-4 mr-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Navigation.tsx",
                                    lineNumber: 108,
                                    columnNumber: 17
                                }, this),
                                item.name
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/Navigation.tsx",
                            lineNumber: 107,
                            columnNumber: 15
                        }, this)
                    }, item.name, false, {
                        fileName: "[project]/src/components/Navigation.tsx",
                        lineNumber: 96,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/src/components/Navigation.tsx",
                lineNumber: 90,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Navigation.tsx",
        lineNumber: 30,
        columnNumber: 5
    }, this);
}
_s(Navigation, "xbyQPtUVMO7MNj7WjJlpdWqRcTo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = Navigation;
var _c;
__turbopack_context__.k.register(_c, "Navigation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_f9dc2478._.js.map