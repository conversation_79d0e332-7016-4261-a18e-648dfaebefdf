{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  // Interface pour les props du composant Textarea\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  // Interface pour les props du composant Input\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/app/error-analysis/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Input } from '@/components/ui/input';\nimport { ErrorAnalysisResult, ProgramFile } from '@/types';\nimport { FileText, AlertCircle, CheckCircle, Clock, Bug, FileX } from 'lucide-react';\nimport { processFile, getAllSupportedTypes, getSupportedTypesDescription, isFileTypeSupported } from '@/lib/fileUtils';\n\nexport default function ErrorAnalysisPage() {\n  const [programFile, setProgramFile] = useState<ProgramFile>({ name: '', content: '', type: 'program' });\n  const [errorFile, setErrorFile] = useState<ProgramFile>({ name: '', content: '', type: 'error' });\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [result, setResult] = useState<ErrorAnalysisResult | null>(null);\n  const [error, setError] = useState<string>('');\n\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, fileType: 'program' | 'error') => {\n    const file = event.target.files?.[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const content = e.target?.result as string;\n        if (fileType === 'program') {\n          setProgramFile({ name: file.name, content, type: 'program' });\n        } else {\n          setErrorFile({ name: file.name, content, type: 'error' });\n        }\n      };\n      reader.readAsText(file);\n    }\n  };\n\n  const handleAnalyze = async () => {\n    if (!programFile.content) {\n      setError('Veuillez fournir au moins le fichier programme');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    setError('');\n    setResult(null);\n\n    try {\n      const endpoint = errorFile.content ? '/api/error-analysis' : '/api/error-analysis/static';\n      const body = errorFile.content ?\n        { programFile, errorFile } :\n        { programFile };\n\n      const response = await fetch(endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(body),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Erreur lors de l\\'analyse');\n      }\n\n      setResult(data.data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Une erreur est survenue lors de l\\'analyse');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'CRITICAL': return 'text-red-600 bg-red-50';\n      case 'HIGH': return 'text-orange-600 bg-orange-50';\n      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50';\n      case 'LOW': return 'text-green-600 bg-green-50';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n          Analyse d'Erreurs\n        </h1>\n        <p className=\"text-gray-600\">\n          Analysez vos fichiers de programme et d'erreur pour obtenir des diagnostics détaillés.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <FileText className=\"w-5 h-5 mr-2\" />\n                Fichier Programme\n              </CardTitle>\n              <CardDescription>\n                Téléchargez ou collez le contenu de votre fichier programme\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <Input\n                  type=\"file\"\n                  accept=\".txt,.c,.cpp,.ec,.log\"\n                  onChange={(e) => handleFileUpload(e, 'program')}\n                  className=\"mb-2\"\n                />\n                {programFile.name && (\n                  <p className=\"text-sm text-green-600\">✓ {programFile.name}</p>\n                )}\n              </div>\n              <Textarea\n                placeholder=\"Ou collez le contenu du fichier programme ici...\"\n                value={programFile.content}\n                onChange={(e) => setProgramFile({ ...programFile, content: e.target.value })}\n                className=\"min-h-[200px] font-mono text-sm\"\n              />\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <AlertCircle className=\"w-5 h-5 mr-2\" />\n                Fichier d'Erreur\n              </CardTitle>\n              <CardDescription>\n                Téléchargez ou collez le contenu de votre fichier d'erreur (optionnel - l'analyse statique sera effectuée si aucun fichier d'erreur n'est fourni)\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div>\n                <Input\n                  type=\"file\"\n                  accept=\".txt,.error,.log\"\n                  onChange={(e) => handleFileUpload(e, 'error')}\n                  className=\"mb-2\"\n                />\n                {errorFile.name && (\n                  <p className=\"text-sm text-green-600\">✓ {errorFile.name}</p>\n                )}\n              </div>\n              <Textarea\n                placeholder=\"Ou collez le contenu du fichier d'erreur ici... (Laissez vide pour une analyse statique du code uniquement)\"\n                value={errorFile.content}\n                onChange={(e) => setErrorFile({ ...errorFile, content: e.target.value })}\n                className=\"min-h-[200px] font-mono text-sm\"\n              />\n            </CardContent>\n          </Card>\n\n\n\n          <Button\n            onClick={handleAnalyze}\n            disabled={isAnalyzing || !programFile.content || !errorFile.content}\n            className=\"w-full\"\n            size=\"lg\"\n          >\n            {isAnalyzing ? (\n              <>\n                <Clock className=\"w-5 h-5 mr-2 animate-spin\" />\n                Analyse en cours...\n              </>\n            ) : (\n              <>\n                <Bug className=\"w-5 h-5 mr-2\" />\n                {errorFile.content ? 'Analyser les Erreurs' : 'Analyse Statique du Code'}\n              </>\n            )}\n          </Button>\n\n          {/* Indicateur du type d'analyse */}\n          <div className=\"text-center text-sm text-gray-600 mt-2\">\n            {errorFile.content ? (\n              <span className=\"flex items-center justify-center\">\n                <AlertCircle className=\"w-4 h-4 mr-1\" />\n                Analyse complète avec fichier d'erreur\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center\">\n                <FileText className=\"w-4 h-4 mr-1\" />\n                Analyse statique du code uniquement\n              </span>\n            )}\n          </div>\n\n          {error && (\n            <div className=\"bg-[#ff7514]/5 border border-[#ff7514]/20 rounded-lg p-4\">\n              <p className=\"text-[#ff7514]\">{error}</p>\n            </div>\n          )}\n        </div>\n\n        <div>\n          {result && (\n            <Card className=\"mt-8\">\n              <CardHeader className=\"bg-[#002857] text-white\">\n                <CardTitle className=\"flex items-center\">\n                  <CheckCircle className=\"w-5 h-5 mr-2\" />\n                  Résultats de l'Analyse\n                </CardTitle>\n                <CardDescription className=\"text-blue-100\">\n                  Analyse terminée le {new Date(result.timestamp).toLocaleString('fr-FR')}\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                {/* Summary */}\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Résumé</h3>\n                  <p className=\"text-gray-700\">{result.summary}</p>\n                </div>\n\n                {/* Errors */}\n                {result.errors.length > 0 && (\n                  <div>\n                    <h3 className=\"font-semibold mb-3\">Erreurs Détectées ({result.errors.length})</h3>\n                    <div className=\"space-y-4\">\n                      {result.errors.map((error, index) => (\n                        <div key={index} className=\"border rounded-lg p-4\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <h4 className=\"font-medium\">{error.errorType}</h4>\n                            <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(error.severity)}`}>\n                              {error.severity}\n                            </span>\n                          </div>\n                          <p className=\"text-sm text-gray-600 mb-2\">\n                            <strong>Emplacement:</strong> {error.location}\n                            {error.lineNumber && (\n                              <span className=\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs\">\n                                Ligne {error.lineNumber}\n                              </span>\n                            )}\n                            {error.fileName && (\n                              <span className=\"ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs\">\n                                {error.fileName}\n                              </span>\n                            )}\n                          </p>\n                          <p className=\"text-sm mb-3\">{error.description}</p>\n\n                          {/* Contexte de code */}\n                          {error.codeContext && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-2\">Contexte du code:</p>\n                              <div className=\"bg-gray-900 text-gray-100 p-3 rounded text-xs font-mono overflow-x-auto\">\n                                {error.codeContext.contextLines.map((line) => (\n                                  <div\n                                    key={line.number}\n                                    className={`flex ${line.isTarget ? 'bg-red-900/50' : ''}`}\n                                  >\n                                    <span className=\"text-gray-400 mr-3 w-8 text-right\">\n                                      {line.number}\n                                    </span>\n                                    <span className={line.isTarget ? 'text-red-300' : ''}>\n                                      {line.content || ' '}\n                                    </span>\n                                  </div>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n                          \n                          {error.possibleCauses.length > 0 && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-1\">Causes possibles:</p>\n                              <ul className=\"text-sm text-gray-600 list-disc list-inside\">\n                                {error.possibleCauses.map((cause, i) => (\n                                  <li key={i}>{cause}</li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n                          \n                          {error.solutions.length > 0 && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-1\">Solutions recommandées:</p>\n                              <ul className=\"text-sm text-gray-600 list-disc list-inside\">\n                                {error.solutions.map((solution, i) => (\n                                  <li key={i}>{solution}</li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n\n                          {/* Suggestions de correction automatique */}\n                          {error.autoFixSuggestions && error.autoFixSuggestions.length > 0 && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-2\">Corrections automatiques suggérées:</p>\n                              <div className=\"space-y-2\">\n                                {error.autoFixSuggestions.map((suggestion, i) => (\n                                  <div key={i} className=\"bg-green-50 border border-green-200 rounded p-3\">\n                                    <div className=\"flex items-center justify-between mb-1\">\n                                      <span className=\"text-sm font-medium text-green-800\">\n                                        {suggestion.type}\n                                      </span>\n                                      <span className={`px-2 py-1 rounded text-xs ${\n                                        suggestion.confidence === 'HIGH' ? 'bg-green-100 text-green-800' :\n                                        suggestion.confidence === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :\n                                        'bg-red-100 text-red-800'\n                                      }`}>\n                                        {suggestion.confidence}\n                                      </span>\n                                    </div>\n                                    <p className=\"text-sm text-green-700 mb-2\">{suggestion.description}</p>\n                                    {suggestion.suggestedCode && (\n                                      <div className=\"bg-green-900 text-green-100 p-2 rounded text-xs font-mono\">\n                                        {suggestion.suggestedCode}\n                                      </div>\n                                    )}\n                                  </div>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Erreurs liées */}\n                          {error.relatedErrors && error.relatedErrors.length > 0 && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-1\">Erreurs liées:</p>\n                              <div className=\"flex flex-wrap gap-1\">\n                                {error.relatedErrors.map((relatedIndex) => (\n                                  <span key={relatedIndex} className=\"px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs\">\n                                    Erreur #{relatedIndex + 1}\n                                  </span>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Analyse contextuelle */}\n                          {error.codeContext?.analysis && (\n                            <div className=\"mb-3\">\n                              <p className=\"text-sm font-medium mb-2\">Analyse contextuelle:</p>\n                              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 text-xs\">\n                                {error.codeContext.analysis.variables.length > 0 && (\n                                  <div>\n                                    <p className=\"font-medium mb-1\">Variables détectées:</p>\n                                    <div className=\"flex flex-wrap gap-1\">\n                                      {error.codeContext.analysis.variables.map((variable, i) => (\n                                        <span key={i} className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded\">\n                                          {variable}\n                                        </span>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                                {error.codeContext.analysis.functions.length > 0 && (\n                                  <div>\n                                    <p className=\"font-medium mb-1\">Fonctions détectées:</p>\n                                    <div className=\"flex flex-wrap gap-1\">\n                                      {error.codeContext.analysis.functions.map((func, i) => (\n                                        <span key={i} className=\"px-2 py-1 bg-purple-100 text-purple-800 rounded\">\n                                          {func}()\n                                        </span>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                              {error.codeContext.analysis.potentialIssues.length > 0 && (\n                                <div className=\"mt-2\">\n                                  <p className=\"font-medium mb-1\">Problèmes potentiels:</p>\n                                  <ul className=\"text-xs text-gray-600 list-disc list-inside\">\n                                    {error.codeContext.analysis.potentialIssues.map((issue, i) => (\n                                      <li key={i}>{issue}</li>\n                                    ))}\n                                  </ul>\n                                </div>\n                              )}\n                            </div>\n                          )}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Recommendations */}\n                {result.recommendations.length > 0 && (\n                  <div>\n                    <h3 className=\"font-semibold mb-2\">Recommandations Générales</h3>\n                    <ul className=\"space-y-1\">\n                      {result.recommendations.map((recommendation, index) => (\n                        <li key={index} className=\"text-sm text-gray-700 flex items-start\">\n                          <span className=\"text-blue-500 mr-2\">•</span>\n                          {recommendation}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          )}\n\n          {!result && !isAnalyzing && (\n            <Card className=\"bg-white/60 backdrop-blur-xl border-0 shadow-xl rounded-3xl\">\n              <CardContent className=\"text-center py-16\">\n                <div className=\"relative mb-6\">\n                  <div className=\"w-24 h-24 mx-auto bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center\">\n                    <Bug className=\"w-12 h-12 text-gray-400\" />\n                  </div>\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse\"></div>\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-700 mb-3\">Prêt pour l'Analyse</h3>\n                <p className=\"text-gray-500 max-w-md mx-auto leading-relaxed\">\n                  Les résultats de l'analyse apparaîtront ici une fois que vous aurez téléchargé vos fichiers et lancé l'analyse.\n                </p>\n                <div className=\"mt-6 flex justify-center space-x-4 text-sm text-gray-400\">\n                  <span>🔍 Détection automatique</span>\n                  <span>📍 Localisation précise</span>\n                  <span>💡 Solutions expertes</span>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAAE,MAAM;QAAI,SAAS;QAAI,MAAM;IAAU;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAAE,MAAM;QAAI,SAAS;QAAI,MAAM;IAAQ;IAC/F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAG3C,MAAM,mBAAmB,CAAC,OAA4C;YACvD;QAAb,MAAM,QAAO,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;oBACC;gBAAhB,MAAM,WAAU,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;gBAChC,IAAI,aAAa,WAAW;oBAC1B,eAAe;wBAAE,MAAM,KAAK,IAAI;wBAAE;wBAAS,MAAM;oBAAU;gBAC7D,OAAO;oBACL,aAAa;wBAAE,MAAM,KAAK,IAAI;wBAAE;wBAAS,MAAM;oBAAQ;gBACzD;YACF;YACA,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,SAAS;YACT;QACF;QAEA,eAAe;QACf,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,UAAU,OAAO,GAAG,wBAAwB;YAC7D,MAAM,OAAO,UAAU,OAAO,GAC5B;gBAAE;gBAAa;YAAU,IACzB;gBAAE;YAAY;YAEhB,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,UAAU,KAAK,IAAI;QACrB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAK/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,QAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;;;;;oDAEX,YAAY,IAAI,kBACf,6LAAC;wDAAE,WAAU;;4DAAyB;4DAAG,YAAY,IAAI;;;;;;;;;;;;;0DAG7D,6LAAC,uIAAA,CAAA,WAAQ;gDACP,aAAY;gDACZ,OAAO,YAAY,OAAO;gDAC1B,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC1E,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,QAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,GAAG;wDACrC,WAAU;;;;;;oDAEX,UAAU,IAAI,kBACb,6LAAC;wDAAE,WAAU;;4DAAyB;4DAAG,UAAU,IAAI;;;;;;;;;;;;;0DAG3D,6LAAC,uIAAA,CAAA,WAAQ;gDACP,aAAY;gDACZ,OAAO,UAAU,OAAO;gDACxB,UAAU,CAAC,IAAM,aAAa;wDAAE,GAAG,SAAS;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtE,WAAU;;;;;;;;;;;;;;;;;;0CAOhB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,eAAe,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,OAAO;gCACnE,WAAU;gCACV,MAAK;0CAEJ,4BACC;;sDACE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA8B;;iEAIjD;;sDACE,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCACd,UAAU,OAAO,GAAG,yBAAyB;;;;;;;;0CAMpD,6LAAC;gCAAI,WAAU;0CACZ,UAAU,OAAO,iBAChB,6LAAC;oCAAK,WAAU;;sDACd,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;yDAI1C,6LAAC;oCAAK,WAAU;;sDACd,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;4BAM1C,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;;;;;;kCAKrC,6LAAC;;4BACE,wBACC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;;oDAAgB;oDACpB,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;kDAGnE,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,6LAAC;wDAAE,WAAU;kEAAiB,OAAO,OAAO;;;;;;;;;;;;4CAI7C,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;4DAAqB;4DAAoB,OAAO,MAAM,CAAC,MAAM;4DAAC;;;;;;;kEAC5E,6LAAC;wDAAI,WAAU;kEACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;gEAiHtB;iFAhHH,6LAAC;gEAAgB,WAAU;;kFACzB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAAe,MAAM,SAAS;;;;;;0FAC5C,6LAAC;gFAAK,WAAW,AAAC,yCAAyE,OAAjC,iBAAiB,MAAM,QAAQ;0FACtF,MAAM,QAAQ;;;;;;;;;;;;kFAGnB,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;0FAAO;;;;;;4EAAqB;4EAAE,MAAM,QAAQ;4EAC5C,MAAM,UAAU,kBACf,6LAAC;gFAAK,WAAU;;oFAA2D;oFAClE,MAAM,UAAU;;;;;;;4EAG1B,MAAM,QAAQ,kBACb,6LAAC;gFAAK,WAAU;0FACb,MAAM,QAAQ;;;;;;;;;;;;kFAIrB,6LAAC;wEAAE,WAAU;kFAAgB,MAAM,WAAW;;;;;;oEAG7C,MAAM,WAAW,kBAChB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,qBACnC,6LAAC;wFAEC,WAAW,AAAC,QAA4C,OAArC,KAAK,QAAQ,GAAG,kBAAkB;;0GAErD,6LAAC;gGAAK,WAAU;0GACb,KAAK,MAAM;;;;;;0GAEd,6LAAC;gGAAK,WAAW,KAAK,QAAQ,GAAG,iBAAiB;0GAC/C,KAAK,OAAO,IAAI;;;;;;;uFAPd,KAAK,MAAM;;;;;;;;;;;;;;;;oEAezB,MAAM,cAAc,CAAC,MAAM,GAAG,mBAC7B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAG,WAAU;0FACX,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,kBAChC,6LAAC;kGAAY;uFAAJ;;;;;;;;;;;;;;;;oEAMhB,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAG,WAAU;0FACX,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,kBAC9B,6LAAC;kGAAY;uFAAJ;;;;;;;;;;;;;;;;oEAOhB,MAAM,kBAAkB,IAAI,MAAM,kBAAkB,CAAC,MAAM,GAAG,mBAC7D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAC,YAAY,kBACzC,6LAAC;wFAAY,WAAU;;0GACrB,6LAAC;gGAAI,WAAU;;kHACb,6LAAC;wGAAK,WAAU;kHACb,WAAW,IAAI;;;;;;kHAElB,6LAAC;wGAAK,WAAW,AAAC,6BAIjB,OAHC,WAAW,UAAU,KAAK,SAAS,gCACnC,WAAW,UAAU,KAAK,WAAW,kCACrC;kHAEC,WAAW,UAAU;;;;;;;;;;;;0GAG1B,6LAAC;gGAAE,WAAU;0GAA+B,WAAW,WAAW;;;;;;4FACjE,WAAW,aAAa,kBACvB,6LAAC;gGAAI,WAAU;0GACZ,WAAW,aAAa;;;;;;;uFAhBrB;;;;;;;;;;;;;;;;oEA0BjB,MAAM,aAAa,IAAI,MAAM,aAAa,CAAC,MAAM,GAAG,mBACnD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,6BACxB,6LAAC;wFAAwB,WAAU;;4FAA0D;4FAClF,eAAe;;uFADf;;;;;;;;;;;;;;;;oEASlB,EAAA,qBAAA,MAAM,WAAW,cAAjB,yCAAA,mBAAmB,QAAQ,mBAC1B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAI,WAAU;;oFACZ,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,mBAC7C,6LAAC;;0GACC,6LAAC;gGAAE,WAAU;0GAAmB;;;;;;0GAChC,6LAAC;gGAAI,WAAU;0GACZ,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,kBACnD,6LAAC;wGAAa,WAAU;kHACrB;uGADQ;;;;;;;;;;;;;;;;oFAOlB,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,mBAC7C,6LAAC;;0GACC,6LAAC;gGAAE,WAAU;0GAAmB;;;;;;0GAChC,6LAAC;gGAAI,WAAU;0GACZ,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC/C,6LAAC;wGAAa,WAAU;;4GACrB;4GAAK;;uGADG;;;;;;;;;;;;;;;;;;;;;;4EAQpB,MAAM,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,mBACnD,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAE,WAAU;kGAAmB;;;;;;kGAChC,6LAAC;wFAAG,WAAU;kGACX,MAAM,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,kBACtD,6LAAC;0GAAY;+FAAJ;;;;;;;;;;;;;;;;;;;;;;;+DAlJb;;;;;;;;;;;;;;;;;4CAgKjB,OAAO,eAAe,CAAC,MAAM,GAAG,mBAC/B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,6LAAC;wDAAG,WAAU;kEACX,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,gBAAgB,sBAC3C,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;oEACpC;;+DAFM;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAYtB,CAAC,UAAU,CAAC,6BACX,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAjawB;KAAA", "debugId": null}}]}