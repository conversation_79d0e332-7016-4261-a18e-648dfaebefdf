/**
 * Utilitaires pour gérer différents types de fichiers
 */

export interface FileProcessingResult {
  content: string;
  originalName: string;
  fileType: string;
  success: boolean;
  error?: string;
}

/**
 * Types de fichiers supportés
 */
export const SUPPORTED_FILE_TYPES = {
  // Fichiers texte
  TEXT: ['.txt', '.log', '.md', '.csv'],
  // Fichiers de code
  CODE: ['.c', '.cpp', '.ec', '.js', '.ts', '.py', '.java', '.php', '.html', '.css', '.sql'],
  // Fichiers de données
  DATA: ['.json', '.xml', '.yaml', '.yml'],
  // Fichiers Microsoft Office (nécessitent traitement spécial)
  OFFICE: ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
  // Fichiers PDF
  PDF: ['.pdf'],
  // Fichiers d'erreur spécifiques
  ERROR: ['.error', '.err', '.trace', '.dump']
};

/**
 * Obtient tous les types de fichiers supportés
 */
export function getAllSupportedTypes(): string[] {
  return [
    ...SUPPORTED_FILE_TYPES.TEXT,
    ...SUPPORTED_FILE_TYPES.CODE,
    ...SUPPORTED_FILE_TYPES.DATA,
    ...SUPPORTED_FILE_TYPES.OFFICE,
    ...SUPPORTED_FILE_TYPES.PDF,
    ...SUPPORTED_FILE_TYPES.ERROR
  ];
}

/**
 * Vérifie si un type de fichier est supporté
 */
export function isFileTypeSupported(fileName: string): boolean {
  const extension = getFileExtension(fileName);
  return getAllSupportedTypes().includes(extension);
}

/**
 * Obtient l'extension d'un fichier
 */
export function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot).toLowerCase() : '';
}

/**
 * Détermine le type de fichier
 */
export function getFileType(fileName: string): string {
  const extension = getFileExtension(fileName);
  
  if (SUPPORTED_FILE_TYPES.TEXT.includes(extension)) return 'text';
  if (SUPPORTED_FILE_TYPES.CODE.includes(extension)) return 'code';
  if (SUPPORTED_FILE_TYPES.DATA.includes(extension)) return 'data';
  if (SUPPORTED_FILE_TYPES.OFFICE.includes(extension)) return 'office';
  if (SUPPORTED_FILE_TYPES.PDF.includes(extension)) return 'pdf';
  if (SUPPORTED_FILE_TYPES.ERROR.includes(extension)) return 'error';
  
  return 'unknown';
}

/**
 * Traite un fichier selon son type
 */
export async function processFile(file: File): Promise<FileProcessingResult> {
  const fileType = getFileType(file.name);
  
  try {
    switch (fileType) {
      case 'text':
      case 'code':
      case 'data':
      case 'error':
        return await processTextFile(file);
      
      case 'office':
        return await processOfficeFile(file);
      
      case 'pdf':
        return await processPDFFile(file);
      
      default:
        // Essayer de lire comme fichier texte par défaut
        return await processTextFile(file);
    }
  } catch (error) {
    return {
      content: '',
      originalName: file.name,
      fileType,
      success: false,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

/**
 * Traite les fichiers texte standard
 */
async function processTextFile(file: File): Promise<FileProcessingResult> {
  return new Promise((resolve) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve({
        content,
        originalName: file.name,
        fileType: getFileType(file.name),
        success: true
      });
    };
    
    reader.onerror = () => {
      resolve({
        content: '',
        originalName: file.name,
        fileType: getFileType(file.name),
        success: false,
        error: 'Erreur lors de la lecture du fichier'
      });
    };
    
    reader.readAsText(file, 'UTF-8');
  });
}

/**
 * Traite les fichiers Office (Word, Excel, PowerPoint)
 * Note: Cette fonction nécessite des bibliothèques supplémentaires pour un traitement complet
 */
async function processOfficeFile(file: File): Promise<FileProcessingResult> {
  // Pour l'instant, on essaie de lire comme texte brut
  // Dans une implémentation complète, on utiliserait des bibliothèques comme:
  // - mammoth.js pour Word
  // - xlsx pour Excel
  // - etc.
  
  try {
    const arrayBuffer = await file.arrayBuffer();
    const decoder = new TextDecoder('utf-8', { fatal: false });
    const content = decoder.decode(arrayBuffer);
    
    // Nettoyer le contenu (supprimer les caractères de contrôle)
    const cleanContent = content.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ').trim();
    
    if (cleanContent.length < 50) {
      return {
        content: '',
        originalName: file.name,
        fileType: 'office',
        success: false,
        error: 'Fichier Office détecté. Pour un meilleur support, convertissez en .txt ou .pdf'
      };
    }
    
    return {
      content: cleanContent,
      originalName: file.name,
      fileType: 'office',
      success: true
    };
  } catch (error) {
    return {
      content: '',
      originalName: file.name,
      fileType: 'office',
      success: false,
      error: 'Impossible de lire le fichier Office. Essayez de le convertir en .txt'
    };
  }
}

/**
 * Traite les fichiers PDF
 * Note: Cette fonction nécessite pdf-parse ou une bibliothèque similaire pour un traitement complet
 */
async function processPDFFile(file: File): Promise<FileProcessingResult> {
  // Pour l'instant, on retourne une erreur explicative
  // Dans une implémentation complète, on utiliserait pdf-parse ou pdf.js
  
  return {
    content: '',
    originalName: file.name,
    fileType: 'pdf',
    success: false,
    error: 'Fichiers PDF non supportés actuellement. Convertissez en .txt pour l\'analyse'
  };
}

/**
 * Obtient une description des types de fichiers supportés
 */
export function getSupportedTypesDescription(): string {
  return `
Types de fichiers supportés :
• Texte : ${SUPPORTED_FILE_TYPES.TEXT.join(', ')}
• Code : ${SUPPORTED_FILE_TYPES.CODE.join(', ')}
• Données : ${SUPPORTED_FILE_TYPES.DATA.join(', ')}
• Erreurs : ${SUPPORTED_FILE_TYPES.ERROR.join(', ')}
• Office : ${SUPPORTED_FILE_TYPES.OFFICE.join(', ')} (support limité)
• PDF : ${SUPPORTED_FILE_TYPES.PDF.join(', ')} (non supporté actuellement)
  `.trim();
}
