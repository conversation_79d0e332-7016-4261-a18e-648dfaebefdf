{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n  }).format(date)\n}\n\nexport interface ParsedError {\n  timestamp: string;\n  level: string;\n  message: string;\n  task?: string;\n  lineNumber?: number;\n  errorType?: string;\n  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  context?: string;\n  fileName?: string;\n}\n\nexport function parseErrorFile(content: string): ParsedError[] {\n  const lines = content.split('\\n').filter(line => line.trim());\n  return lines.map((line, index) => {\n    // Pattern principal pour les logs CAOFORS\n    const caoMatch = line.match(/^(\\d{2}\\.\\d{2}\\.\\d{4} \\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/);\n\n    if (caoMatch) {\n      const [, timestamp, level, message] = caoMatch;\n      const taskMatch = message.match(/task \\[([^\\]]+)\\]/);\n\n      // Patterns étendus pour extraire les numéros de ligne\n      const linePatterns = [\n        /(?:line|ligne)[:\\s]+(\\d+)/i,                    // line: 123, ligne: 123\n        /\\[.*(?:line|ligne)[:\\s]+(\\d+)/i,                // [something line: 123]\n        /caofors\\.ec line: (\\d+)/,                       // caofors.ec line: 123\n        /at line (\\d+)/i,                                // at line 123\n        /error on line (\\d+)/i,                          // error on line 123\n        /(\\d+):\\d+:/,                                    // 123:45: (format file:line:col)\n        /line (\\d+) column \\d+/i,                        // line 123 column 45\n        /\\((\\d+),\\d+\\)/,                                 // (123,45) format\n        /:\\s*(\\d+)\\s*:/,                                 // : 123 :\n        /ligne\\s+(\\d+)/i,                                // ligne 123\n        /row\\s+(\\d+)/i,                                  // row 123\n        /position\\s+(\\d+)/i                              // position 123\n      ];\n\n      let lineNumber: number | undefined;\n      for (const pattern of linePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          lineNumber = parseInt(match[1]);\n          break;\n        }\n      }\n\n      // Extraction du nom de fichier\n      const filePatterns = [\n        /([a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z]+)/,          // filename.ext\n        /in file ([^\\s]+)/i,                             // in file filename\n        /file \"([^\"]+)\"/i,                               // file \"filename\"\n        /([^\\s]+\\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions\n      ];\n\n      let fileName: string | undefined;\n      for (const pattern of filePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          fileName = match[1];\n          break;\n        }\n      }\n\n      // Détection du type d'erreur\n      const errorType = detectErrorType(message);\n\n      // Détection de la sévérité\n      const severity = detectSeverity(level, message);\n\n      return {\n        timestamp,\n        level,\n        message,\n        task: taskMatch ? taskMatch[1] : undefined,\n        lineNumber,\n        errorType,\n        severity,\n        fileName\n      };\n    }\n\n    // Patterns pour d'autres formats de logs\n    const genericPatterns = [\n      // Format standard: ERROR: message at line 123\n      /^(ERROR|WARNING|INFO|DEBUG):\\s*(.+?)(?:\\s+at\\s+line\\s+(\\d+))?$/i,\n      // Format avec timestamp ISO\n      /^(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/,\n      // Format simple: level message\n      /^(\\w+):\\s*(.+)$/\n    ];\n\n    for (const pattern of genericPatterns) {\n      const match = line.match(pattern);\n      if (match) {\n        const level = match[1] || match[2] || 'UNKNOWN';\n        const message = match[2] || match[3] || match[1] || line;\n        const lineNumber = match[3] ? parseInt(match[3]) : undefined;\n\n        return {\n          timestamp: match[1]?.includes('T') ? match[1] : '',\n          level,\n          message,\n          lineNumber,\n          errorType: detectErrorType(message),\n          severity: detectSeverity(level, message)\n        };\n      }\n    }\n\n    // Fallback pour les lignes non reconnues\n    return {\n      timestamp: '',\n      level: 'UNKNOWN',\n      message: line,\n      lineNumber: index + 1,\n      errorType: 'UNKNOWN',\n      severity: 'LOW'\n    };\n  });\n}\n\nfunction detectErrorType(message: string): string {\n  const errorPatterns = [\n    { pattern: /variable.*not.*(?:initialized|declared|defined)/i, type: 'VARIABLE_NOT_INITIALIZED' },\n    { pattern: /undefined.*variable/i, type: 'UNDEFINED_VARIABLE' },\n    { pattern: /syntax.*error/i, type: 'SYNTAX_ERROR' },\n    { pattern: /compilation.*error/i, type: 'COMPILATION_ERROR' },\n    { pattern: /runtime.*error/i, type: 'RUNTIME_ERROR' },\n    { pattern: /null.*pointer/i, type: 'NULL_POINTER' },\n    { pattern: /memory.*leak/i, type: 'MEMORY_LEAK' },\n    { pattern: /buffer.*overflow/i, type: 'BUFFER_OVERFLOW' },\n    { pattern: /division.*by.*zero/i, type: 'DIVISION_BY_ZERO' },\n    { pattern: /file.*not.*found/i, type: 'FILE_NOT_FOUND' },\n    { pattern: /permission.*denied/i, type: 'PERMISSION_DENIED' },\n    { pattern: /connection.*failed/i, type: 'CONNECTION_ERROR' },\n    { pattern: /timeout/i, type: 'TIMEOUT_ERROR' },\n    { pattern: /locked.*already.*runs/i, type: 'RESOURCE_LOCKED' },\n    { pattern: /sql.*error/i, type: 'SQL_ERROR' },\n    { pattern: /database.*error/i, type: 'DATABASE_ERROR' },\n    { pattern: /assertion.*failed/i, type: 'ASSERTION_FAILED' },\n    { pattern: /stack.*overflow/i, type: 'STACK_OVERFLOW' },\n    { pattern: /out.*of.*memory/i, type: 'OUT_OF_MEMORY' },\n    { pattern: /invalid.*argument/i, type: 'INVALID_ARGUMENT' },\n    { pattern: /type.*mismatch/i, type: 'TYPE_MISMATCH' }\n  ];\n\n  for (const { pattern, type } of errorPatterns) {\n    if (pattern.test(message)) {\n      return type;\n    }\n  }\n\n  return 'GENERAL_ERROR';\n}\n\nfunction detectSeverity(level: string, message: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {\n  // Sévérité basée sur le niveau\n  const levelSeverity: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {\n    'DEBUG': 'LOW',\n    'INFO': 'LOW',\n    'WARNING': 'MEDIUM',\n    'WARN': 'MEDIUM',\n    'ERROR': 'HIGH',\n    'FATAL': 'CRITICAL',\n    'CRITICAL': 'CRITICAL'\n  };\n\n  let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';\n\n  // Ajustement basé sur le contenu du message\n  const criticalPatterns = [\n    /crash/i, /fatal/i, /critical/i, /system.*failure/i,\n    /memory.*corruption/i, /security.*breach/i\n  ];\n\n  const highPatterns = [\n    /error/i, /exception/i, /failed/i, /abort/i,\n    /null.*pointer/i, /buffer.*overflow/i, /stack.*overflow/i\n  ];\n\n  const lowPatterns = [\n    /warning/i, /info/i, /debug/i, /notice/i\n  ];\n\n  if (criticalPatterns.some(pattern => pattern.test(message))) {\n    severity = 'CRITICAL';\n  } else if (highPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'LOW' ? 'HIGH' : severity;\n  } else if (lowPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'HIGH' ? 'MEDIUM' : severity;\n  }\n\n  return severity;\n}\n\nexport interface CodeContext {\n  targetLine: string;\n  contextLines: Array<{ number: number; content: string; isTarget: boolean }>;\n  analysis?: {\n    variables: string[];\n    functions: string[];\n    potentialIssues: string[];\n    suggestions: string[];\n  };\n}\n\nexport function extractLineFromProgram(programContent: string, lineNumber: number, context: number = 2): CodeContext {\n  const lines = programContent.split('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n\n  const contextLines = [];\n  for (let i = startLine; i < endLine; i++) {\n    contextLines.push({\n      number: i + 1,\n      content: lines[i] || '',\n      isTarget: i === lineNumber - 1\n    });\n  }\n\n  // Analyse contextuelle avancée\n  const analysis = analyzeCodeContext(lines, lineNumber, context);\n\n  return {\n    targetLine,\n    contextLines,\n    analysis\n  };\n}\n\nexport function analyzeCodeContext(lines: string[], lineNumber: number, context: number = 5): {\n  variables: string[];\n  functions: string[];\n  potentialIssues: string[];\n  suggestions: string[];\n} {\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n  const contextCode = lines.slice(startLine, endLine).join('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  // Extraction des variables\n  const variables = extractVariables(contextCode);\n\n  // Extraction des fonctions\n  const functions = extractFunctions(contextCode);\n\n  // Détection des problèmes potentiels\n  const potentialIssues = detectPotentialIssues(targetLine, contextCode);\n\n  // Génération de suggestions\n  const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);\n\n  return {\n    variables,\n    functions,\n    potentialIssues,\n    suggestions\n  };\n}\n\nfunction extractVariables(code: string): string[] {\n  const variables = new Set<string>();\n\n  // Patterns pour différents langages\n  const patterns = [\n    // C/C++: type var = value; ou type var;\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Variables avec déclaration explicite\n    /(?:var|let|const)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Assignations\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=/g,\n    // Paramètres de fonction\n    /\\(\\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\\s*\\)/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1]) {\n        // Séparer les variables multiples (pour les paramètres)\n        match[1].split(',').forEach(v => {\n          const varName = v.trim().split(/\\s+/).pop();\n          if (varName && varName.length > 1) {\n            variables.add(varName);\n          }\n        });\n      }\n    }\n  });\n\n  return Array.from(variables);\n}\n\nfunction extractFunctions(code: string): string[] {\n  const functions = new Set<string>();\n\n  const patterns = [\n    // Définitions de fonction C/C++\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Appels de fonction\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Fonctions JavaScript/TypeScript\n    /function\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Méthodes\n    /\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1] && match[1].length > 1) {\n        functions.add(match[1]);\n      }\n    }\n  });\n\n  return Array.from(functions);\n}\n\nfunction detectPotentialIssues(targetLine: string, contextCode: string): string[] {\n  const issues: string[] = [];\n\n  // Vérifications communes\n  const checks = [\n    {\n      pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=\\s*[^;]*$/,\n      message: \"Variable potentiellement non initialisée ou assignation incomplète\"\n    },\n    {\n      pattern: /\\*\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?![=])/,\n      message: \"Déréférencement de pointeur - vérifier si le pointeur est NULL\"\n    },\n    {\n      pattern: /\\[\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\]/,\n      message: \"Accès tableau - vérifier les limites d'index\"\n    },\n    {\n      pattern: /\\/\\*.*\\*\\//,\n      message: \"Code commenté - peut indiquer du code problématique\"\n    },\n    {\n      pattern: /TODO|FIXME|HACK|BUG/i,\n      message: \"Commentaire indiquant un problème connu\"\n    },\n    {\n      pattern: /malloc|calloc|free/,\n      message: \"Gestion mémoire manuelle - vérifier les fuites mémoire\"\n    },\n    {\n      pattern: /strcpy|strcat|sprintf/,\n      message: \"Fonction potentiellement dangereuse - risque de buffer overflow\"\n    }\n  ];\n\n  checks.forEach(check => {\n    if (check.pattern.test(targetLine)) {\n      issues.push(check.message);\n    }\n  });\n\n  // Vérifications contextuelles\n  if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {\n    issues.push(\"Requête SQL sans vérification d'erreur appropriée\");\n  }\n\n  if (targetLine.includes('=') && !targetLine.includes(';')) {\n    issues.push(\"Assignation sans point-virgule terminal\");\n  }\n\n  return issues;\n}\n\nfunction generateSuggestions(targetLine: string, contextCode: string, issues: string[]): string[] {\n  const suggestions: string[] = [];\n\n  // Suggestions basées sur les problèmes détectés\n  if (issues.some(issue => issue.includes('non initialisée'))) {\n    suggestions.push(\"Initialiser la variable avant utilisation\");\n    suggestions.push(\"Vérifier la déclaration de la variable\");\n  }\n\n  if (issues.some(issue => issue.includes('pointeur'))) {\n    suggestions.push(\"Ajouter une vérification NULL avant déréférencement\");\n    suggestions.push(\"Utiliser des pointeurs intelligents si possible\");\n  }\n\n  if (issues.some(issue => issue.includes('tableau'))) {\n    suggestions.push(\"Vérifier que l'index est dans les limites du tableau\");\n    suggestions.push(\"Utiliser des fonctions sécurisées pour l'accès aux tableaux\");\n  }\n\n  if (targetLine.includes('EXEC SQL')) {\n    suggestions.push(\"Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL\");\n    suggestions.push(\"Vérifier le code de retour SQL\");\n  }\n\n  // Suggestions générales\n  if (targetLine.trim().length === 0) {\n    suggestions.push(\"Ligne vide - vérifier si du code manque\");\n  }\n\n  if (targetLine.includes('//') || targetLine.includes('/*')) {\n    suggestions.push(\"Code commenté - vérifier si c'est intentionnel\");\n  }\n\n  return suggestions;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAcO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC1D,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,0CAA0C;QAC1C,MAAM,WAAW,KAAK,KAAK,CAAC;QAE5B,IAAI,UAAU;YACZ,MAAM,GAAG,WAAW,OAAO,QAAQ,GAAG;YACtC,MAAM,YAAY,QAAQ,KAAK,CAAC;YAEhC,sDAAsD;YACtD,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,oBAAiD,eAAe;aACjE;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,aAAa,SAAS,KAAK,CAAC,EAAE;oBAC9B;gBACF;YACF;YAEA,+BAA+B;YAC/B,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA,qDAAqD,oBAAoB;aAC1E;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,WAAW,KAAK,CAAC,EAAE;oBACnB;gBACF;YACF;YAEA,6BAA6B;YAC7B,MAAM,YAAY,gBAAgB;YAElC,2BAA2B;YAC3B,MAAM,WAAW,eAAe,OAAO;YAEvC,OAAO;gBACL;gBACA;gBACA;gBACA,MAAM,YAAY,SAAS,CAAC,EAAE,GAAG;gBACjC;gBACA;gBACA;gBACA;YACF;QACF;QAEA,yCAAyC;QACzC,MAAM,kBAAkB;YACtB,8CAA8C;YAC9C;YACA,4BAA4B;YAC5B;YACA,+BAA+B;YAC/B;SACD;QAED,KAAK,MAAM,WAAW,gBAAiB;YACrC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;oBAMI;gBALb,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACtC,MAAM,UAAU,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACpD,MAAM,aAAa,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;gBAEnD,OAAO;oBACL,WAAW,EAAA,UAAA,KAAK,CAAC,EAAE,cAAR,8BAAA,QAAU,QAAQ,CAAC,QAAO,KAAK,CAAC,EAAE,GAAG;oBAChD;oBACA;oBACA;oBACA,WAAW,gBAAgB;oBAC3B,UAAU,eAAe,OAAO;gBAClC;YACF;QACF;QAEA,yCAAyC;QACzC,OAAO;YACL,WAAW;YACX,OAAO;YACP,SAAS;YACT,YAAY,QAAQ;YACpB,WAAW;YACX,UAAU;QACZ;IACF;AACF;AAEA,SAAS,gBAAgB,OAAe;IACtC,MAAM,gBAAgB;QACpB;YAAE,SAAS;YAAoD,MAAM;QAA2B;QAChG;YAAE,SAAS;YAAwB,MAAM;QAAqB;QAC9D;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAmB,MAAM;QAAgB;QACpD;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAiB,MAAM;QAAc;QAChD;YAAE,SAAS;YAAqB,MAAM;QAAkB;QACxD;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAqB,MAAM;QAAiB;QACvD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAY,MAAM;QAAgB;QAC7C;YAAE,SAAS;YAA0B,MAAM;QAAkB;QAC7D;YAAE,SAAS;YAAe,MAAM;QAAY;QAC5C;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAoB,MAAM;QAAgB;QACrD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAmB,MAAM;QAAgB;KACrD;IAED,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,cAAe;QAC7C,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,KAAa,EAAE,OAAe;IACpD,+BAA+B;IAC/B,MAAM,gBAAwE;QAC5E,SAAS;QACT,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,YAAY;IACd;IAEA,IAAI,WAAW,aAAa,CAAC,MAAM,WAAW,GAAG,IAAI;IAErD,4CAA4C;IAC5C,MAAM,mBAAmB;QACvB;QAAU;QAAU;QAAa;QACjC;QAAuB;KACxB;IAED,MAAM,eAAe;QACnB;QAAU;QAAc;QAAW;QACnC;QAAkB;QAAqB;KACxC;IAED,MAAM,cAAc;QAClB;QAAY;QAAS;QAAU;KAChC;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,WAAW;IACb,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC9D,WAAW,aAAa,QAAQ,SAAS;IAC3C,OAAO,IAAI,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC7D,WAAW,aAAa,SAAS,WAAW;IAC9C;IAEA,OAAO;AACT;AAaO,SAAS,uBAAuB,cAAsB,EAAE,UAAkB;QAAE,UAAA,iEAAkB;IACnG,MAAM,QAAQ,eAAe,KAAK,CAAC;IACnC,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IAEpD,MAAM,eAAe,EAAE;IACvB,IAAK,IAAI,IAAI,WAAW,IAAI,SAAS,IAAK;QACxC,aAAa,IAAI,CAAC;YAChB,QAAQ,IAAI;YACZ,SAAS,KAAK,CAAC,EAAE,IAAI;YACrB,UAAU,MAAM,aAAa;QAC/B;IACF;IAEA,+BAA+B;IAC/B,MAAM,WAAW,mBAAmB,OAAO,YAAY;IAEvD,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS,mBAAmB,KAAe,EAAE,UAAkB;QAAE,UAAA,iEAAkB;IAMxF,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IACpD,MAAM,cAAc,MAAM,KAAK,CAAC,WAAW,SAAS,IAAI,CAAC;IACzD,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,qCAAqC;IACrC,MAAM,kBAAkB,sBAAsB,YAAY;IAE1D,4BAA4B;IAC5B,MAAM,cAAc,oBAAoB,YAAY,aAAa;IAEjE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,oCAAoC;IACpC,MAAM,WAAW;QACf,wCAAwC;QACxC;QACA,uCAAuC;QACvC;QACA,eAAe;QACf;QACA,yBAAyB;QACzB;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,wDAAwD;gBACxD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;oBAC1B,MAAM,UAAU,EAAE,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG;oBACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;wBACjC,UAAU,GAAG,CAAC;oBAChB;gBACF;YACF;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,MAAM,WAAW;QACf,gCAAgC;QAChC;QACA,qBAAqB;QACrB;QACA,kCAAkC;QAClC;QACA,WAAW;QACX;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;gBACnC,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,sBAAsB,UAAkB,EAAE,WAAmB;IACpE,MAAM,SAAmB,EAAE;IAE3B,yBAAyB;IACzB,MAAM,SAAS;QACb;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;KACD;IAED,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa;YAClC,OAAO,IAAI,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,8BAA8B;IAC9B,IAAI,YAAY,QAAQ,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,kBAAkB;QAC9E,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC,MAAM;QACzD,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,UAAkB,EAAE,WAAmB,EAAE,MAAgB;IACpF,MAAM,cAAwB,EAAE;IAEhC,gDAAgD;IAChD,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,qBAAqB;QAC3D,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,cAAc;QACpD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,aAAa;QACnD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,aAAa;QACnC,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,wBAAwB;IACxB,IAAI,WAAW,IAAI,GAAG,MAAM,KAAK,GAAG;QAClC,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,OAAO;QAC1D,YAAY,IAAI,CAAC;IACnB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { Bug, Database, Home, Sparkles } from 'lucide-react';\n\nconst navigation = [\n  {\n    name: 'Accueil',\n    href: '/',\n    icon: Home,\n  },\n  {\n    name: \"<PERSON><PERSON><PERSON> d'Erreurs\",\n    href: '/error-analysis',\n    icon: Bug,\n  },\n  {\n    name: 'Génération SQL',\n    href: '/sql-generation',\n    icon: Database,\n  },\n];\n\nexport default function Navigation() {\n  const pathname = usePathname();\n\n  return (\n    <nav className=\"relative\">\n      {/* Navigation avec couleurs corporate Leoni */}\n      <div className=\"bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-20 items-center\">\n            {/* Logo + Title */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30\">\n                  <Sparkles className=\"w-7 h-7 text-white\" />\n                </div>\n                <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse\"></div>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-white\">\n                  Leoni Agents\n                </h1>\n                <p className=\"text-xs text-[#ff8c42] font-medium\">Intelligence Artificielle</p>\n              </div>\n            </div>\n\n            {/* Navigation Links */}\n            <div className=\"hidden sm:flex sm:space-x-2\">\n              {navigation.map((item) => {\n                const Icon = item.icon;\n                const isActive = pathname === item.href;\n\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={cn(\n                      'group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105',\n                      isActive\n                        ? 'bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white shadow-lg shadow-[#ff7514]/25'\n                        : 'text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md'\n                    )}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <Icon\n                      className={cn(\n                        'w-5 h-5 mr-2 transition-all duration-300',\n                        isActive ? 'text-white' : 'text-white/70 group-hover:text-[#ff8c42]'\n                      )}\n                    />\n                    {item.name}\n                    {isActive && (\n                      <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff7514] to-[#ff8c42] opacity-20 animate-pulse\"></div>\n                    )}\n                  </Link>\n                );\n              })}\n            </div>\n\n            \n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <div className=\"sm:hidden px-4 pt-2 pb-3 space-y-1\">\n        {navigation.map((item) => {\n          const Icon = item.icon;\n          const isActive = pathname === item.href;\n\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'block pl-3 pr-4 py-2 border-l-4 text-base font-medium',\n                isActive\n                  ? 'bg-blue-50 border-blue-500 text-blue-700'\n                  : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'\n              )}\n              aria-current={isActive ? 'page' : undefined}\n            >\n              <div className=\"flex items-center\">\n                <Icon className=\"w-4 h-4 mr-3\" />\n                {item.name}\n              </div>\n            </Link>\n          );\n        })}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;IACX;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAG9C,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;0CAKtD,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6IACA,WACI,0FACA;wCAEN,gBAAc,WAAW,SAAS;;0DAElC,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,WAAW,eAAe;;;;;;4CAG7B,KAAK,IAAI;4CACT,0BACC,6LAAC;gDAAI,WAAU;;;;;;;uCAlBZ,KAAK,IAAI;;;;;gCAsBpB;;;;;;;;;;;;;;;;;;;;;;0BASR,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oBAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,WACI,6CACA;wBAEN,gBAAc,WAAW,SAAS;kCAElC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;gCACf,KAAK,IAAI;;;;;;;uBAZP,KAAK,IAAI;;;;;gBAgBpB;;;;;;;;;;;;AAIR;GA3FwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}]}