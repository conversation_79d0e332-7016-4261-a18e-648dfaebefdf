// Script de test simple pour vérifier les agents
const fs = require('fs');

// Test de l'agent d'analyse d'erreurs avec fichier d'erreur
async function testErrorAnalysis() {
  console.log('🔍 Test de l\'Agent d\'Analyse d\'Erreurs (avec fichier d\'erreur)...');

  try {
    const programContent = fs.readFileSync('./public/examples/test_program_with_issues.c', 'utf8');
    const errorContent = fs.readFileSync('./public/examples/test_program_with_issues.error', 'utf8');

    const response = await fetch('http://localhost:3000/api/error-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        programFile: {
          name: 'test_program_with_issues.c',
          content: programContent,
          type: 'program'
        },
        errorFile: {
          name: 'test_program_with_issues.error',
          content: errorContent,
          type: 'error'
        }
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Agent d\'Analyse d\'Erreurs fonctionne !');
      console.log('📊 Résumé:', data.data.summary);
      console.log('🐛 Erreurs détectées:', data.data.errors.length);

      // Afficher les détails des erreurs avec numéros de ligne
      data.data.errors.forEach((error, index) => {
        console.log(`\n   Erreur ${index + 1}:`);
        console.log(`   - Type: ${error.errorType}`);
        console.log(`   - Ligne: ${error.lineNumber || 'Non spécifiée'}`);
        console.log(`   - Sévérité: ${error.severity}`);
        if (error.codeContext) {
          console.log(`   - Code: ${error.codeContext.targetLine.trim()}`);
        }
      });
    } else {
      console.log('❌ Erreur:', data.error);
    }
  } catch (error) {
    console.log('❌ Erreur de test:', error.message);
  }
}

// Test de l'analyse statique (sans fichier d'erreur)
async function testStaticAnalysis() {
  console.log('\n🔍 Test de l\'Analyse Statique (sans fichier d\'erreur)...');

  try {
    const programContent = fs.readFileSync('./public/examples/test_program_with_issues.c', 'utf8');

    const response = await fetch('http://localhost:3000/api/error-analysis/static', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        programFile: {
          name: 'test_program_with_issues.c',
          content: programContent,
          type: 'program'
        }
      }),
    });

    const data = await response.json();

    if (data.success) {
      console.log('✅ Analyse statique réussie!');
      console.log(`📊 Résumé: ${data.data.summary}`);
      console.log(`🐛 Problèmes détectés: ${data.data.errors.length}`);

      data.data.errors.forEach((error, index) => {
        console.log(`\n   Problème ${index + 1}:`);
        console.log(`   - Type: ${error.errorType}`);
        console.log(`   - Ligne: ${error.lineNumber || 'Non spécifiée'}`);
        console.log(`   - Sévérité: ${error.severity}`);
        if (error.autoFixSuggestions && error.autoFixSuggestions.length > 0) {
          console.log(`   - Corrections suggérées: ${error.autoFixSuggestions.length}`);
        }
      });

      if (data.data.codeQuality) {
        console.log(`\n📈 Qualité du code: ${data.data.codeQuality.score}/100`);
        console.log(`🔧 Maintenabilité: ${data.data.codeQuality.maintainability}`);
      }
    } else {
      console.log('❌ Erreur:', data.error);
    }
  } catch (error) {
    console.log('❌ Erreur de test:', error.message);
  }
}

// Test de l'agent de génération SQL
async function testSQLGeneration() {
  console.log('\n🗄️ Test de l\'Agent de Génération SQL...');
  
  try {
    const specification = `
Créer une base de données simple pour un système de gestion des utilisateurs avec :
- Table users (id, nom, email, date_creation)
- Table roles (id, nom, description)
- Table user_roles (user_id, role_id)

Contraintes :
- Clés primaires auto-incrémentées
- Clés étrangères appropriées
- Email unique
`;

    const response = await fetch('http://localhost:3000/api/sql-generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        specification,
        databaseType: 'mysql',
        includeComments: true
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Agent de Génération SQL fonctionne !');
      console.log('📝 Tables créées:', data.data.tables.join(', '));
      console.log('⚙️ Opérations:', data.data.operations.join(', '));
    } else {
      console.log('❌ Erreur:', data.error);
    }
  } catch (error) {
    console.log('❌ Erreur de test:', error.message);
  }
}

// Exécuter les tests
async function runTests() {
  console.log('🚀 Démarrage des tests des agents Leoni...\n');
  
  // Attendre que le serveur soit prêt
  console.log('⏳ Attente du serveur...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testErrorAnalysis();
  await testStaticAnalysis();
  await testSQLGeneration();
  
  console.log('\n✨ Tests terminés !');
}

runTests().catch(console.error);
