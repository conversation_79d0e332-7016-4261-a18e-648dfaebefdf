/****************************************************************************
 * (c)  2015 , Copyright Leoni AG, Nuernberg
 *
 * File    : SCCS/s.caofors.ec
 * Version : 1.112
 * Date    : 22/11/09 14:34:53
 *
 * Author     : cont3460 (derived from math1009)
 * Libraries  : pbp
 * Description:
 *		see usage()
 *              partly derived from old cutting machine: vsr/ffsso002/ffsso002.ec
 *
 *             do not kill -9 ! (else LOCK file can not be deleted) use normal kill <pid>
 *
 *   DB-Tables (access, select cols):
 *         SELECT * on tables C-Struct MUST match exact DB structure (else SQLlib fatal errors)
 *
 *   		fvzalias(RO, *): 	(server / Verzeichnis)
 *   			valias='CAOIN' & 'CAOOUT' contains server & destdir
 *		pasy(RO, datenf) 	(system paramter)
 *			datenf[36++8] contains machine
 *
 *		fbwstort(RW, *): 		(Uebertragungssteuerung Preisdatei) arbgeb='CAO'
 *					update aendat (last run of Material export)
 *
 *	Material Lists:
 *		apfw(RO, *):		(Fertigungsweg-Kopfdaten)    UNIQUE: 1 row per teilnr (sprrkz=0)
 *		apzd(RO, *): 		(Fertigungsweg-Zusatzdaten)  UNI: teilnr & arbgnr  (ferweg)
 *
 *		apag(RO, *):		(Arbeitsgang-Daten)          UNI: teilnr & arbgnr
 *		magd(RO, *): 		(Material-Grunddaten Teilestamm) UNI: teilnr
 *		magi(RW, indiX): 	(select only 3 indi columns: DB structure independant) UNI: teilnr
 *
 *		mako(RO, *):		(Komponentensatz)	     UNI: teilnr & stponr / komtnr
 *
 *		maks(RO, *):		(Teilestamm KANBAN)	     UNI: teilnr & arbgnr
 *		apko(RO, *):		(Satzaufbau)	     	     UNI: teilnr & arbgnr
 *
 *		apvd(RO, *): 		(Operation-twisted wire)     UNI: teilnr & arbgnr, agnr..
 *
 *		fotx(RO, txtnmr): 	(common text)    txtshl LIKE '<WERK><teilnr[22]>01XXXX%'  XXXX=arbgnr
 *		fotp(RO, txtzle):	(text rows)      txtnmr == fotx.txtnmr
 *
 *	Crimp-Export:
 *		fpekodatv2(RO, *)	all teilnr
 *		fpekoda2v2(RO, *)	if match with above (teilnr & leit1 & kundnr) additional ISO crimp values here
 *              magd(RO, *)
 *              fbwparam(RO, *)		DEFTOL for PullOffForce
 *
 *	Crimp-Update (Import)           update magi.indi customer and wireclass
 *              ffsso002cust(RO, *)	for update magi.indiX-customer
 *              ffsso002wgv2(RO, *)	for update magi.indiX-wireclass
 *
 *	Orders:
 *		fpsl(RO, *): 		(PSR, Schneideposition vom Typ Leitung)
 *					   (einres=pasy.datenf[36] & avtxsl <"2000")
 *              fpms(RO, *) 		(Schneidepositionen)
 *		apzd(RO, *)
 *		makt(RO):		(Bewegungsdaten KANBAN)	     UNI: teilnr & arbgnr & kanbnr
 *
 * 		caocolors(RO, *):	colorcodes (prefilled color mapping FORS->CAO)
 *
 *		caojobs(RW, *):		CAO orders
 *
 *      Feedback:
 *		caojobs (RW, *)        (update status)
 *		fpms(RW, *)            (insert)
 *      Scrap:
 *              ffsso002p (RO, *)      get warehouse (1. fpsl.lagort or 2. fpld&apag.lagort)
 *              fpsl(RO, *)
	*              fpld(RO, *)
	*              apag(RO, *)
			    *              bi_lalf (RW, *)        ($FORS_BI_SUFFIX=cao, autocreate bi_lalf_cao)
					  *              do-bi lalf --import    loads from DB:bi_lalf_$FORS_BI_SUFFIX !
							*                                     imports to lakt
								      *

* ***************************************************************************/
/*   Aenderungen:                                                           */							
/*LD01  07.03.2023 khouloud Boujnah   (CHG0044259 - CTASK0078380)           */
/*                  - Enhance the function D110_write_leadset, for MAVA     */ 
/*					  Change for Twisted part         					    */
/*LD02  13.04.2023 khouloud Boujnah   (CHG0045279 - CTASK0085814)           */
/*                  - Enhance the function D110_write_leadset, by separate  */
/*                    the type twist and Double crimp wires depending on    */
/*                    the APVD table record.      					        */
/*LD03  20.04.2023 khouloud Boujnah   (CHG0045279 - CTASK0085814)           */
/*                  - Enhance the function D113_write_leadset_P10, to       */
/*                    define the wire modified by the MAVA for the double   */
/*                    crimp type.      					                    */
/*LD04  10.10.2023 khouloud Boujnah   (CHG0048262 - CTASK0103972)           */
/*					- Enhance the section D520_get_warehouse, to treat      */
/*                    the new version of cao for scrap feedback vector 	    */
/*LD05  06.06.2024 Wissal Jabbari     (CHG0052236 - CTASK0126545)           */
/*                  -Initialize typp1 var to proceed selection of Wh(apag)  */
/*                  -Related to INC0162795                                  */
/*LD06  07.11.2024 Tarek Bejaoui      (CHG0054961 - CTASK0141951)           */
/*                  -Change the value of zeta batch size to take apzd.ltbmng*/ 
/*LD07  28.04.2025 Khouloud Boujnah      (CHG0057068 - CTASK0153750)        */
/*                  -Optimise the query for better performance              */
/*LD08  13.06.2025 Khouloud Boujnah      (CHG0057952 - CTASK0162021)        */
/*                  -Add the type when leadset equal to ZETA                */
/****************************************************************************/
#ifndef  __DONT_USE_SCCSID__
CONST char sccsid_caofors_ec[] = "@(#)caofors.ec	1.112" " Build: " __DATE__ " at "  __TIME__;
#endif
CONST char prog_ver[] = "1.112";

#define PROGNAME "CAOFORS"
#define PROGNAME_L "caofors"

#define OK          0
#define ERROR      -1

									     /* prog_step (not all used) */
#define ABORT           7
#define OUTPUT          6
#define SPOOL           5
#define TOUCH_RT        4
#define DB_OPEN         3
#define START           2

#include <string.h>
#include <stdio.h>
#include <ctype.h>
#include <errno.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <time.h>
#include <sys/uio.h>
#include <signal.h>
#include <getopt.h>
#include <math.h>

#include "share/lib/common_def.h"
#include "share/lib/pbp.h"
#include "share/src/error.h"
#include "share/src/publfuncsql.h"
#include "share/src/sqllib.h"
#include "share/src/sshfunc.h"
#include "share/include/fehnum.h"
#include "share/src/fleoallg.c"
#include "share/inc/load_file.inc"
#include "share/inc/execute.inc"
#include "share/inc/ftoa.inc"
#include "share/inc/log_parameter.inc"
#include "share/inc/get_filename.inc"
#include "share/inc/swap_string.inc"
#include "share/inc/swap_double.inc"
#include "share/inc/swap_int.inc"
#include "share/inc/string_replace.inc"
#include "share/inc/string_trans.inc"
#include "share/inc/file_date.inc"
#include "share/src/dirent_list.c"
#include "../inc/get_dbtime.inc"
#include "../inc/get_dbdate.inc"

/* do not declare global vars in (some new) include's, bad style to declare mem in include
 *  very ERROR prone to use always same global db-struct in all funcs
 *  use multiple ones defined below
 */
$define SQLINC_DONOT_DECLARE_VARS;

EXEC SQL BEGIN DECLARE SECTION;

/* define for SQL string replacements (cpp does not see) ADDITIONAL to caofors.h MAGI_INDI_XXXX define !!! */
EXEC SQL DEFINE S_MAGI_INDI_CUST        indi41;
EXEC SQL DEFINE S_MAGI_INDI_PROJ        indi42;
EXEC SQL DEFINE S_MAGI_INDI_WIRECL 	indi43;

EXEC SQL INCLUDE share/include/apzdrec.h;
EXEC SQL INCLUDE share/include/magdrec.h;
EXEC SQL INCLUDE share/include/matlrec.h;
EXEC SQL INCLUDE share/include/apfwrec.h;
EXEC SQL INCLUDE share/include/makorec.h;
EXEC SQL INCLUDE share/include/apvdrec.h;
EXEC SQL INCLUDE share/include/apagrec.h;
EXEC SQL INCLUDE share/include/fpslrec.h;
EXEC SQL INCLUDE share/include/fpldrec.h;
EXEC SQL INCLUDE share/include/fpscrec.h;
EXEC SQL INCLUDE share/include/fpmsrec.h;
EXEC SQL INCLUDE share/include/fpmcrec.h;
EXEC SQL INCLUDE share/include/fpmrrec.h;
EXEC SQL INCLUDE share/include/maksrec.h;
EXEC SQL INCLUDE share/include/fotprec.h;
EXEC SQL INCLUDE share/include/fotxrec.h;
EXEC SQL INCLUDE share/include/pasyrec.h;
EXEC SQL INCLUDE share/include/maktrec.h;
EXEC SQL INCLUDE share/include/apkorec.h;
EXEC SQL INCLUDE share/include/magirec.h;
EXEC SQL INCLUDE share/include/matrrec.h;
EXEC SQL INCLUDE share/include/caojobsrec.h;
EXEC SQL INCLUDE share/include/bi_lalfrec.h;
EXEC SQL INCLUDE share/include/bierrorrec.h;
EXEC SQL INCLUDE share/include/errorscraprec.h;
EXEC SQL INCLUDE share/include/fvzaliasrec.h;
EXEC SQL INCLUDE share/include/caocolorsrec.h;
EXEC SQL INCLUDE share/include/fbwstortrec.h;
EXEC SQL INCLUDE share/include/ffsso002prec.h;
EXEC SQL INCLUDE share/include/ffsso002custrec.h;
EXEC SQL INCLUDE share/include/ffsso002wgv2rec.h;
EXEC SQL INCLUDE share/include/fpekodatv2rec.h;
EXEC SQL INCLUDE share/include/fpekoda2v2rec.h;
EXEC SQL INCLUDE share/include/fbwparamrec.h;
EXEC SQL INCLUDE share/include/weuirec.h;
EXEC SQL INCLUDE share/include/labsrec.h;
EXEC SQL INCLUDE share/include/pasurec.h;

/* used globals from above include */
/* APFWTYP apfw;		for main teilnr */
/* APZD    apzd;		for main teilnr */
/* APVD    apvd;		for main teilnr */
/* MAGDTYP magd;		for main teilnr  */
/* APAG    apag;		for main teilnr */
/* MAKOTYP mako;		- */
MAGITYP magi;			/* for order */
/* BI_LALF bi_lalf;		scrap */

/* additional structs for lower loop selects, main data kept !) */
APZD    apzd_ltponr;		/* for ltponr */

MAKOTYP mako_ltg1;		/* 1. ltg */
MAKOTYP mako_ltg2;		/* 2. ltg */
MAGDTYP magd_ltg1;		/* 1. ltg */
MAGDTYP magd_ltg2;		/* 2. ltg */
APZD    apzd_ltg2;		/* 2. ltg (1.ltg = apzd) */
APAG    apag_ltg2;		/* 2. ltg (1.ltg = apag) */
APAG    apag_ws;		/* workstep */
APAG    apag_bom;		/* bom */
APKO	apko_bom;		/* bom */
MAKOTYP mako_bomX;		/* bom  ltg/term/seal */
MAGDTYP magd_getX;		/* tmp use in G_get_magd */
LABSTYP labs_hu	;		/* use handling unit */
MAGDTYP magd_cmp;		/* use crimp */
MAGDTYP magd_hu;		/* use handling unit */
MAGDTYP magd_bom;		/* use bom */
MAGDTYP magd_bom_update;		/* use bom */
MAGITYP magi_ltg;		/* for Wire */
MAKTTYP makt;			/* for order */

MAKOTYP mako_term11;		/* 1. ltg Side 1 */
MAKOTYP mako_term12;		/* 1. ltg Side 2 */
MAKOTYP mako_seal11;		/* 1. ltg Side 1 */
MAKOTYP mako_seal12;		/* 1. ltg Side 2 */
MAKOTYP mako_term21;		/* 2. ltg Side 1 */
MAKOTYP mako_term22;		/* 2. ltg Side 2 */
MAKOTYP mako_seal21;		/* 2. ltg Side 1 */
MAKOTYP mako_seal22;		/* 2. ltg Side 2 */

CAOCOLORS caocolors;
CAOJOBS caojobs;

/* order */
/* FPSL    fpsl;		dont use global inc */
FPSL    fpsl_1;			/* 1.nd entry */
FPSL    fpsl_2;			/* 2.nd entry */
FPSL    fpsl_chk;		/* check in D320_check_fpsl_new */
FPMS	fpms_1;
FPMS	fpms_2;
FPMS	fpms_chk;
FPMR	fpmr_1;
FPMR	fpmr_2;
FPMC	fpmc_chk;
FPMC	fpmc;
FPMR	fpmr_chk;

char	arbplz [255];	    /* for tmp usage in select*/
char	sql_tmpstr [255];	/* for tmp usage in select*/
char	sql_tmpstr1[255];	/* for tmp usage in select*/
char	sql_tmpstr2[255];	/* for tmp usage in select*/
int	    sql_tmpint;		    /* for tmp usage in select*/
int	    sql_tmpint2;		/* for tmp usage in select*/
long	sql_tmplong;		/* for tmp usage in select*/
double  sql_tmpdbl;		   /* for tmp usage in select*/
long    today = 0;		   /* date as long yyyymmdd for aendat.. (TEST with -T) */
long    real_today = 0;	   /* real today (not TEST with -T) */
long    date_update   = 0;
long    real_time  = 0;
char    matl_retrkz[5];		/* 								 */
char    avtxsl_von[5],avtxsl_bis[5];
float   meng_ltg1;
float   meng_ltg2;
char    teilnr_div[22], matnum_ltg1[17], matnum_ltg2[17];
char    matnum[18];
//char BMGRUP[9];
char    RESKBZ[21];
long    sttime                 = 0;     /* current time job start             */
bool    with_time;
bool    without_time;
double  tomorrow;

char    sql_errfld[8]; /*   Trace Scrap error  */
char    sql_errtxt[61];/*   Trace Scrap error  */

int     arbgnr_ltg1 = 0;
int     arbgnr_ltg2 = 0;
int     agnr01 = 0;

EXEC SQL END DECLARE SECTION;


/****  linked lists for storing DB-structs often access'ed in local mem */
#define LIST_LINKED_KEYSTR_LEN   32	/* list key-string len (max of all lists) */
#include "share/src/list_linked.c"	/* linked list handling */

#include "caofors.h"			/* need above DB structs */


/**********************************************************************/
/************************* G L O B A L S ******************************/
/**********************************************************************/
/**********************************************************************/
/* Definition globale Variablen und Konstanten                        */
/**********************************************************************/

/**     LOG files                                                     */
char errlog_name     [80+EN] = "caofors.error"; /* Name Fehler-Datei */
char logfile_name    [80+EN] = "caofors.log";   /* Name Log-Datei    */
char logpath[200]; 

int dbg_level = 1;		/* dbg file   level -dx (see share/src/error.h) */
int dbg_stdlevel = 0;		/* dbg stdout level -Dx (see share/src/error.h) */

char done = 'N'  ; /* would be set to 'Y' when the table caocolors is empty  */
char score = 'N' ; // used for rawmat feedback to distinguish between P1 an P2.
char zeta = 'N'  ; // to know which one is under processing: Zeta or not.
char p_Leadset_lsm [MAXLEN_LDS_MAIN_LDS+1]; //save previous LeadSet name on LeadSetMain.
char p_Leadset_ws [MAXLEN_LDS_MAIN_LDS+1]; //save previous LeadSet name on WorkStep.
char p_Leadset_bom [MAXLEN_LDS_MAIN_LDS+1]; //save previous LeadSet name on WorkStep.
int double_crimp;

#define DBG_FILE_VAR		dbg_level
#define DBG_STD_VAR		dbg_stdlevel

/**********************************************************************
 ***** global data  (defaults memset  0)
 **********************************************************************/
struct {
    pid_t	mypid;				/* own PID */
    char	hostname[64];			/* hostname */
    char	cao_name[21];			/* -A xxx default:CAO (fvzalias.valias) */
    int 	prog_step;			/* program step */
    char	start_msg[512];			/* prg start info (version,user, args) */
    char	lock_file[MAX_FILENAME_LEN];	/* created lock file (per task) */
    int		has_locked;			/* created lock, so delete on exit */
    int		no_pidlogname;			/* option: -P */
    int		keep_files;			/* option: -k */
    int		dont_copy;			/* option: -C */
    int		beauty_out;			/* option: -B */
    char	do_task[32];			/* arg 4: task */
    char 	cao_in_dir[MAX_FILENAME_LEN];	/* CAO input dir (fvzalias) FORS->CAO)  */
    char 	cao_out_dir[MAX_FILENAME_LEN];	/* CAO output dir (fvzalias) CAO->FORS) */
    char 	cao_in_srv[20];			/* CAO input server hostname (fvzalias)  */
    char 	cao_out_srv[20];		/* CAO output server hostname (fvzalias)  */
    char	tmp_order_fdb[MAX_FILENAME_LEN]; /* written order fdb file with unprocessed lines */
    char	tmp_order_rawmat[MAX_FILENAME_LEN]; /* written order rawmat file with unprocessed lines */
	char	tmp_order_rawmat_hu[MAX_FILENAME_LEN]; /* written order rawmat file with unprocessed lines */
    int		tmp_order_fdb_cnt; 		/* cnt fdb with unprocessed lines */
    int		tmp_order_rawmat_cnt; 		/* cnt rawmat with unprocessed lines */
	int		tmp_order_rawmat_hu_cnt; 		/* cnt rawmat with unprocessed lines */
    char 	host_out[MAX_FILENAME_LEN];	/* host_out dir, TODO ?? not used */
    char	warehouse_default[20];		/* default warehouse */
    char	scrap_recfunc[10];		/* SCR or SRT(de) */
    char	tmparg[MAX_FILENAME_LEN];	/* if given -t */
    char	tmpdir[MAX_FILENAME_LEN];	/* tmp out dir (csv-out, LOCK, stop) */
    long	use_today;			/* use this as aendat (usually: today) */
    char	timestamp_start[24];		/* timestamp format csv files yyyymmdd-hhmmss */
    int		opt_wire_klassf;		/* option: -o wire-klassf */
    int		opt_noexec_do_bi_scrap;		/* option: -o noexec-do-bi-scrap */
    char	opt_apfw_teilnr[30];		/* option: -o apfw-teilnr=XXXXXX (export only this) */
    char	opt_matl[30];		/* option: -o matl_retrkz=X (export only parts with retrkz = X) */
    list_linked_t	*magd_list; 		/* linked list head */
    list_linked_t	*caocol_list; 		/* linked list head */
	int scrap;
	int apar;
	int time;
} g;

int fdg_leadset;
int fdg_workstep;
int fdg_leadset_P1;
int fdg_leadset_P2;
int fdg_bom;
int fdg_font;
int fdg_seal;
int fdg_terminal;
int fdg_wire;
int millesecond;
long nanosecond = 0;
int mako_cnt = 0;


char matken_P10 [6];
char csv_file_leadset	   	[MAX_FILENAME_LEN + EN];
char csv_file_workstep		[MAX_FILENAME_LEN + EN];
char csv_file_leadset_P1 	[MAX_FILENAME_LEN + EN];
char csv_file_leadset_P2 	[MAX_FILENAME_LEN + EN];
char csv_file_BOM 	   	[MAX_FILENAME_LEN + EN];
char csv_file_FONT 		[MAX_FILENAME_LEN + EN];
char csv_file_SEAL		[MAX_FILENAME_LEN + EN];
char csv_file_TERMINAL		[MAX_FILENAME_LEN + EN];
char csv_file_Wire		[MAX_FILENAME_LEN + EN];
/*LD04{*/
int typp1 = 0;
/*LD04}*/

/* prototypes --------------------------------------------------------------- */
int main(int argc, char **argv);
int A100_prog_start(int parmc, char *parmv[]);
int A130_get_environment(void);
void A150_check_logfile (void);

/* material export */
int A140_create_csv_files(void);

int D100_masterdata(char mode);
int E100_closecopy_csv_files(int leads_cnt);
int D110_write_leadset(void);
int D111_write_leadset_main(char cable_type, rec_leadset_main_t *prec_leadset_main);
int D112_write_workstep    (char cable_type, rec_leadset_main_t *prec_leadset_main);
int D113_write_leadset_P10 (char cable_type, rec_leadset_main_t *prec_leadset_main);
int D119_write_leadset_P20 (char cable_type, rec_leadset_main_t *prec_leadset_main);
int D114_write_bom         (char cable_type, rec_leadset_main_t *prec_leadset_main);
int D1141_write_bomdata    (char cable_type, rec_bom_t *prec_bom, mat_ptr_t *mtpp, int ltgnum, MAKOTYP *mako_xp,
		int res_type, int res_place, const char *info);
int D1142_writeline_bom(rec_bom_t *prec_bom);
int D115_write_wire        (char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp);
void D120_write_wire_update (void);		   
int D116_write_terminal    (char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp);
int D117_write_seal        (char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp);
int D118_write_font(void);
magd_list_t * G_get_magd(int firmnr, char *teilnr);

/* order export */
int D300_order(void);
int D310_write_orders(int fdg, char * csv_file_name);
int D320_caojobs (FPSL *fpslp, int do_insert);
int D311_writeline_order(struct record_type_ORDER_DATA *order_dataTp, int fdg, char *csv_file_name);

/* feedback */
int D400_feedback(void);
int D410_feedback_file(const char *dirpath, const char * fname);
int D411_feedback_line(rec_order_feedback_t *order_fdb_p);
int D412_feedback_check_fpms(FPMS *fpms_p);
int D413_feedback_check_insert_fpms(FPMS *fpms_p);

/* scrap */
int A500_scrap_init(void);
int D500_scrapmat(void);
int D510_scrapmat_file(const char *dirpath, const char * fname);
int D511_scrapmat_line(rec_order_scrap_t *scrapmat_p);
char * D520_get_warehouse(rec_order_scrap_t *scrapmat_p);
int D530_clean_bilalf(int del_bi_lalf, int del_bierror);
int D540_check_bilalf(int check_table);
int D550_insert_errorscrap(rec_order_scrap_t  scrap_data, char  * timestamp_errorscrap);

/* crimp export,  update magi*/
int D600_crimp_exp(void);
int D610_crimp_exp_part(int fd, const char * fname, FPEKODATV2TYP *fpekodatv2_p);
int D620_crimp_exp_line(int fd, const char * fname, record_crimp_exp_t *crimp_dat_p);
int D700_crimp_upd_magi_custom(void);
int D700_crimp_upd_magi_wirecl(void);

/* rawmat */
int D800_rawmat(void);
int D810_rawmat_file(const char *dirpath, const char * fname);
int D811_rawmat_line(rec_order_rawmat_t *rawmat_p);
int D811_rawmat_line_HU(rec_order_rawmat_hu_t *order_rawmat_p);
int D812_rawmat_insert_fpmc(FPMC *fpmc_p);
int D812_rawmat_insert_fpmr(FPMR *fpmr_p);
/*Handling Unit creation*/
int D200_handling_unit(void);
int D210_handling_unit_exp(int fd, const char * fname, WEUITYP *weui_p);
int D220_handling_unit_exp_line(int fd, const char * fname, record_handling_unit_exp_t *handling_unit_dat_p);
/* common */
int D900_caojobs_update (FPMS *fpmsp, const char *state);
int D900_caojobs_clean(void);

caocol_list_t * G_get_color_code(char *forscolor);
void G_fona_msg (int srcline, const char * infomsg);
int G_write_line (int fdg , char * line);
int G_write_fileappend (const char *filename, char *data, int data_len);
int G_lock_file (const char * task, int mode);
int G_lock_process (const char * task);
int G_check_stop_file (const char * filename);
int G_copy_file(char * src_file, char* server, char * dst_dir);
int G_delete_file(const char *filename, int force_delete);
int G_rename_file(const char *filename, const char *new_filename);
void sigact_handler(int sig, siginfo_t *siginfo, void * contextp);
int G_terminate (int retcode);
void split_leit(char * leit, char * quer_1,char * quer_2 );
/***********************************************************************************
 * function macros
 *   as in code, so 'return ERROR' will return the function used this Macro !
 ********************************************************************************* */
/* on SQL ERROR do logfile */
#define  SQLCODE_ERROR_LOGONLY(srcline, dblog, msgid, msgtable)	\
	if (SQLCODE < 0 ) {					\
		DBG_log(1, "SQLCODE=%ld (%s, %s) [caofors.ec line: %d]", SQLCODE, msgid, msgtable, srcline); \
	}

/* on SQL ERROR do log & error file, and return (in used function!) */
#define  SQLCODE_ERROR_PUTERRDB_RET(srcline, dblog, msgfunc, msgsql)	\
	if (SQLCODE < 0 ) {					\
		db_error(dblog);					\
		puterrdb2(msgsql, msgfunc);				\
		DBG_err(1, "SQLCODE=%ld [caofors.ec line: %d]", SQLCODE, srcline); \
		return ERROR;					\
	}

/* same as above but add. return with given cast & value (if not int) */
#define  SQLCODE_ERROR_PUTERRDB_RETVAL(srcline, dblog, msgfunc, msgsql, retval) \
	if (SQLCODE < 0 ) {							\
		db_error(dblog);							\
		puterrdb2(msgsql, msgfunc);						\
		DBG_err(1, "SQLCODE=%ld [caofors.ec line: %d]", SQLCODE, srcline);	\
		return (retval);						\
	}

#define  FONA_MSG(srcline, ...) 				\
{ 							\
	char _tmpbuf[2048];					\
	snprintf(_tmpbuf, 2047, __VA_ARGS__);		\
	_tmpbuf[2047] = '\0';				\
	G_fona_msg(srcline, _tmpbuf);			\
}

/* prototypes end ----------------------------------------------------------- */

/*******************************************************************************
 * function: usage
 *    print usage
 * parameters:
 *      p: progname (argv[0])
 * return: -
 ******************************************************************************/

void usage(char *progname)
{
	printf("\n");
	printf("Leoni CAO-FORS Interface (V.%s Build: %s %s)\n", prog_ver, __DATE__, __TIME__);
	printf(" Description:\n");
	printf("    data exchange FORS <-> CAO DiIT Server\n");
	printf("    - write CSV's      -> CAO\n");
	printf("    - read  feedback's -> FORS\n");
	printf("\n");
	printf(" Call:\n");
	printf("\n");
	printf("  %s [-opts] [FIRMA WERK DB [task]] \n", PROGNAME_L);
	printf("\n");
	printf("     task: MAT_P1    	    send MATERIALS Masterdata csv to CAO_P1 (default)\n");
	printf("     	   MAT_P2	        send MATERIALS Masterdata csv to CAO_P2 (default)\n");
	printf("           ORDER_LOOP       create order, read feedback, scrap (endless loop)\n");
	printf("           CRIMP_EXP        CRIMP data export\n");
	printf("           CAO_HU        	Handling Unit export\n");	
	printf("\n");
	printf("           CRIMP_UPD_MAGI   CRIMP update MAGI customer/wireclass (%s+%s)\n",
			_STRDEF(MAGI_INDI_CUST), _STRDEF(MAGI_INDI_WIRECL));
	printf("           ORDER_ONCE       TEST create order csv (only once)\n");
	printf("           FBACK_ONCE       TEST read feedback, scrap (only once)\n");
	printf("\n");
	printf(" Options:\n");
	printf("    -o <option> : activate special option (multiple options: -o opt1 -o opt2):\n");
	printf("        wire-klassf            use WireType as magd.klassf (default: magi.%s)\n", _STRDEF(MAGI_INDI_WIRECL));
	printf("        noexec-do-bi-scrap     dont exec do-bi lalf import (scrap see results in bi_lalf_cao)\n");
	printf("        apfw-teilnr=XXteilnr   master data export only of apfw.teilnr \n");
	printf("        matl_retrkz=X          master data export only of parts with retrkz = X \n");
	printf("\n");
	printf("    -A valias   : TEST: fvzalias.valias +IN&OUT to use (default: CAO -> CAOIN,CAOOUT)\n");
	printf("    -P          : TEST: no PID in log/err filename only username\n");
	printf("    -T yyyymmdd : TEST: use this for apzd.aendat>= (default: fbwstort.aendat = last run)\n");
	printf("    -t tmpdir   : TEST: out tmp dir (default: %s$WERK)\n", CAO_TMPDIR);
	printf("    -d <lev>    : TEST: set file   debuglevel (default: 2)\n");
	printf("    -D <lev>    : TEST: set stdout debuglevel (default: 0)\n");
	printf("    -k          : TEST: keep local files\n");
	printf("    -C          : TEST: dont copy remote (use with -k)\n");
	printf("    -B          : TEST: beauty out (extra lines in CSV and filename time 000000)\n");
	printf("\n");
	printf(" Environment variables:\n");
	printf("    $FIRMA, $WERK, $FORS_DATABASE\n");
	printf("\n");
	printf(" Examples:\n");
	printf("   caofors                       (default DB-environ and task: MAT_P1)\n");
	printf("  standard production:\n");
	printf("   caofors 3 38 seb MAT_P1   (run each midnight)\n");
	printf("   caofors 3 38 seb ORDER_LOOP   (order & feedback, will run endless)\n");
	printf("   touch %s$WERK/caofors.stop    (stop ORDER_LOOP)\n", CAO_TMPDIR);
	printf("\n");
	printf("   TEST only:\n");
	printf("   caofors -k -C                         (keep files in tmpdir, do not copy remote, output to dir)\n");
	printf("   caofors -k -C  3 38 seb               (same as above with other DB-params, use swin)\n");
	printf("           add '-D1' for some info output on console\n");
	printf("\n");
}


/**********************************************************************
 *   Name            :   main
 *   Funktion        :   Haupt-Verarbeitung
 *   Paramter        :   /
 *   Return Code     :   0:OK  1:ERROR  2:ERROR usage
 **********************************************************************/
int main(int argc, char ** argv)
{
	
	int status, rlock;                  /* Verarbeitungs-Status , filelock */
	char c;				/* getopt char */
	char * argv_0 = argv[0];		/* orig argv[0] progname */
	int leads_cnt = 0;			/* new written leads */
	struct stat fstat;			/* file stat */
	struct sigaction sigact;		/* signal handler */
	char *ptr;				/* tmp ptr */
	char	scrap[30];
	/* globals prob. overwritten by cmd line options */
	strcpy(g.cao_name, CAO_NAME);
	g.scrap=10;
	g.apar=0;
	g.time=0;
	gethostname(g.hostname, sizeof(g.hostname)-1);
	if ((ptr = strchr(g.hostname, '.'))) *ptr = '\0';	/* cut domainname */  
	
	/* read cmd-line options */
	while ((c = getopt(argc, argv, "o:A:d:D:Pt:T:kCBh?")) != -1) {
		switch (c) {
			case 'o':           /* special option */
				if (strcmp(optarg, "wire-klassf") == 0) {
					g.opt_wire_klassf = 1;
				} else if (strcmp(optarg, "noexec-do-bi-scrap") == 0) {
					g.opt_noexec_do_bi_scrap = 1;
				} else if (strncmp(optarg, "apfw-teilnr=", 12) == 0) {  /* -o pfw-teilnr=XXXXteilnr */
					snprintf(g.opt_apfw_teilnr, 29, "%s", &optarg[12]);	/* copy part after = */
				} else if (strncmp(optarg, "matl-retrkz=", 12) == 0) {  /* -o pfw-teilnr=XXXXteilnr */
					snprintf(g.opt_matl, 29, "%s", &optarg[12]);	/* copy part after = */
				} else if (strncmp(optarg, "scrap-length=", 13) == 0) {  /* -o pfw-teilnr=XXXXteilnr */
					snprintf(scrap, 29, "%s", &optarg[13]);	/* copy part after = */
					g.scrap=atol(scrap);
				} else if (strcmp(optarg, "APAR") == 0) {
					g.apar = 1;
				}
				else if(strcmp(optarg, "TIME") == 0) {
					g.time=1;
				}
				else {
					usage(argv_0);
					return 0;
				}
				break;
			case 'A':           /* fvzalias.valias */
				strncpy(g.cao_name, optarg, sizeof(g.cao_name)-1);
				break;
			case 'd':           /* debug level */
				dbg_level = strtol(optarg, 0, 0);
				break;
			case 'D':           /* stdout debug level */
				dbg_stdlevel = strtol(optarg, 0, 0);
				break;
			case 'P':           /* no PID in log filenames */
				g.no_pidlogname = 1;
				break;
			case 't':           /* tmp out dir */
				strncpy(g.tmparg, optarg, sizeof(g.tmparg)-1);
				break;
			case 'T':           /* use as today */
				g.use_today = strtol(optarg, 0, 0);
				break;
			case 'k':           /* keep local files */
				g.keep_files = 1;
				break;
			case 'C':           /* dont copy remote */
				g.dont_copy = 1;
				break;
			case 'B':           /*uty out csv */
				g.beauty_out = 1;
				break;
			case '?':
			case 'h':
			default:
				usage(argv_0);
				return 0;
				break;
		}
	}
	/* rest args without -opts  !! ATTENTION argv[0] now not progname, but last option! */
	argc = argc - (optind -1);
	argv = argv + (optind -1);
	argv[0] = argv_0;			/* store orig. argv[0], to give forssetup() */
	
	/* start */
	g.prog_step = START;
	if ((status = A100_prog_start(argc,argv)) == ERROR) {
		G_terminate(1);
	}
		
	set_fonatran("MAIN");		/* msg->sendfk */
	/* tmp dir for csv out */
	if (strlen(g.tmparg) > 0) {
		sprintf(g.tmpdir, "%s", g.tmparg);
	} else {
		sprintf(g.tmpdir, "%s%d", CAO_TMPDIR, parm_werk);
	}
	if (stat(g.tmpdir, &fstat) == -1) {		/* not exist */
		DBG_log(3, "created: tmpdir [%s]", g.tmpdir);
		mkdir(g.tmpdir, 0777);
	}
	/* CHECK only global LOCK file (e.g. on DB backend prevent start) */
	if ((rlock = G_lock_process ("ALL")) != OK) { 	/* global locked */
		G_terminate(1);
	}

	/* install signal handler to catch signals & cleanup lock */
	memset(&sigact, 0, sizeof(sigact));
	sigact.sa_sigaction = sigact_handler;
	sigact.sa_flags = SA_SIGINFO;		/* use sa_sigaction() & give siginfo */
	sigaction(SIGHUP,  &sigact, 0);		/* hangup  kill -1 */
	sigaction(SIGTERM, &sigact, 0);		/* terminate (norm kill (-15)) */
	sigaction(SIGINT,  &sigact, 0);		/* CTRL_C */

	/* server destination: fvzalias: valias server verzei */
	DBG_log(3, "fvzalias: CAO using valias: %sIN & %sOUT (tmpout: %s)", g.cao_name, g.cao_name, g.tmpdir);
	fvzalias.firmnr = parm_firma;
	fvzalias.werknr = parm_werk;
	fvzalias.werkid = parm_werk;
	sprintf(fvzalias.valias, "%sIN", g.cao_name);
	EXEC SQL
		select *  into :fvzalias from fvzalias
		where fvzalias.valias = :fvzalias.valias
		and fvzalias.firmnr = :fvzalias.firmnr
		and fvzalias.werknr = :fvzalias.werknr
		and fvzalias.werkid = :fvzalias.werkid;

	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fvzalias.valias IN");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_err(1, "caofors main: CAOIN no fvzalias.valias = [%s] found.", fvzalias.valias);
		G_terminate(1);
	}
	strcpy(g.cao_in_dir, fvzalias.verzei);
	strcpy(g.cao_in_srv, fvzalias.server);
	DBG_log(1, "fvzalias : CAOIN valias: %s, server: %s, verzei: %s", fvzalias.valias, fvzalias.server, fvzalias.verzei);
	/* OUT */
	sprintf(fvzalias.valias, "%sOUT", g.cao_name);
	EXEC SQL
		select *  into :fvzalias from fvzalias
		where fvzalias.valias = :fvzalias.valias
		and fvzalias.firmnr = :fvzalias.firmnr
		and fvzalias.werknr = :fvzalias.werknr
		and fvzalias.werkid = :fvzalias.werkid;

	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fvzalias.valias OUT");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_err(1, "caofors main: CAOOUT no fvzalias.valias = [%s] found.", fvzalias.valias);
		G_terminate(1);
	}
	strcpy(g.cao_out_dir, fvzalias.verzei);
	strcpy(g.cao_out_srv, fvzalias.server);
	DBG_log(1, "fvzalias : CAOOUT valias: %s, server: %s, verzei: %s", fvzalias.valias, fvzalias.server, fvzalias.verzei);
	/* get & init scrap environ */
	if ((status = A500_scrap_init()) != OK) {
		DBG_err(1, "caofors main: ERROR SCRAP init.");
		G_terminate(1);
	}
	if (dbg_stdlevel > 1) FONA_MSG(__LINE__, "START %s", g.do_task);

	/* start task master data export */
	//if (!strcasecmp(g.do_task, "MAT_TO_CAO")) {			/* write master files */
	if (!strcasecmp(g.do_task, "MAT_P1") || !strcasecmp(g.do_task, "MAT_P2")) {			/* write master files */
		set_fonatran("MAT");					/* msg->sendfk */

		if (!strcasecmp(g.do_task, "MAT_P1")){
			if ((rlock = G_lock_process ("MAT_P1")) != OK) { 	/* locked or error */
				G_terminate(1);
			}
		}
		else if (!strcasecmp(g.do_task, "MAT_P2")){
			if ((rlock = G_lock_process ("MAT_P2")) != OK) { 	/* locked or error */
				G_terminate(1);
			}
		}
		/* open files */
		if ((status = A140_create_csv_files()) == ERROR) {
			G_terminate(1); 
		}
		//D120_write_wire_update();
		/* write files */
		if ((leads_cnt = D100_masterdata('-')) == ERROR) {
			DBG_err(1, "CAOFORS ERROR masterdata failed (see log [%s])", errlog_name);
			FONA_MSG(__LINE__, "ERROR masterdata create");
			G_terminate(1);
		}
		/* close & copy all files */
		if ((status = E100_closecopy_csv_files(leads_cnt)) == ERROR) {
			G_terminate(1);
		}

		/*  loop to create order files and read feedback, scrap */
	} 
	else if ( !strcasecmp(g.do_task, "ORDER_LOOP") ) {
		int order_loop_cnt = 0;			/* done each N loops (*60 sec) */

		set_fonatran("ORD");
		if ((rlock = G_lock_process ("ORDER_LOOP")) != OK) { 	/* locked or error */
			G_terminate(1);
		}

		while (1) {			/* check forever */

			//I-333691 : log file should be reset when it reaches a cetain size
			A150_check_logfile ();


			if (!order_loop_cnt) {
				/* write out new orders */
				if ((status = D300_order()) != OK ) {
					DBG_err(1, "CAOFORS ORDER create ERROR. continue.");
					FONA_MSG(__LINE__, "ERROR ORDER create");
					//sleep(ORDER_LOOP_DELAY);
					while(close_database() != 1)
						sleep(5);
					DBG_log(1, "%s", parm_dbid);
					while (open_database(parm_dbid) != 1) {
						DBG_log(1, "ERROR at reopen DB. continue.");
						sleep(ORDER_LOOP_DELAY);
					}
				}
				/* feedback */
				if ((status = D400_feedback()) != OK ) {
					DBG_err(1, "CAOFORS ORDER FBACK ERROR. continue.");
					FONA_MSG(__LINE__, "ERROR ORDER feedback");
					//sleep(ORDER_LOOP_DELAY);
					while(close_database() != 1)
						sleep(5);
					DBG_log(1, "%s", parm_dbid);
					while (open_database(parm_dbid) != 1) {
						DBG_log(1, "ERROR at reopen DB. continue.");
						sleep(ORDER_LOOP_DELAY);
					}
				}
				/* rawmat */
				if ((status = D800_rawmat()) != OK ) {
					DBG_err(1, "CAOFORS ORDER RAWMAT ERROR. continue.");
					FONA_MSG(__LINE__, "ERROR ORDER rawmat");
					//sleep(ORDER_LOOP_DELAY);
					while(close_database() != 1)
						sleep(5);
					DBG_log(1, "%s", parm_dbid);
					while (open_database(parm_dbid) != 1) {
						DBG_log(1, "ERROR at reopen DB. continue.");
						sleep(ORDER_LOOP_DELAY);
					}
				}
				/* scrap */
				if ((status = D500_scrapmat()) != OK ) {
					DBG_err(1, "CAOFORS SCRAP ERROR. continue.");
					FONA_MSG(__LINE__, "ERROR SCRAP");
					//sleep(ORDER_LOOP_DELAY);
					while(close_database() != 1)
						sleep(5);
					DBG_log(1, "%s", parm_dbid);
					while (open_database(parm_dbid) != 1) {
						DBG_log(1, "ERROR at reopen DB. continue.");
						sleep(ORDER_LOOP_DELAY);
					}
				}
				D900_caojobs_clean();
			}		
			fflush(stdout);			/* flush dbg stdout for "| tee file" see at once */
			if (G_check_stop_file (CAO_STOPFILE)) {
				break;
			}
			if (++order_loop_cnt == ORDER_LOOP_CNT) order_loop_cnt = 0;
			sleep(1);
		}
	}	
	else if ( !strcasecmp(g.do_task, "CRIMP_EXP") ) {
		if ((rlock = G_lock_process ("CRIMP_EXP")) != OK) { 	/* locked or error */
			G_terminate(1);
		}
		if ((status = D600_crimp_exp()) != OK ) {
			DBG_err(1, "CAOFORS CRIMP export ERROR. continue.");
			FONA_MSG(__LINE__, "ERROR CRIMP export");
		}

	}	else if ( !strcasecmp(g.do_task, "CAO_HU") ) {
		if ((rlock = G_lock_process ("CAO_HU")) != OK) { 	/* locked or error */
			G_terminate(1);
		}
		if ((status = D200_handling_unit()) != OK ) {
			DBG_err(1, "Handling unit export ERROR. continue.");
			FONA_MSG(__LINE__, "ERROR handling unit export");
		}
				fbwstort.firmnr = parm_firma;
		fbwstort.werknr = parm_werk;
		strcpy(fbwstort.arbgeb, "CAO_HU");
				EXEC SQL
			update  fbwstort set  aendat = :real_today
			where   firmnr = :fbwstort.firmnr
			and werknr = :fbwstort.werknr
			and arbgeb = :fbwstort.arbgeb;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "update fbwstort.lexdat");
		DBG_log(1, "magi.indi41 and magi.indi43 : updated fbwstort.lexdat = %ld.", real_today);

	}
	else if ( !strcasecmp(g.do_task, "CRIMP_UPD_MAGI") ) {
		if ((rlock = G_lock_process ("CRIMP_UPD_MAGI")) != OK) { 	/* locked or error */
			G_terminate(1);
		}
		
		fbwstort.firmnr = parm_firma;
		fbwstort.werknr = parm_werk;
		strcpy(fbwstort.arbgeb, "CAO_P1");
		
		EXEC SQL
			select * into :fbwstort from fbwstort
			where      fbwstort.firmnr = :fbwstort.firmnr
			AND        fbwstort.werknr = :fbwstort.werknr
			AND        fbwstort.arbgeb = :fbwstort.arbgeb;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "SELECT from fbwstort");
		if (SQLCODE == SQLNOTFOUND) {
			DBG_err(1, "caofors: no fbwstort F/W (%d/%d) arbgeb [%s] found",
					fbwstort.firmnr, fbwstort.werknr, fbwstort.arbgeb);
			return ERROR;
		}
		DBG_log(1, "D700_crimp_upd_magi: CAO current (last run) [fbwstort.lexdat: %ld]", fbwstort.lexdat);
#if 1
		if ((status = D700_crimp_upd_magi_custom()) != OK ) {
			DBG_err(1, "CAOFORS CRIMP update MAGI.INDI41 ERROR. continue.");
			FONA_MSG(__LINE__, "ERROR CRIMP update");
		}
#endif
#if 1
		if ((status = D700_crimp_upd_magi_wirecl()) != OK ) {
			DBG_err(1, "CAOFORS CRIMP update MAGI.INDI43 ERROR. continue.");
			FONA_MSG(__LINE__, "ERROR CRIMP update");
		}
		
		EXEC SQL
			update  fbwstort set  lexdat = :real_today
			where   firmnr = :fbwstort.firmnr
			and werknr = :fbwstort.werknr
			and arbgeb = :fbwstort.arbgeb;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "update fbwstort.lexdat");
		DBG_log(1, "magi.indi41 and magi.indi43 : updated fbwstort.lexdat = %ld.", real_today);
#endif
		/*  TEST once order create */
	} else if ( !strcasecmp(g.do_task, "ORDER_ONCE")) {
		if ((status = D300_order()) != OK ) {
			DBG_err(1, "CAOFORS ORDER ERROR.");
			FONA_MSG(__LINE__, "ERROR ORDER create");
		}

		/*  TEST once read feedback & scrap files */
	} else if ( !strcasecmp(g.do_task, "FBACK_ONCE")) {

		if ((status = D400_feedback()) != OK ) {
			DBG_err(1, "CAOFORS FEEDBACK ERROR.");
			FONA_MSG(__LINE__, "ERROR ORDER feedback");
		}
		if ((status = D800_rawmat()) != OK ) {
			DBG_err(1, "CAOFORS RAWMAT ERROR.");
			FONA_MSG(__LINE__, "ERROR ORDER RAWMAT");
		}
		if ((status = D500_scrapmat()) != OK ) {
			DBG_err(1, "CAOFORS SCRAP ERROR.");
			FONA_MSG(__LINE__, "ERROR SCRAP");
		}
		D900_caojobs_clean();

	} else {
		usage(argv_0);
		G_terminate(2);
	}
	if (status == ERROR) {
		G_terminate(1);
	}
	if (dbg_stdlevel > 1) FONA_MSG(__LINE__, "END");
	G_terminate(0);
}

/**********************************************************************
 *   Name            :  A100_prog_start
 *   Funktion        :  Vor-Verarbeitung (Dateien oeffnen, Felder ini-
 *                      tialisieren, Log-Meldungen ausgeben, Main-Auf-
 *                      ruf-Parameter einlesen und pruefen)
 *   Parameter       :  Anzahl,Adr. der Pointerleiste der Main-Param.
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int A100_prog_start(int parmc, char *parmv[])
{
	int status;                          /* Verarbeitungs-Status      */
	char buffer [255];

	for(status=0;status<=parmc;status++)
		log_ent("parm[%d] = %s",status,parmv[status]);
	/* all starts in caofors.log */
	g.mypid = getpid();
	DBG_log(1, "--- %s [A100_prog_start V.%s (%s %s)] USER: %s HOST: %s (PID: %d) TASK: %s ---",
			PROGNAME_L, prog_ver, __DATE__, __TIME__, getenv("LOGNAME"), g.hostname, g.mypid,
			(parmc>4)?parmv[4]: "NULL(MAT_TO_CAO)");

	sprintf(ext_progname, "%s", PROGNAME);
	set_defaults("FONA=Y","LOG=RS","ERROR=RS","NOTIFY=N","RUNTABLE=N","SPOOL=STD");

	DBG_log(5, "A100_prog_start: args: %d (%s, %s, %s, %s)",parmc,parmv[0],parmv[1],parmv[2],parmv[3]);

	if ((status = forssetup(parmc, parmv, BATCH)) == ERROR) {
		snprintf(buffer, sizeof(buffer),
				"CAOFORS: ERROR forsetup. wrong args Firm,Werk,DB,[Task] (%s, %s, %s, %s). "
				"Give all or nothing. See usage with -?",
				(parmc>1)?parmv[1]: "NULL", (parmc>2)?parmv[2]: "NULL",
				(parmc>3)?parmv[3]: "NULL", (parmc>4)?parmv[4]: "NULL");
		DBG_err(1, "%s", buffer);
		return ERROR;
	}
	g.prog_step = DB_OPEN; 

	/* no all into caofors.<pid>.<usr>.log, cleared */
	if ((status = reset(errlog_name,SAVE,ERRORFILE)) == ERROR) {
		return ERROR;
	}
	if ((status = reset(logfile_name,SAVE,LOGFILE)) == ERROR) {
		return ERROR;
	}
	/* (optional) arg 4 (task) and arg 5 (Scrap function)*/
	if (parmc > 4) {
		strncpy(g.do_task, parmv[4], sizeof(g.do_task));
	} else {
		strcpy(g.do_task, "MAT_P1");	/* default */
	}

	caps_on(env_user, strlen(env_user));
	if (g.no_pidlogname) {
		if (!strcasecmp(g.do_task, "MAT_P1")){
			sprintf(buffer,"%s_P1.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "MAT_P2")){
			sprintf(buffer,"%s_P2.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "ORDER_LOOP")){
			sprintf(buffer,"%s_ORDER.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "CRIMP_EXP")){
			sprintf(buffer,"%s_CRIMP.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "CRIMP_UPD_MAGI")){
			sprintf(buffer,"%s_MAGI.%s",PROGNAME_L, env_user);
		}
	} else {
		if (!strcasecmp(g.do_task, "MAT_P1")){
			sprintf(buffer,"%s_P1.%d.%s",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "MAT_P2")){
			sprintf(buffer,"%s_P2.%d.%s",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "ORDER_LOOP")){
			sprintf(buffer,"%s_ORDER.%d.%s",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "CRIMP_EXP")){
			sprintf(buffer,"%s_CRIMP.%d.%s",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "CRIMP_UPD_MAGI")){
			sprintf(buffer,"%s_MAGI.%d.%s",PROGNAME_L, g.mypid, env_user);
		}
	}
	set_logf(buffer);		/* set pbp.log_file full path */
	set_errf(buffer);		/* set pbp.err_file full path */

	if (parmc > 5) {
		strncpy(g.scrap_recfunc, parmv[5], sizeof(g.scrap_recfunc));
		log_ent("Setting scrap function to %s (parameter)",g.scrap_recfunc);
	} else {

		/* g.scrap_recfunc: default value depends on FORMGR user langage */
		pasu.firmnr = 0;
		pasu.werkli = 0;
		strcpy(pasu.userid, "VSR1"); 
		EXEC SQL 
			select sprasl into :pasu.sprasl from pasu 
			where pasu.firmnr = :pasu.firmnr
			and pasu.werkli = :pasu.werkli
			and pasu.userid = :pasu.userid;

		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "pasu.sprasl");
		if (SQLCODE == SQLNOTFOUND) {
			strcpy(g.scrap_recfunc, "SCR"); /* SCR if the FORSMGR doens't exist in pasu*/
			DBG_err(1, "caofors main: no pasu.userid = [%s] found.", pasu.userid);
			log_ent("Setting scrap function to %s (default value)",g.scrap_recfunc);
		}
		if (SQLCODE == 0) {
			/* set scrap_recfunc = SCR or SRT(de) */
			if (!strcmp(pasu.sprasl, "D")) {
				strcpy(g.scrap_recfunc, "SRT");
			} else {
				strcpy(g.scrap_recfunc, "SCR");
			}
			log_ent("Setting scrap function to %s (pasu)",g.scrap_recfunc);
		}
	}
	DBG_log(5, "A100_prog_start Args: task %s scrap function %s", g.do_task, g.scrap_recfunc);
	get_date_time(start_date, start_time);
	snprintf(g.start_msg, sizeof(g.start_msg)-1,
			"caofors V.%s (%s %s) task: %s user: %s host: %s args: %d (%s, %s, %s, %s, %s, %s)",
			prog_ver, __DATE__, __TIME__, g.do_task, env_user, g.hostname, parmc, parmv[0],
			(parmc>1)?parmv[1]: "NULL", (parmc>2)?parmv[2]: "NULL",
			(parmc>3)?parmv[3]: "NULL", (parmc>4)?parmv[4]: "NULL",
			(parmc>5)?parmv[5]: "NULL");
	DBG_log(1, "%s", g.start_msg);

	gettoday (check_date, "JJJJMMTT");
	gettimeofday (check_time, "HHMMSSSS");
	real_today = atol (check_date);
	if (g.use_today) {
		today = g.use_today;
		DBG_log(1, "TEST mode: use %ld instead today (%ld) for aendat", today, real_today);
	} else {
		today = real_today;
		DBG_log(1, "use today %ld for aendat", today);
	}
	/* sttime = atol (check_time); */
	strcpy (current_date, check_date);
	if (!g.beauty_out) {	/*  csv filename yyyymmdd-hhmmss */
		strcpy(g.timestamp_start, get_current_time(7, NULL, NULL));
	} else {			/* TEST csv filename time = 000000 */
		sprintf(g.timestamp_start, "%s-000000", get_current_time(1, NULL, NULL));
	}

#if 0
	if ((status = A130_get_environment()) == ERROR) {
		return ERROR;
	}
#endif
	/*-----------------------------------------------------------------------*/
	DBG_log(1, "Parameter: Firma     = %d", parm_firma);
	DBG_log(1, "           Werk      = %d", parm_werk);
	DBG_log(1, "           DB-ID     = %s", parm_dbid);
	DBG_log(1, "           Task      = %s", g.do_task);
	if (g.opt_wire_klassf) {
		DBG_log(1, "           Option: wire-klassf");
	}
	if (g.opt_noexec_do_bi_scrap) {
		DBG_log(1, "           Option: noexec-do-bi-scrap");
	}
	if (g.opt_apfw_teilnr[0] != '\0') {
		DBG_log(1, "           Option: apfw-teilnr=[%s]", g.opt_apfw_teilnr);
	}
	if (g.opt_matl[0] != '\0') {
		DBG_log(1, "           Option: matl_retrkz=[%s]", g.opt_matl);
	}

	return OK;
}

#if 0
/**********************************************************************
 *   Name            :  A130_get_environment
 *   Funktion        :  benoetigten Environment-Variablen einlesen
 *   Syntax-Aufruf   :  A130_get_environment
 *   Paramter        :
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int A130_get_environment(void)
{
	/*-----------------------------------  Pfad fuer Ausgabe-Datei erm. -*/
	/** TODO ?? not used */
	ptr_env = getenv("HOST_OUT");
	if (ptr_env == NULL) {
		strcpy(g.host_out, "./");
	} else {
		sprintf(g.host_out, "%s", ptr_env);
	}
	return OK;
}
#endif

/**********************************************************************
 *   Name            :   A140_create_csv_files
 *   Funktion        :   create Material CSV files
 *   			 open and store in global fd, filename
 *   			 MUST close fd before copy/delete !
 *   Parameter       :
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int A140_create_csv_files(void)
{

	DBG_log(3, "A140_create_csv_files Masterfiles");
	/* Create all csv files */
	sprintf(csv_file_leadset, "%s/Leadset_Main%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_workstep,"%s/Workstep%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_leadset_P1, "%s/Leadset_P1%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_leadset_P2, "%s/Leadset_P2%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_BOM,"%s/Bom%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_FONT,"%s/Font%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_SEAL,"%s/Seal%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_TERMINAL,"%s/Terminal%s.csv", g.tmpdir, g.timestamp_start);

	sprintf(csv_file_Wire,"%s/Wire%s.csv", g.tmpdir, g.timestamp_start);

	/* open all files */
	fdg_leadset = open_output (csv_file_leadset);
	if (fdg_leadset == - 1) {
		DBG_log(1, "ERROR: open csv_file_leadset =%s", csv_file_leadset);
		return ERROR;
	}
	fdg_workstep = open_output (csv_file_workstep);
	if (fdg_workstep == - 1) {
		DBG_log(1, "ERROR: open csv_file_workstep =%s", csv_file_workstep);
		return ERROR;
	}
	fdg_leadset_P1 = open_output (csv_file_leadset_P1);
	if (fdg_leadset_P1 == - 1) {
		DBG_log(1, "ERROR: open csv_file_leadset_P1 =%s", csv_file_leadset_P1);
		return ERROR;
	}
	fdg_leadset_P2 = open_output (csv_file_leadset_P2);
	if (fdg_leadset_P2 == - 1) {
		DBG_log(1, "ERROR: open csv_file_leadset_P2 =%s", csv_file_leadset_P2);
		return ERROR;
	}
	fdg_bom = open_output (csv_file_BOM);
	if (fdg_bom == - 1) {
		DBG_log(1, "ERROR: open csv_file_BOM =%s", csv_file_BOM);
		return ERROR;
	}
	fdg_font = open_output (csv_file_FONT);
	if (fdg_font == - 1) {
		DBG_log(1, "ERROR: open csv_file_FONT =%s", csv_file_FONT);
		return ERROR;
	}
	fdg_seal = open_output (csv_file_SEAL);
	if (fdg_seal == - 1) {
		DBG_log(1, "ERROR: open csv_file_SEAL =%s", csv_file_SEAL);
		return ERROR;
	}
	fdg_terminal = open_output (csv_file_TERMINAL);
	if (fdg_terminal == - 1) {
		DBG_log(1, "ERROR: open csv_file_TERMINAL =%s", csv_file_TERMINAL);
		return ERROR;
	}
	fdg_wire = open_output (csv_file_Wire);
	if (fdg_wire == - 1) {
		DBG_log(1, "ERROR: open csv_file_Wire =%s", csv_file_Wire);
		return ERROR;
	}
	return OK;
}

/**********************************************************************
 *   Name            :   E100_closecopy_csv_files
 *   Funktion        :   close & copy Material CSV files
 *   Parameter       :
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int E100_closecopy_csv_files(int leads_cnt)
{
	int rc = OK;
	int rc_close = 0;
	int rc_copy  = 0;
	int rc_del   = 0;

	DBG_log(1, "E100_closecopy_csv_files: new leads %d written (keeplocal:%d, dontcopy:%d)",
			leads_cnt, g.keep_files, g.dont_copy);

	/* MUST close all files before copy ! */
	rc_close += close_output(fdg_leadset, 	csv_file_leadset);
	rc_close += close_output(fdg_workstep, 	csv_file_workstep);
	rc_close += close_output(fdg_leadset_P1, 	csv_file_leadset_P1);
	rc_close += close_output(fdg_leadset_P2, 	csv_file_leadset_P2);
	rc_close += close_output(fdg_bom, 		csv_file_BOM);
	rc_close += close_output(fdg_font, 		csv_file_FONT);
	rc_close += close_output(fdg_seal, 		csv_file_SEAL);
	rc_close += close_output(fdg_terminal, 	csv_file_TERMINAL);
	rc_close += close_output(fdg_wire, 		csv_file_Wire);

	if (rc_close != 0) {		/* at least on close has error */
		DBG_log(1, "E100_closecopy_csv_files: ERROR close (%d)", rc_close);
		return ERROR;
	}
	
	if (!g.dont_copy) {
		if (leads_cnt > 0){
			rc_copy += G_copy_file(csv_file_leadset, 	g.cao_in_srv, g.cao_in_dir);
			rc_copy += G_copy_file(csv_file_workstep, 	g.cao_in_srv, g.cao_in_dir);
			rc_copy += G_copy_file(csv_file_leadset_P1,	g.cao_in_srv, g.cao_in_dir);
			rc_copy += G_copy_file(csv_file_leadset_P2,	g.cao_in_srv, g.cao_in_dir);
			rc_copy += G_copy_file(csv_file_BOM, 		g.cao_in_srv, g.cao_in_dir);
			rc_copy += G_copy_file(csv_file_FONT, 		g.cao_in_srv, g.cao_in_dir);
		}
		if(file_size (csv_file_Wire) != 0)
			rc_copy += G_copy_file(csv_file_Wire, 		g.cao_in_srv, g.cao_in_dir);
		if(file_size (csv_file_SEAL) != 0)
			rc_copy += G_copy_file(csv_file_SEAL, 		g.cao_in_srv, g.cao_in_dir);
		if(file_size (csv_file_TERMINAL) != 0) 
			rc_copy += G_copy_file(csv_file_TERMINAL, 	g.cao_in_srv, g.cao_in_dir);
	} else {
		DBG_log(1, "E100_closecopy_csv_files: Masterfiles not copied remote (new leads: %d)", leads_cnt);
	}
	if (rc_copy != 0) {		/* at least on copy has error */
		DBG_log(1, "E100_closecopy_csv_files: ERROR copy (%d)", rc_copy);
		return ERROR;
	}
	if (!g.keep_files) {
		rc_del += G_delete_file(csv_file_leadset, 0);
		rc_del += G_delete_file(csv_file_workstep, 0);
		rc_del += G_delete_file(csv_file_leadset_P1, 0);
		rc_del += G_delete_file(csv_file_leadset_P2, 0);
		rc_del += G_delete_file(csv_file_BOM, 0);
		rc_del += G_delete_file(csv_file_FONT, 0);
		rc_del += G_delete_file(csv_file_SEAL, 0);
		rc_del += G_delete_file(csv_file_TERMINAL, 0);
		rc_del += G_delete_file(csv_file_Wire, 0);
	}
	if (rc_del != 0) {		/* at least on copy has error */
		DBG_log(1, "E100_closecopy_csv_files: ERROR delete (%d)", rc_del);
		return ERROR;
	}
	return rc;
}

/**********************************************************************
 *   Name            :  D100_masterdata
 *   Funktion        :  Export all material, and write CSV files
 *   Parameter       :  mode  (not used)
 *   Return Code     :  0  : OK, but no new data
 *                      cnt: count of written leads
 *                      -1 : ERROR
 **********************************************************************/
int D100_masterdata(char mode)
{
	int status;
	int apfw_cnt = 0, apzd_cnt, apag_cnt, zeta_cnt ;
	int apfw_cnt_updated = 0;	/* updated (written) leadsets */
	int error_cnt = 0;
	FILE *fp;
	char command[100];
	char tom_date[9];
	

	DBG_log(1, "D100_masterdata Masterfile csv export");
	fbwstort.firmnr = parm_firma;
	fbwstort.werknr = parm_werk;
	//strcpy(fbwstort.arbgeb, CAO_NAME);
	if ( !strcasecmp(g.do_task, "MAT_P1") || !strcasecmp(g.do_task, "CRIMP_UPD_MAGI") ) {
		sprintf(fbwstort.arbgeb, "%s%s", CAO_NAME, "_P1");
	}
	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		sprintf(fbwstort.arbgeb, "%s%s", CAO_NAME, "_P2");
	}
			
	/* strcpy(fbwstort.arbgeb, g.cao_name); */
	EXEC SQL
		select * into :fbwstort from fbwstort
		where fbwstort.firmnr = :fbwstort.firmnr 
		and   fbwstort.werknr = :fbwstort.werknr 
		and   fbwstort.arbgeb = :fbwstort.arbgeb;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "SELECT from fbwstort");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_err(1, "caofors: no fbwstort F/W (%d/%d) arbgeb [%s] found",
				fbwstort.firmnr, fbwstort.werknr, fbwstort.arbgeb);
		return ERROR;
	}
	DBG_log(1, "D100_masterdata: CAO current (last run Masterfiles) [fbwstort.aendat: %ld] [fbwstort.loktim: %ld]", fbwstort.aendat, fbwstort.loktim);
	if (g.use_today) {		/* TEST faked today with -T yyyymmdd */
		DBG_log(1, "D100_masterdata: TEST (-T) use apzd.aendat>= %ld (instead fbwstort.aendat: %ld)",
				g.use_today, fbwstort.aendat);
	}
	
	EXEC SQL 	SELECT SUBSTR(datenf,34,4),
	     SUBSTR(datenf,38,4)
		     INTO :avtxsl_von,:avtxsl_bis
		     FROM pasy
		     WHERE firmnr = 3
		     AND   werkli = 0
		     AND   schlgr = 'LDW0'
		     AND   Schlsl = '1';
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "pasy.datenf");


	/* loop all not locked apfw.teilnr (1 row per teilnr) */
	/* ATTN: do not write global apfw, apzd structs in lower SQL calls, e.g. use apzd_1 !! */
	apfw.firmnr = parm_firma;
	apfw.plwerk = parm_werk;
	strcpy(matl_retrkz,"%");
	strcat(matl_retrkz,g.opt_matl);
	strcat(matl_retrkz,"%");
	
	if (g.use_today) {				/* TEST faked today with -T yyyymmdd */
		sql_tmplong = g.use_today;
	} else {
		sql_tmplong = real_today;		/* last run of program */
	}
	if(g.time==1){
		with_time=true;
		without_time=false;
	}
	else
	{
		with_time=false;
		without_time=true;	
	}
	
	D120_write_wire_update();
		strcpy (command, "");
		sprintf (command, "date -d '%d +1 days' ",fbwstort.aendat);
		strcat(command,"+'m%d'\n");
		fp = popen(command, "r");
		if (fp == NULL) {
			log_ent("Failed to run command '%s'", command );
			return -1;
		}
		fgets(tom_date, sizeof(tom_date), fp);
		if( atoi(tom_date) > fbwstort.aendat ){
			tomorrow=atoi(tom_date);
		}
		else
		tomorrow=	fbwstort.aendat;
	
	DBG_log(3, "D100_masterdata - APFW : [matl_retrkz : %s ] [fbwstort.aendat: %ld] [fbwstort.loktim: %ld] [tomorrow : %ld] [sql_tmplong : %ld]", matl_retrkz, fbwstort.aendat, fbwstort.loktim, tomorrow, sql_tmplong ); 
	
	if ( !strcasecmp(g.do_task, "MAT_P1") ) {
		EXEC SQL declare c_apfw_main_P1 cursor with hold for
			SELECT * INTO :apfw
			FROM (SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					AND   ((((apfw.frgdat >= :fbwstort.aendat and apfw.frguhr >= :fbwstort.loktim)or(apfw.frgdat >= :tomorrow)) and :with_time)or(apfw.frgdat >= :fbwstort.aendat and :without_time))
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					AND (matl.dataen  >= :fbwstort.aendat OR matl.datneu  >= :fbwstort.aendat )
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					AND apzd.dplcrp != ' '
					JOIN apvd
					ON apvd.firmnr = :apfw.firmnr
					AND apvd.plwerk = :apfw.plwerk
					AND apvd.teilnr = apfw.teilnr
					AND apvd.arbgnr = apzd.arbgnr
					AND ((((apvd.aendat >= :fbwstort.aendat and APVD.Aenuhr >= :fbwstort.loktim)or( apvd.aendat >= :tomorrow)) and :with_time )OR ((apvd.aendat >= :fbwstort.aendat and :without_time)) OR(((apvd.erfdat>= :fbwstort.aendat and APVD.erfuhr>= :fbwstort.loktim) OR(apvd.erfdat>= :tomorrow) )and :with_time)OR((apvd.erfdat>= :fbwstort.aendat and :without_time)))
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					JOIN maks
					ON maks.firmnr = :apfw.firmnr
					AND maks.werktl = :apfw.plwerk
					AND maks.ferweg = 1
					AND maks.kbntyp = '3'
					AND maks.teilnr = apfw.teilnr
					AND (maks.aendat >= :fbwstort.aendat OR maks.erfdat >= :fbwstort.aendat)
					AND apzd.arbgnr = maks.arbgnr
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					JOIN fotx
					ON fotx.firmnr = :apfw.firmnr
					AND fotx.txttyp = 'AG'
					AND TO_NUMBER (SUBSTR (fotx.txtshl,1,2),'99') = :apfw.plwerk
					AND TRIM (SUBSTR (fotx.txtshl,3,22)) = apfw.teilnr
					AND TO_NUMBER (SUBSTR (fotx.txtshl,27,4),'9999') = apzd.arbgnr
					AND ((((fotx.aendat >= :fbwstort.aendat and FOTX.aenuhr >= :fbwstort.loktim)or (fotx.aendat >= :tomorrow)) and :with_time)OR(fotx.aendat >= :fbwstort.aendat and :without_time))
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apzd,
					     mako,
					     apfw,
					     matl
					WHERE apfw.firmnr = :apfw.firmnr
					AND   matl.firmnr = :apfw.firmnr
					AND   matl.werknr = :apfw.plwerk
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					AND   apzd.firmnr = :apfw.firmnr
					AND   apzd.plwerk = :apfw.plwerk
					AND   mako.firmnr = :apfw.firmnr
					AND   mako.plwerk = :apfw.plwerk
					AND   mako.teilnr = apfw.teilnr
					AND   apzd.teilnr = apfw.teilnr
					AND   matl.teilnr = apfw.teilnr
					AND   matl.retrkz LIKE :matl_retrkz
					AND   ((mako.aendat = :sql_tmplong AND mako.eindat = 0 AND mako.ausdat = 0 AND MAKO.aenuhr >= :fbwstort.loktim AND :with_time)OR (mako.aendat = :sql_tmplong AND mako.eindat = 0 AND mako.ausdat = 0 AND :without_time) OR mako.eindat = :sql_tmplong)
					AND   apzd.ferweg = 1
					AND   (apzd.s1ponr = mako.stponr OR apzd.s2ponr = mako.stponr OR apzd.ltponr = mako.stponr OR apzd.z1ponr = mako.stponr OR apzd.z2ponr = mako.stponr)
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN magi
					ON magi.firmnr = :apfw.firmnr
					AND magi.teilnr = apfw.teilnr
					AND ((((magi.aendat >=:fbwstort.aendat and magi.aenzei>= :fbwstort.loktim)OR(magi.aendat >=:tomorrow)) and :with_time)OR(magi.aendat >=:fbwstort.aendat and :without_time) )
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz 
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1 
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					AND apfw.sprrkz = 0
					JOIN matl
					ON  matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz					
					JOIN apbm 
					ON apbm.firmnr = :apfw.firmnr
					AND apbm.teilnr = apfw.teilnr 
					AND apbm.arbgnr =apzd.arbgnr
					where (( ((apbm.aendat >=:fbwstort.aendat and apbm.aenuhr>= :fbwstort.loktim)or(apbm.aendat >=:tomorrow) )and :with_time) OR ( apbm.aendat >=:fbwstort.aendat  and :without_time)	)	
					ORDER BY teilnr) AS selection;
					
		
	}
	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		EXEC SQL declare c_apfw_main_P2 cursor with hold for
			SELECT * INTO :apfw
			FROM (SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					AND   apfw.frgdat >= :fbwstort.aendat
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					AND apzd.aendat >= :fbwstort.aendat
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					AND apzd.dplcrp != ' '
					JOIN apvd
					ON apvd.firmnr = :apfw.firmnr
					AND apvd.plwerk = :apfw.plwerk
					AND apvd.teilnr = apfw.teilnr
					AND apvd.arbgnr = apzd.arbgnr
					AND (apvd.aendat >= :fbwstort.aendat OR apvd.erfdat>= :fbwstort.aendat)
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					JOIN maks
					ON maks.firmnr = :apfw.firmnr
					AND maks.werktl = :apfw.plwerk
					AND maks.ferweg = 1
					AND maks.kbntyp = '3'
					AND maks.teilnr = apfw.teilnr
					AND (maks.aendat >= :fbwstort.aendat OR maks.erfdat >= :fbwstort.aendat)
					AND apzd.arbgnr = maks.arbgnr
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN matl
					ON matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					JOIN fotx
					ON fotx.firmnr = :apfw.firmnr
					AND fotx.txttyp = 'AG'
					AND TO_NUMBER (SUBSTR (fotx.txtshl,1,2),'99') = :apfw.plwerk
					AND TRIM (SUBSTR (fotx.txtshl,3,22)) = apfw.teilnr
					AND TO_NUMBER (SUBSTR (fotx.txtshl,27,4),'9999') = apzd.arbgnr
					AND fotx.aendat >= :fbwstort.aendat
					WHERE apfw.firmnr = :apfw.firmnr
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apzd,
					     mako,
					     apfw,
					     matl
					WHERE apfw.firmnr = :apfw.firmnr
					AND   matl.firmnr = :apfw.firmnr
					AND   matl.werknr = :apfw.plwerk
					AND   apfw.plwerk = :apfw.plwerk
					AND   apfw.sprrkz = 0
					AND   apfw.ferweg = 1
					AND   apzd.firmnr = :apfw.firmnr
					AND   apzd.plwerk = :apfw.plwerk
					AND   mako.firmnr = :apfw.firmnr
					AND   mako.plwerk = :apfw.plwerk
					AND   mako.teilnr = apfw.teilnr
					AND   apzd.teilnr = apfw.teilnr
					AND   matl.teilnr = apfw.teilnr
					AND   matl.retrkz LIKE :matl_retrkz
					AND   ((mako.aendat = :sql_tmplong AND mako.eindat = 0 AND mako.ausdat = 0) OR mako.eindat = :sql_tmplong)
					AND   apzd.ferweg = 1
					UNION
					SELECT apfw.*
					FROM apfw
					JOIN apzd
					ON apzd.firmnr = :apfw.firmnr
					AND apzd.plwerk = :apfw.plwerk
					AND apzd.teilnr = apfw.teilnr
					AND apzd.ferweg = 1
					AND apfw.sprrkz = 0
					JOIN matl
					ON  matl.firmnr = :apfw.firmnr
					AND matl.werknr = :apfw.plwerk
					AND matl.teilnr = apfw.teilnr
					AND matl.retrkz LIKE :matl_retrkz
					JOIN apbm 
					ON  apbm.firmnr = :apfw.firmnr
					AND apbm.teilnr = apfw.teilnr 
					AND apbm.arbgnr = apzd.arbgnr
					where apbm.aendat >=:fbwstort.aendat
					ORDER BY teilnr) AS selection;
	}

	if ( !strcasecmp(g.do_task, "MAT_P1") ) {
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apfw_main_P1");
		EXEC SQL open c_apfw_main_P1;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apfw_main_P1");
	}
	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apfw_main_P2");
		EXEC SQL open c_apfw_main_P2;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apfw_main_P2");
	}
	g.use_today = 0; 

	do {

		/* get apfw.teilnr  */
		if ( !strcasecmp(g.do_task, "MAT_P1") ) {
			EXEC SQL fetch c_apfw_main_P1;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apfw_main_P1");
		}
		else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
			EXEC SQL fetch c_apfw_main_P2;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apfw_main_P2");
		}
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(3, "D100_masterdata: apfw fetch end (found total: %d)", apfw_cnt);
			break;
		}
		apfw_cnt++;
		if (g.opt_apfw_teilnr[0] != '\0' && strcmp(g.opt_apfw_teilnr, apfw.teilnr)) {
			DBG_log(3, "D100_masterdata: ======= apfw.teilnr (%d) [%s] SKIPed (option: apfw-teilnr=%s) =======",
					apfw_cnt, apfw.teilnr, g.opt_apfw_teilnr);
			continue;
		} else {
			DBG_log(3, "D100_masterdata: ======= apfw.teilnr (%d) [%s] aendat: %ld =======",
					apfw_cnt, apfw.teilnr, apfw.aendat);
			/*DBG_log(2, "D100_masterdata: ======= apfw.teilnr (%d) [%s] aendat: %ld =======",
			  apfw_cnt, apfw.teilnr, apfw.aendat);*///I-367048
		}

		/* get magd for apfw.teilnr  */
		EXEC SQL
			select * into :magd from magd
			where      magd.firmnr = :apfw.firmnr
			and magd.teilnr = :apfw.teilnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd.teilnr");
		DBG_log(4, "D100_masterdata: magd.tnrbe1 [%s] tnrkbz [%s]", magd.tnrbe1, magd.tnrkbz);


		/* loop all apzd for this apfw.teilnr (arbgnr rows per teilnr)
		 * lastrun == fbwstort.aendat:
		 *   if apfw.aendat >= lastrun -> use all apzd (ignore aendat)
		 *   if apfw.aendat <  lastrun -> use only apzd.aendat >=lastrun
		 */
		apzd.firmnr = parm_firma;
		apzd.plwerk = parm_werk;
		apzd_cnt = 0;
		zeta_cnt = 0;
		apag.firmnr = parm_firma;
		apag.plwerk = parm_werk;
		apag_cnt = 0;
		if (g.use_today) {				/* TEST faked today with -T yyyymmdd */
			sql_tmplong = g.use_today;
		} else {
			sql_tmplong = fbwstort.aendat;		/* last run of program */
		}
		/* apfw was changed (e.g. sperrkz), then use all apzd, or '-o apfw-teilnr=XXX' force to export ignore aendat */
		if (apfw.aendat >= sql_tmplong || g.opt_apfw_teilnr[0] != '\0') {
			sql_tmplong = 0;					/* ignore apzd.aendat */ 
		}
		//to be changed

		if ( !strcasecmp(g.do_task, "MAT_P1") ) {

			strcpy(p_Leadset_lsm," ");
			zeta = 'N';
			if (zeta == 'N'){
				if(g.time==1){
		with_time=true;
		without_time=false;
	}
	else
	{
		with_time=false;
		without_time=true;	
	}
	DBG_log(3, "D100_masterdata - APZD : [matl_retrkz : %s ] [fbwstort.aendat: %ld] [fbwstort.loktim: %ld] [tomorrow : %ld] [sql_tmplong : %ld]  [real_today : %ld] ", matl_retrkz, fbwstort.aendat, fbwstort.loktim, tomorrow, sql_tmplong, real_today );
	
				EXEC SQL declare c_apzd_1 cursor with hold for
					SELECT * INTO :apzd
					FROM (SELECT *
							FROM apzd
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							AND   ((((apzd.aendat >= :sql_tmplong AND   APZD.aenuhr >= :fbwstort.loktim)OR(apzd.aendat >= :tomorrow)) and :with_time)OR (apzd.aendat >= :sql_tmplong and :without_time))
							UNION
							SELECT apzd.*
							FROM apzd
							JOIN matl
							ON matl.firmnr = :apzd.firmnr
							AND matl.werknr = :apzd.plwerk
							AND matl.teilnr = :apfw.teilnr
							AND (matl.dataen  >= :fbwstort.aendat OR matl.datneu  >= :fbwstort.aendat )
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							UNION
							SELECT apzd.*
							FROM apzd
							JOIN apvd
							ON apvd.firmnr = :apzd.firmnr
							AND apvd.plwerk = :apzd.plwerk
							AND apvd.teilnr = :apfw.teilnr
							AND apvd.arbgnr = apzd.arbgnr
							AND ((((apvd.aendat >= :sql_tmplong and apvd.aenuhr >= :fbwstort.loktim )or(apvd.aendat >= :tomorrow))and :with_time) OR (apvd.aendat >= :sql_tmplong and :without_time) OR (((apvd.erfdat >= :sql_tmplong and APVD.erfuhr>= :fbwstort.loktim)OR(apvd.erfdat >= :tomorrow)) and :with_time)OR(apvd.erfdat >= :sql_tmplong and :without_time))
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							AND   apzd.dplcrp != ' '
							UNION
							SELECT apzd.*
							FROM apzd
							JOIN maks
							ON maks.firmnr = :apzd.firmnr
							AND maks.werktl = :apzd.plwerk
							AND maks.ferweg = 1
							AND maks.kbntyp = '3'
							AND maks.teilnr = :apfw.teilnr
							AND (maks.aendat >= :sql_tmplong OR maks.erfdat >= :fbwstort.aendat)
							AND apzd.arbgnr = maks.arbgnr
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							UNION
							SELECT apzd.*
							FROM apzd
							JOIN fotx
							ON fotx.firmnr = :apzd.firmnr
							AND fotx.txttyp = 'AG'
							AND TO_NUMBER (SUBSTR (fotx.txtshl,1,2),'99') = :apzd.plwerk
							AND TRIM (SUBSTR (fotx.txtshl,3,22)) = :apfw.teilnr
							AND TO_NUMBER (SUBSTR (fotx.txtshl,27,4),'9999') = apzd.arbgnr
							AND ((((fotx.aendat >= :sql_tmplong and FOTX.aenuhr >= :fbwstort.loktim)or(fotx.aendat >= :tomorrow)) and :with_time)OR(fotx.aendat >= :sql_tmplong and :without_time))
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							UNION
							SELECT apzd.*
							FROM apzd,mako
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   mako.firmnr = :apzd.firmnr
							AND   mako.plwerk = :apzd.plwerk
							AND   mako.teilnr = :apfw.teilnr
							AND   apzd.teilnr = :apfw.teilnr
							AND   ((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND MAKO.aenuhr>=:fbwstort.loktim and :with_time)OR((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND :without_time)) OR mako.eindat = :real_today)
							AND   apzd.ferweg = 1
							AND   (apzd.s1ponr = mako.stponr OR apzd.s2ponr = mako.stponr OR apzd.ltponr = mako.stponr OR apzd.z1ponr = mako.stponr OR apzd.z2ponr = mako.stponr)
							UNION
							SELECT apzd.*
							from apzd
							JOIN magi
							ON magi.firmnr = :apzd.firmnr
							AND magi.teilnr = apzd.teilnr
							AND ((((magi.aendat >=:fbwstort.aendat and Magi.aenzei>= :fbwstort.loktim)or (magi.aendat >=:tomorrow)) and :with_time)OR(magi.aendat >=:fbwstort.aendat and :without_time))
							WHERE apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							UNION
							SELECT apzd.*
							from apzd
							join apbm 
							ON apbm.firmnr = :apzd.firmnr
							AND apbm.teilnr = :apfw.teilnr 
							AND apbm.arbgnr =apzd.arbgnr
							where (( ((apbm.aendat >=:fbwstort.aendat and apbm.aenuhr>= :fbwstort.loktim) OR (apbm.aendat >=:tomorrow))and :with_time)OR ( apbm.aendat >=:fbwstort.aendat and :without_time) )
							and apzd.firmnr = :apzd.firmnr
							AND   apzd.plwerk = :apzd.plwerk
							AND   apzd.teilnr = :apfw.teilnr
							AND   apzd.ferweg = 1
							ORDER BY arbgnr) AS result;
							
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apzd_1");
				EXEC SQL open c_apzd_1;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apzd_1");

				do {
					A150_check_logfile();
					EXEC SQL fetch c_apzd_1;
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apzd_1");
					if (SQLCODE == SQLNOTFOUND) {
						DBG_log(3, "D100_masterdata: apzd fetch end %s (found total: %d)", apfw.teilnr, apzd_cnt);
						break;
					}
					apzd_cnt++;
					DBG_log(4, "D100_masterdata: ==== LOOP apzd.teilnr [%s] arbgnr: [%d] dplcrp [%s]",
							apfw.teilnr, apzd.arbgnr, apzd.dplcrp);

					/* get apag for apzd.teilnr & arbgnr  */
					EXEC SQL
						select * into :apag from apag
						where       apag.firmnr = :apzd.firmnr
						and apag.plwerk = :apzd.plwerk
						and apag.teilnr = :apzd.teilnr
						and apag.ferweg = 1		/* CAO_FERWEG */
						and apag.arbgal = 0
						and apag.arbgnr = :apzd.arbgnr;
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apag.teilnr");
					DBG_log(4, "D100_masterdata: apag.avtxsl [%s]", apag.avtxsl);

					/* get maks for apzd.teilnr & arbgnr */
					strcpy(maks.teilnr, "");		/* clear if row not exist */
					maks.firmnr = 0;			/* remember if row not exist */
					EXEC SQL
						select * into :maks from maks
						where       maks.firmnr = :apzd.firmnr
						and maks.werktl = :apzd.plwerk		/* or bewerk ? TODO ? */
						-- and maks.teilnr = :apzd.teilnr
						and maks.ferweg = 1		/* CAO_FERWEG */
						and maks.kbntyp = '3'		/* ?? */
						and maks.kabgrp = :apzd.teilnr || '-' || :apzd.arbgnr ;
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "maks.teilnr");
					if (SQLCODE == SQLNOTFOUND) {
						DBG_log(3, "D100_masterdata: WARN maks (F/W: %d/%d) not exist for teilnr [%s] arbgnr:%d kbntyp:3",
								apzd.firmnr, apzd.plwerk, apzd.teilnr, apzd.arbgnr);
					}
					DBG_log(4, "D100_masterdata: maks.teilnr [%s] arbgnr %d kabgrp [%s] anzkan: %d",
							maks.teilnr, maks.arbgnr,  maks.kabgrp, maks.anzkan);

					/* write leadset files */
					if ((status = D110_write_leadset()) != OK) {
						DBG_err(1, "D100_masterdata: ERROR write master data leadset [teilnr:%s, arbgnr:%d]",
								apfw.teilnr, apzd.arbgnr);
						error_cnt++;
						continue;
					}

				} while (1);	/* c_apzd_1 */
				EXEC SQL close c_apzd_1;

				DBG_log(1, "D100_masterdata:   updated %d apzd.parts [%s]", apzd_cnt, apfw.teilnr);
				if (apzd_cnt > 0) apfw_cnt_updated++;		/* at least one part of leadset changed */
			}

			strcpy(p_Leadset_ws," ");
			strcpy(p_Leadset_bom," ");
			zeta = 'Y';
			if (zeta == 'Y'){
				EXEC SQL declare c_apag_2 cursor with hold for
					select * into :apag from apag
					where       apag.firmnr = :apag.firmnr
					and apag.plwerk = :apag.plwerk
					and apag.teilnr = :apfw.teilnr
					and apag.ferweg = 1			/* CAO_FERWEG */
					and avtxsl = '1045'
					--and apag.aendat >= :sql_tmplong
					order by arbgnr;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apag_2");
				EXEC SQL open c_apag_2;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apag_2");

				do {
					A150_check_logfile();
					EXEC SQL fetch c_apag_2;
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apag_2");
					if (SQLCODE == SQLNOTFOUND) {
						DBG_log(3, "D100_masterdata: apag fetch end %s (found total: %d)", apfw.teilnr, zeta_cnt);
						break;
					}
					zeta_cnt++;
					DBG_log(4, "D100_masterdata: ==== LOOP apag.teilnr [%s] arbgnr: [%d] avtxsl [%s]",
							apfw.teilnr, apag.arbgnr, apag.avtxsl);

					/* write leadset files */
					if ((status = D110_write_leadset()) != OK) {
						DBG_err(1, "D100_masterdata: ERROR write master data leadset [teilnr:%s, arbgnr:%d]",
								apfw.teilnr, apag.arbgnr);
						error_cnt++;
						continue;
					}

				} while (1);	/* c_apag_2 */
				EXEC SQL close c_apag_2;

				DBG_log(1, "D100_masterdata:   updated %d apag.parts [%s]", zeta_cnt, apfw.teilnr);
				if (zeta_cnt > 0) apfw_cnt_updated++;		/* at least one part of leadset changed */
			}
		}

		if ( !strcasecmp(g.do_task, "MAT_P2") ) {
			EXEC SQL declare c_apag_1 cursor with hold for
				SELECT * INTO :apag
				FROM (SELECT *
						FROM apag
						WHERE apag.firmnr = :apag.firmnr
						AND   apag.plwerk = :apag.plwerk
						AND   apag.teilnr = :apfw.teilnr
						AND   apag.ferweg = 1
						AND   apag.zaufnr IS NOT NULL
						AND    apag.zaufnr !=''
						AND   apag.avtxsl between :avtxsl_von and :avtxsl_bis
						AND   apag.aendat >= :sql_tmplong
						UNION
						SELECT apag.*
						FROM   apag,mako
						WHERE  apag.firmnr = :apag.firmnr
						AND    apag.plwerk = :apag.plwerk
						AND    mako.firmnr = :apag.firmnr
						AND    mako.plwerk = :apag.plwerk
						AND    mako.teilnr = :apfw.teilnr
						AND    apag.teilnr = :apfw.teilnr
						AND    apag.zaufnr IS NOT NULL
						AND    apag.zaufnr !=''
						AND   apag.avtxsl between :avtxsl_von and :avtxsl_bis
						AND    ((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0) OR mako.eindat = :real_today)
						AND    apag.ferweg = 1
						ORDER BY arbgnr) AS result;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apag_1");
			EXEC SQL open c_apag_1;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apag_1");

			do {
				A150_check_logfile();
				EXEC SQL fetch c_apag_1;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apag_1");
				if (SQLCODE == SQLNOTFOUND) {
					DBG_log(3, "D100_masterdata: apag fetch end %s (found total: %d)", apfw.teilnr, apag_cnt);
					break;
				}
				apag_cnt++;
				DBG_log(4, "D100_masterdata: ==== LOOP apag.teilnr [%s] arbgnr: [%d] avtxsl [%s]",
						apfw.teilnr, apag.arbgnr, apag.avtxsl);

				/* write leadset files */
				if ((status = D110_write_leadset()) != OK) {
					DBG_err(1, "D100_masterdata: ERROR write master data leadset [teilnr:%s, arbgnr:%d]",
							apfw.teilnr, apag.arbgnr);
					error_cnt++;
					continue;
				}

			} while (1);	/* c_apag_1 */
			EXEC SQL close c_apag_1;

			DBG_log(1, "D100_masterdata:   updated %d apag.parts [%s]", apag_cnt, apfw.teilnr);
			if (apag_cnt > 0) apfw_cnt_updated++;		/* at least on part of leadset changed */
		}
	} while (1);	/* c_apfw_main */

	if ( !strcasecmp(g.do_task, "MAT_P1") ) {
		EXEC SQL close c_apfw_main_P1;
	}
	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		EXEC SQL close c_apfw_main_P2;
	}

	if (error_cnt) {		/* at least one lead failed */
		DBG_err(1, "CAOFORS ERROR masterdata failed for %d leadsets (see log [%s]).", error_cnt, errlog_name);
		FONA_MSG(__LINE__, "ERROR masterdata failed %d leads", error_cnt);
	}
	/* write independant CSV files */
	if ((status = D118_write_font()) != OK) {
		DBG_err(3, "D100_masterdata: ERROR return D118_write_font");
		return ERROR;
	}
	gettimestamp(&pbp,&pbpdat,&msg);
	
	/* TIME condition : parts updated while running the interface should be taken into consideration (update fbwstort.loktim by interface start time) */
	//gettimeofday(check_time,"HHMMSSSS");
	sttime   = atol(check_time);
	/*update fbwstort.aendat = today  (remember this last run, to only update newer parts) */
	EXEC SQL
		update fbwstort set  aendat = :real_today,  loktim =  :sttime 
		where fbwstort.firmnr = :fbwstort.firmnr
		and   fbwstort.werknr = :fbwstort.werknr
		and   fbwstort.arbgeb = :fbwstort.arbgeb;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "update fbwstort.aendat & fbwstort.loktim");
	DBG_log(1, "D100_masterdata: updated fbwstort.aendat = %ld fbwstort.loktim = %ld.", real_today,sttime);
	DBG_log(1, "D100_masterdata: written %d leadsets (errors: %d).", apfw_cnt_updated, error_cnt);
	return apfw_cnt_updated;
}

/**********************************************************************
 *   Name            :  D110_write_leadset
 *   Funktion        :  write leadset files for  teilnr & arbgnr
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   				fbwstort, apfw, magd    (teilnr)
 *   				apzd, apag, maks        (teilnr/arbgnr)
 *		use 2.nd structs for further selects
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D110_write_leadset(void)
{
	int rc = OK;
	char cable_type = '-';
	rec_leadset_main_t rec_leadset_main;

	if ( !strcasecmp(g.do_task, "MAT_P1") ) {

		DBG_log(3, "D110_write_leadset: apfw.teilnr [%s] apzd.arbgnr:%d magd.tnrbe1: [%s]",
				apfw.teilnr, apzd.arbgnr, magd.tnrbe1);

		if (zeta == 'N'){
			/* get mako_ltg1  (= apzd.ltponr) */
			EXEC SQL
				select * into :mako_ltg1 from mako
				where        mako.firmnr = :apzd.firmnr
				and  mako.plwerk = :apzd.plwerk
				and  mako.teilnr = :apzd.teilnr
				and  mako.stponr = :apzd.ltponr
				and (mako.eindat <= :real_today or mako.eindat = 0)
				and (mako.ausdat >= :real_today or mako.ausdat = 0);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg1")
				DBG_log(4, "D110_write_leadset: mako_ltg1.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]",
						mako_ltg1.teilnr, mako_ltg1.komtnr, mako_ltg1.stponr, mako_ltg1.matnum);

			kill_all_blanks(apzd.dplcrp);		/* if only spaces, SQLlib returns " " */
			// Check for Double crimp  
			if(strlen(apzd.dplcrp) > 0) {
				//apvd record => twist				
				// in the table apvd arbgnr = 1.st wire 
				//                   agnr01 = 2.nd wire  
				// Check if the 1.st or 2.nd wire has been changed  
				// JOIN on apvd V.arbgnr = Z.arbgnr ==> 1.st wire 
				EXEC SQL
				SELECT V.arbgnr, V.agnr01 into :arbgnr_ltg1 , :arbgnr_ltg2
                FROM   mako M 
                JOIN   apzd Z
                ON Z.firmnr = M.firmnr AND Z.plwerk = M.plwerk AND Z.ltponr = M.stponr  AND Z.ferweg = 1 and Z.teilnr = M.teilnr 
                JOIN apvd V 
                ON M.firmnr = V.firmnr AND M.plwerk = V.plwerk AND V.arbgnr = Z.arbgnr AND V.teilnr = M.teilnr
				WHERE    M.firmnr = :apzd.firmnr
					AND  M.plwerk = :apzd.plwerk
					AND  M.teilnr = :apzd.teilnr							
					AND  M.stponr = :apzd.ltponr
				    AND (M.eindat <= :real_today or M.eindat = 0)
				    AND (M.ausdat >= :real_today or M.ausdat = 0);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apvd.arbgnr")
				DBG_log(4, "D110_write_leadset: Check if the 1.st or 2.nd wire has been changed ");
                if (SQLCODE == SQLNOTFOUND) { // 2.st wire has been changed THEN : 
				    // - get 1.st wire from apvd (JOIN on apvd V.agnr01 = Z.arbgnr ==> 2.st wire)
					// - get mako_ltg1 from mako with mako.eindat,mako.ausdat equal to last change 				    				
				    EXEC SQL
				    SELECT V.arbgnr, V.agnr01 into :arbgnr_ltg1 , :arbgnr_ltg2
                    from mako M 
                    JOIN apzd Z
                    ON Z.firmnr = M.firmnr AND Z.plwerk = M.plwerk AND Z.ltponr = M.stponr  AND Z.ferweg = 1 AND Z.teilnr = M.teilnr 
                    JOIN apvd V 
                    ON M.firmnr = V.firmnr AND M.plwerk = V.plwerk AND V.agnr01 = Z.arbgnr AND V.teilnr = M.teilnr
				    WHERE    M.firmnr = :apzd.firmnr
				    	AND  M.plwerk = :apzd.plwerk
				    	AND  M.teilnr = :apzd.teilnr							
				    	AND  M.stponr = :apzd.ltponr
				        AND (M.eindat <= :real_today or M.eindat = 0)
				        AND (M.ausdat >= :real_today or M.ausdat = 0);
				    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "arbgnr_ltg1")
				    DBG_log(4, "D110_write_leadset: get 1.st wire from apvd? arbgnr_ltg1:%d",arbgnr_ltg1);
/*LD02*/					
					if (SQLCODE == SQLNOTFOUND) { //double crimp (no apvd record)
						arbgnr_ltg1 = apzd.arbgnr;
						EXEC SQL
							SELECT apzd.*  into :apzd 
							FROM   apzd
							WHERE  apzd.firmnr = :apzd.firmnr
							AND apzd.plwerk = :apzd.plwerk
							AND apzd.teilnr = :apzd.teilnr
							AND apzd.ferweg = 1
							AND apzd.arbgnr = :arbgnr_ltg1;
						SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd.arbgnr and apzd.ltponr")
						EXEC SQL
							SELECT * into :mako_ltg1 from mako
							WHERE mako.firmnr  = :apzd.firmnr
							AND   mako.plwerk  = :apzd.plwerk
							AND   mako.teilnr  = :apzd.teilnr
							AND   mako.stponr  = :apzd.ltponr
							AND  (( mako.eindat != 0 and mako.ausdat  = 0 ) OR  ( mako.eindat = 0 and mako.ausdat  = 0 ) );	
						SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg1")
						DBG_log(4, "D110_write_leadset - DoubleCrimp: mako_ltg1.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]", mako_ltg1.teilnr, mako_ltg1.komtnr, mako_ltg1.stponr, mako_ltg1.matnum);
						
						kill_all_blanks(mako_ltg1.matnum);	
						
						EXEC SQL
							SELECT apzd.arbgnr  into :arbgnr_ltg2 
							FROM   apzd
							WHERE  apzd.firmnr = :apzd.firmnr
							AND apzd.plwerk = :apzd.plwerk
							AND apzd.teilnr = :apzd.teilnr
							AND apzd.ferweg = 1
							AND apzd.dplcrp = :mako_ltg1.matnum; 
						SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd.arbgnr and apzd.ltponr")

						if ((rc = D1101_Wire_Written(arbgnr_ltg2)) != ERROR) {
				            if ( (strlen(apzd.dplcrp) > 0) && (arbgnr_ltg1 > arbgnr_ltg2)) {
								DBG_log(1, "D110_write_leadset - DC: SKIP 2.nd wire apzd.arbgnr [%d] apzd.agnr01 [%d]", arbgnr_ltg1, arbgnr_ltg2);
													return OK;
				            	}							
						}

					}
/*LD02*/					
				    // Check if the 1.st wire has been treated by checking the apzd join to avoid double writing the wire in the file BOMxxx.csv
                    /*
					EXEC SQL
                        SELECT apzd.* 
				        FROM apzd,mako
				        WHERE apzd.firmnr = :apzd.firmnr
				        AND   apzd.plwerk = :apzd.plwerk
				        AND   mako.firmnr = :apzd.firmnr
				        AND   mako.plwerk = :apzd.plwerk
				        AND   mako.teilnr = :apzd.teilnr
				        AND   apzd.teilnr = :apzd.teilnr
				        AND   ((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND mako.aenuhr>=:fbwstort.loktim AND        :with_time)OR((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND :without_time))  OR mako.eindat = :real_today)
				        AND   apzd.ferweg = 1
				        AND   (apzd.s1ponr = mako.stponr OR apzd.s2ponr = mako.stponr OR apzd.ltponr = mako.stponr OR apzd.z1ponr = mako.stponr OR apzd.z2ponr = mako.stponr)
				        AND apzd.arbgnr = :arbgnr_ltg1;
				    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd")
										
				    DBG_log(4, "D110_write_leadset: apzd 1.st wire is written ");				
				    if (SQLCODE == SQLNOTFOUND) { // 1.st wire doesn't exist in apzd selection so search for mako_ltg1 by skippind real_today
				        EXEC SQL
				        	 SELECT apzd.*  into :apzd 
				        	 FROM   apzd
				        	 WHERE  apzd.firmnr = :apzd.firmnr
				        	 AND apzd.plwerk = :apzd.plwerk
				        	 AND apzd.teilnr = :apzd.teilnr
				        	 AND apzd.ferweg = 1
				        	 AND apzd.arbgnr = :arbgnr_ltg1;
				        SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd.arbgnr and apzd.ltponr")
				        // get mako_ltg1  (= apzd.ltponr) 
				        EXEC SQL
				        	 SELECT * into :mako_ltg1 from mako
				        	 WHERE mako.firmnr  = :apzd.firmnr
				        	 AND   mako.plwerk  = :apzd.plwerk
				        	 AND   mako.teilnr  = :apzd.teilnr
				        	 AND   mako.stponr  = :apzd.ltponr
				        	 AND   mako.eindat != 0
				        	 AND   mako.ausdat  = 0;
				        SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg1")
				        	DBG_log(4, "D110_write_leadset: mako_ltg1.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]",
				        			mako_ltg1.teilnr, mako_ltg1.komtnr, mako_ltg1.stponr, mako_ltg1.matnum);
				        			
				        // only Single-Wires or 1.st wire from double-wire  ("CSD-1" < "CSD-2") 
				        if ( (strlen(apzd.dplcrp) > 0) && (strcmp(mako_ltg1.matnum, apzd.dplcrp) > 0)) {
				            DBG_log(4, "D110_write_leadset: SKIP 2.nd wire apzd.dplcrp [%s] mako_ltg1.matnum [%s]",
				                              apzd.dplcrp, mako_ltg1.matnum);
				                        return OK;
				        	}	
				    }
					*/
					else {
					  if ((rc = D1101_Wire_Written(arbgnr_ltg1)) == ERROR) {
				        EXEC SQL
				            	 SELECT apzd.*  into :apzd 
				            	 FROM   apzd
				            	 WHERE  apzd.firmnr = :apzd.firmnr
				            	 AND apzd.plwerk = :apzd.plwerk
				            	 AND apzd.teilnr = :apzd.teilnr
				            	 AND apzd.ferweg = 1
				            	 AND apzd.arbgnr = :arbgnr_ltg1;
				            SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd.arbgnr and apzd.ltponr")
				            /* get mako_ltg1  (= apzd.ltponr) */
				            EXEC SQL
				            	 SELECT * into :mako_ltg1 from mako
				            	 WHERE mako.firmnr  = :apzd.firmnr
				            	 AND   mako.plwerk  = :apzd.plwerk
				            	 AND   mako.teilnr  = :apzd.teilnr
				            	 AND   mako.stponr  = :apzd.ltponr
				            	 AND  (( mako.eindat != 0 and mako.ausdat  = 0 ) OR  ( mako.eindat = 0 and mako.ausdat  = 0 ) );
								 
				            SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg1")
				            	DBG_log(4, "D110_write_leadset - Twist: mako_ltg1.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]",
				            			mako_ltg1.teilnr, mako_ltg1.komtnr, mako_ltg1.stponr, mako_ltg1.matnum);
							
				            /* only Single-Wires or 1.st wire from double-wire  ("CSD-1" < "CSD-2") */
/*LD01*/
/*				            if ( (strlen(apzd.dplcrp) > 0) && (strcmp(mako_ltg1.matnum, apzd.dplcrp) > 0)) {
				                DBG_log(4, "D110_write_leadset: SKIP 2.nd wire apzd.dplcrp [%s] mako_ltg1.matnum [%s]",
				                                  apzd.dplcrp, mako_ltg1.matnum);
				                            return OK;
				            	}	*/
				            if ( (strlen(apzd.dplcrp) > 0) && (arbgnr_ltg1 > arbgnr_ltg2)) {
								DBG_log(1, "D110_write_leadset: SKIP 2.nd wire apzd.arbgnr [%d] apzd.agnr01 [%d]", arbgnr_ltg1, arbgnr_ltg2);
													return OK;
				            	}
/*LD01*/								
			           }
                       else
				       {
				    	 DBG_log(4, "D110_write_leadset: 1.st has been written apzd.teilnr[%s], apzd.ltponr:%d, apzd.dplcrp[%s]",
						            apzd.teilnr,apzd.ltponr,apzd.dplcrp);
				         return OK;				
				       }
					}
				}
				else  // 1.st wire has changed THEN get mako with mako.eindat,mako.ausdat equal to real_today
				{    
					/* get mako_ltg1  (= apzd.ltponr) */
			        EXEC SQL
			        	select * into :mako_ltg1 from mako
			        	where        mako.firmnr = :apzd.firmnr
			        	and  mako.plwerk = :apzd.plwerk
			        	and  mako.teilnr = :apzd.teilnr
			        	and  mako.stponr = :apzd.ltponr
			        	and (mako.eindat <= :real_today or mako.eindat = 0)
			        	and (mako.ausdat >= :real_today or mako.ausdat = 0);
			        SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg1")
			        	DBG_log(4, "D110_write_leadset: mako_ltg1.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]",
			        			mako_ltg1.teilnr, mako_ltg1.komtnr, mako_ltg1.stponr, mako_ltg1.matnum);
			        
			        kill_all_blanks(apzd.dplcrp);		/* if only spaces, SQLlib returns " " */
					/* only Single-Wires or 1.st wire from double-wire  ("CSD-1" < "CSD-2") */
/*LD01*/
/*					if ( (strlen(apzd.dplcrp) > 0) && (strcmp(mako_ltg1.matnum, apzd.dplcrp) > 0)) {
					    DBG_log(4, "D110_write_leadset: SKIP 2.nd wire apzd.dplcrp [%s] mako_ltg1.matnum [%s]",
					                      apzd.dplcrp, mako_ltg1.matnum);
				                 return OK;
						}	*/
					if ( (strlen(apzd.dplcrp) > 0) && (arbgnr_ltg1 > arbgnr_ltg2)) {
					    DBG_log(1, "D110_write_leadset: SKIP 2.nd wire apzd.arbgnr [%d] apzd.agnr01 [%d]", arbgnr_ltg1, arbgnr_ltg2);
				                 return OK;
						}
/*LD01*/						
				}
			}
			
			/* get magd_ltg1   */
			EXEC SQL
				select * into :magd_ltg1 from magd
				where        magd.firmnr = :apzd.firmnr
				and  magd.teilnr = :mako_ltg1.komtnr;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd_ltg1")
				DBG_log(4, "D110_write_leadset: magd_ltg1.teilnr [%s] tnrkbz [%s] tnrbe1 [%s]",
						magd_ltg1.teilnr, magd_ltg1.tnrkbz, magd_ltg1.tnrbe1);

			/* cable type */
			if ( (strlen(apzd.dplcrp) > 0)) {		/* double or twisted wire */
				DBG_log(4, "D110_write_leadset: apzd.dplcrp [%s] ltponr:%d s1/2meng %f / %f",
						apzd.dplcrp, apzd.ltponr, apzd.s1meng, apzd.s2meng);
				if ((apzd.s1meng == 0.5) || (apzd.s2meng == 0.5)) {
					cable_type = 'D';
					if (apzd.s1meng == 0.5) {		/* always put double crimped wire ends on side 2 */
						DBG_log(4, "D110_write_leadset: SWAP'ed apzd.s1ponr<->s2ponr..");
						swap_int    (&apzd.s1ponr, &apzd.s2ponr);
						swap_double (&apzd.s1meng, &apzd.s2meng);
						swap_string ( apzd.s1werk,  apzd.s2werk);
						swap_double (&apzd.s1abis, &apzd.s2abis);
						swap_string ( apzd.s1drck,  apzd.s2drck);
						swap_int    (&apzd.z1ponr, &apzd.z2ponr);
						swap_double (&apzd.z1meng, &apzd.z2meng);
					}
				} else {
					cable_type = 'T';
					/* get apvd */
					memset(&apvd, 0, sizeof(APVD));		/* set all 0, if not found*/
					EXEC SQL
						select * into :apvd from apvd
						where       apvd.firmnr = :apzd.firmnr
						and apvd.plwerk = :apzd.plwerk
						and apvd.teilnr = :apzd.teilnr
						and apvd.ferweg = 1
						and (apvd.arbgnr = :apzd.arbgnr or apvd.agnr01 = :apzd.arbgnr);
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apvd");
					DBG_log(4, "D110_write_leadset: Twist apvd.teilnr [%s] arbgnr:%ld agnr01:%ld",
							apvd.teilnr, apvd.arbgnr, apvd.agnr01);
				}
				/* get 2. wire  ltg2's */
				/* get mako_ltg2  (= apzd.ltponr) */
				EXEC SQL
					select * into :mako_ltg2 from mako
					where        mako.firmnr = :apzd.firmnr
					and  mako.plwerk = :apzd.plwerk
					and  mako.teilnr = :apzd.teilnr
					and  mako.matnum = :apzd.dplcrp
					and (mako.eindat <= :real_today or mako.eindat = 0)
					and (mako.ausdat >= :real_today or mako.ausdat = 0);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_ltg2")
					DBG_log(4, "D110_write_leadset: mako_ltg2.teilnr [%s] komtnr [%s] stponr:%d matnum [%s]",
							mako_ltg2.teilnr, mako_ltg2.komtnr, mako_ltg2.stponr, mako_ltg2.matnum);
				/* get magd_ltg2   */
				EXEC SQL
					select * into :magd_ltg2 from magd
					where        magd.firmnr = :apzd.firmnr
					and  magd.teilnr = :mako_ltg2.komtnr;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd_ltg2")
					DBG_log(4, "D110_write_leadset: magd_ltg2.teilnr [%s] tnrkbz [%s] tnrbe1 [%s]",
							magd_ltg2.teilnr, magd_ltg2.tnrkbz, magd_ltg2.tnrbe1);

				/* get apzd_ltg2 */
				EXEC SQL
					select * into :apzd_ltg2 from apzd
					where        apzd.firmnr = :apzd.firmnr
					and  apzd.plwerk = :apzd.plwerk
					and  apzd.teilnr = :apzd.teilnr
					and apzd.ferweg = 1                 /* CAO_FERWEG */
					and apzd.ltponr = :mako_ltg2.stponr;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd_ltg2")
					DBG_log(4, "D110_write_leadset: apzd_ltg2.dplcrp [%s] ltponr:%d s1/2meng %f / %f",
							apzd_ltg2.dplcrp, apzd_ltg2.ltponr, apzd_ltg2.s1meng, apzd_ltg2.s2meng);

				if ((cable_type == 'D') && (apzd_ltg2.s1meng == 0.5)) {	/* always put double crimped wire ends on side 2 */
					DBG_log(4, "D110_write_leadset: SWAP'ed apzd_ltg2.s1ponr<->s2ponr..");
					swap_int    (&apzd_ltg2.s1ponr, &apzd_ltg2.s2ponr);
					swap_double (&apzd_ltg2.s1meng, &apzd_ltg2.s2meng);
					swap_string ( apzd_ltg2.s1werk,  apzd_ltg2.s2werk);
					swap_double (&apzd_ltg2.s1abis, &apzd_ltg2.s2abis);
					swap_string ( apzd_ltg2.s1drck,  apzd_ltg2.s2drck);
					swap_int    (&apzd_ltg2.z1ponr, &apzd_ltg2.z2ponr);
					swap_double (&apzd_ltg2.z1meng, &apzd_ltg2.z2meng);
				}

				/* get apag_ltg2 */
				EXEC SQL
					select * into :apag_ltg2 from apag
					where       apag.firmnr = :apzd.firmnr
					and apag.plwerk = :apzd.plwerk
					and apag.teilnr = :apzd.teilnr
					and apag.ferweg = 1             /* CAO_FERWEG */
					and apag.arbgnr = :apzd_ltg2.arbgnr;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apag_ltg2.arbgnr");
				DBG_log(4, "D110_write_leadset: apag_ltg2.avtxsl [%s]", apag_ltg2.avtxsl);

			} else {					/* single */
				cable_type = 'S';
			}
		}
		else {
			cable_type = 'Z';
		}

		/* ------ now have all main info in DB structs, write files */
		if (zeta == 'N'){
			if ((rc = D111_write_leadset_main(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			if ((rc = D112_write_workstep(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			if ((rc = D113_write_leadset_P10(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR; 
			}		
			/* BOM.csv and Wire, Terminal, Seal */
			if ((rc = D114_write_bom(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			//D120_write_wire_update();
		}
		else if (zeta == 'Y') {
			if ((rc = D111_write_leadset_main(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			if ((rc = D112_write_workstep(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			if ((rc = D114_write_bom(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
			//D120_write_wire_update ();
		}

		DBG_log(3, "D110_write_leadset type: %c ret: %d.", cable_type, rc);
	}

	if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		if ((rc = D111_write_leadset_main('P', &rec_leadset_main)) == ERROR) {
			return ERROR;
		}
		if ((rc = D112_write_workstep('P', &rec_leadset_main)) == ERROR) {
			return ERROR;
		}
		if (!strcmp(apag.avtxsl,"2160")) {
			if ((rc = D119_write_leadset_P20(cable_type, &rec_leadset_main)) == ERROR) {
				return ERROR;
			}
		}
		/* BOM.csv and Wire, Terminal, Seal */
		if ((rc = D114_write_bom('P', &rec_leadset_main)) == ERROR) {
			return ERROR;
		}

		DBG_log(3, "D110_write_leadset type: P ret: %d.", rc);
	}
	return rc;
}
/**********************************************************************
 *   Name            : D1101_Wire_Written
 *   Funktion        : Check if 1.st wire exists in the selection of APZD (cursor c_apzd_1) 
 *   Parameter       : Operation Number : arbgnr
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D1101_Wire_Written(int arbgnr)
{
    int rc = OK;
	agnr01 = arbgnr;  
	
	DBG_log(4, "D1101_Wire_Written: 1.st wire apvd.arbgnr:[%d], 2.nd wire apzd.arbgnr:[%d] ",
	arbgnr,apzd.arbgnr);
	
	EXEC SQL
	SELECT *
	FROM apzd
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
    AND   ((((apzd.aendat >= :sql_tmplong AND   APZD.aenuhr >= :fbwstort.loktim)OR(apzd.aendat >= :tomorrow)) and :with_time)OR (apzd.aendat >= :sql_tmplong and :without_time))
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    FROM apzd
    JOIN matl
    ON matl.firmnr = :apzd.firmnr
    AND matl.werknr = :apzd.plwerk
    AND matl.teilnr = :apfw.teilnr
    AND (matl.dataen  >= :fbwstort.aendat OR matl.datneu  >= :fbwstort.aendat )
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    FROM apzd
    JOIN apvd
    ON apvd.firmnr = :apzd.firmnr
    AND apvd.plwerk = :apzd.plwerk
    AND apvd.teilnr = :apfw.teilnr
    AND apvd.arbgnr = apzd.arbgnr
    AND ((((apvd.aendat >= :sql_tmplong and apvd.aenuhr >= :fbwstort.loktim )or(apvd.aendat >= :tomorrow))and :with_time) OR (apvd.aendat >= :sql_tmplong and :without_time) OR (((apvd.erfdat >= :sql_tmplong and APVD.erfuhr>= :fbwstort.loktim)OR(apvd.erfdat >= :tomorrow)) and :with_time)OR(apvd.erfdat >= :sql_tmplong and :without_time))
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
    AND   apzd.dplcrp != ' '
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    FROM apzd
    JOIN maks
    ON maks.firmnr = :apzd.firmnr
    AND maks.werktl = :apzd.plwerk
    AND maks.ferweg = 1
    AND maks.kbntyp = '3'
    AND maks.teilnr = :apfw.teilnr
    AND (maks.aendat >= :sql_tmplong OR maks.erfdat >= :fbwstort.aendat)
    AND apzd.arbgnr = maks.arbgnr
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    FROM apzd
    JOIN fotx
    ON fotx.firmnr = :apzd.firmnr
    AND fotx.txttyp = 'AG'
    AND TO_NUMBER (SUBSTR (fotx.txtshl,1,2),'99') = :apzd.plwerk
    AND TRIM (SUBSTR (fotx.txtshl,3,22)) = :apfw.teilnr
    AND TO_NUMBER (SUBSTR (fotx.txtshl,27,4),'9999') = apzd.arbgnr
    AND ((((fotx.aendat >= :sql_tmplong and FOTX.aenuhr >= :fbwstort.loktim)or(fotx.aendat >= :tomorrow)) and :with_time)OR(fotx.aendat >= :sql_tmplong and :without_time))
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    FROM apzd,mako
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   mako.firmnr = :apzd.firmnr
    AND   mako.plwerk = :apzd.plwerk
    AND   mako.teilnr = :apfw.teilnr
    AND   apzd.teilnr = :apfw.teilnr
    AND   ((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND MAKO.aenuhr>=:fbwstort.loktim and :with_time)OR((mako.aendat = :real_today AND mako.eindat = 0 AND mako.ausdat = 0 AND :without_time)) OR mako.eindat = :real_today)
    AND   apzd.ferweg = 1
    AND   (apzd.s1ponr = mako.stponr OR apzd.s2ponr = mako.stponr OR apzd.ltponr = mako.stponr OR apzd.z1ponr = mako.stponr OR apzd.z2ponr = mako.stponr)
    AND   apzd.arbgnr = :agnr01
	UNION
    SELECT apzd.*
    from apzd
    JOIN magi
    ON magi.firmnr = :apzd.firmnr
    AND magi.teilnr = apzd.teilnr
    AND ((((magi.aendat >=:fbwstort.aendat and Magi.aenzei>= :fbwstort.loktim)or (magi.aendat >=:tomorrow)) and :with_time)OR(magi.aendat >=:fbwstort.aendat and :without_time))
    WHERE apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
	AND   apzd.arbgnr = :agnr01
    UNION
    SELECT apzd.*
    from apzd
    join apbm 
    ON apbm.firmnr  = :apzd.firmnr
    AND apbm.teilnr = :apfw.teilnr 
    AND apbm.arbgnr = apzd.arbgnr
    where (( ((apbm.aendat >=:fbwstort.aendat and apbm.aenuhr>= :fbwstort.loktim) OR (apbm.aendat >=:tomorrow))and :with_time)OR ( apbm.aendat >=:fbwstort.aendat and :without_time) )
    and apzd.firmnr = :apzd.firmnr
    AND   apzd.plwerk = :apzd.plwerk
    AND   apzd.teilnr = :apfw.teilnr
    AND   apzd.ferweg = 1
	AND apzd.arbgnr = :agnr01
    ORDER BY arbgnr;
	
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd")
		
	if (SQLCODE == SQLNOTFOUND) {	
	    DBG_log(4, "D1101_Wire_Written: The first wire isn't written (doesn't exists in the selection c_apzd_1 ) apzd.arbgnr[%d]",
		arbgnr);
		return ERROR;
	} 
	
	return rc;
}
/**********************************************************************
 *   Name            :  D111_write_leadset_main
 *   Funktion        :  write leadset_main
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   				fbwstort, apfw, magd  (teilnr)
 *   				apzd(_ltg2), apag(_ltg2), maks, apvd(T)   (teilnr/arbgnr)
 *		mako_ltg1/2  magd_ltg1/2 apzd_ltg2 apag_ltg2
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D111_write_leadset_main(char cable_type, rec_leadset_main_t *prec_leadset_main)
{
	int rc = OK;
	int rlen;
	char *p1, *p2, *t1, *t2;
	char buffer_line [MAX_CSVLINE_LEN+1];
	//char buffer_line_p2 [MAX_CSVLINE_LEN+1];

	/* write leadset_main */
	memset(prec_leadset_main, 0, sizeof(rec_leadset_main_t));	/* clear all, force 0-term strings ! */
	prec_leadset_main->ProdVersion = 1;

	if ( !strcasecmp(g.do_task, "MAT_P1") ) {

		if (zeta == 'N'){
			sprintf(prec_leadset_main->CableClass, "%c", cable_type);
			//prec_leadset_main->BatchSize = apzd.ltbmng;
			sprintf(prec_leadset_main->Description, "%s", magd.tnrbe1);
			string_trans(prec_leadset_main->Description, ";", ",");

			switch (cable_type) {
				case 'S':
					p1 = mako_ltg1.matnum;
					p2 = "0";
					break;
				case 'D':
				case 'T':
					/* matnum ltg1 ltg2 order */
/*LD01*/
/*					if (strcmp(mako_ltg1.matnum, mako_ltg2.matnum) < 0) {*/
					if (arbgnr_ltg1 < arbgnr_ltg2) {
/*LD01*/
						p1 = mako_ltg1.matnum;
						p2 = mako_ltg2.matnum;
						t1 = magd_ltg1.tnrbe1;		/* not used ? */
						t2 = magd_ltg2.tnrbe1;
					} else {
						prec_leadset_main->mako_ltg_swap = 1;		/* remember if to swap ltg's */
						p1 = mako_ltg2.matnum;
						p2 = mako_ltg1.matnum;
						t1 = magd_ltg2.tnrbe1;
						t2 = magd_ltg1.tnrbe1;
					}
					break;
				default:
					return ERROR;
			}
			rlen = snprintf(prec_leadset_main->Leadset, MAXLEN_LDS_MAIN_LDS, "%s#%s#%s", apzd.teilnr, p1, p2);
/*LD06{*/	       if (!strcmp(p1, "ZETA")) {
                 prec_leadset_main->BatchSize = 0;
                } 
		   else {
                prec_leadset_main->BatchSize = apzd.ltbmng;
                } 
/*LD06}*/				
				
				
			if (rlen == MAXLEN_LDS_MAIN_LDS) {
				prec_leadset_main->Leadset[MAXLEN_LDS_MAIN_LDS] = '\0';		/* no \0 if too long */
				DBG_err(1, "D111_write_leadset_main: ERROR Leadset name too long '%s # %s # %s'", apzd.teilnr, p1, p2);
				rc = ERROR;
			}
			DBG_log(4, "D111_write_leadset_main: Leadset [%s] mako_ltg_swap:%d",
					prec_leadset_main->Leadset, prec_leadset_main->mako_ltg_swap);

			/* maks handling */
			if (maks.firmnr != 0) {			/* if maks row found */
				/* above memset ensures \0 term */
				snprintf(prec_leadset_main->UserText1, MAXLEN_LDS_MAIN_USTXT, "%s %s", maks.text01, maks.text02);
				snprintf(prec_leadset_main->UserText2, MAXLEN_LDS_MAIN_USTXT, "%s %s", maks.text03, maks.text04);
				snprintf(prec_leadset_main->UserText3, MAXLEN_LDS_MAIN_USTXT, "%s %s", maks.text05, maks.text06);
				snprintf(prec_leadset_main->UserText4, MAXLEN_LDS_MAIN_USTXT, "%s %s", maks.text07, maks.text08);
				snprintf(prec_leadset_main->UserText5, MAXLEN_LDS_MAIN_USTXT, "%s %s", maks.text09, maks.text10);
				string_trans(prec_leadset_main->UserText1, ";", ",");
				string_trans(prec_leadset_main->UserText2, ";", ",");
				string_trans(prec_leadset_main->UserText3, ";", ",");
				string_trans(prec_leadset_main->UserText4, ";", ",");
				string_trans(prec_leadset_main->UserText5, ";", ",");
			}
			/* search fotx.txtshl LIKE '38<teilnr>               01XXXX%'    XXXX=arbgnr */
			sprintf (sql_tmpstr, "%02d%-22s01%04d%%", apzd.plwerk, apzd.teilnr, apzd.arbgnr);
			DBG_log(3, "D111_write_leadset_main: fotx.txtshl like [%s]", sql_tmpstr);
			fotx.txtnmr = 0.0;
			EXEC SQL
				select fotx.txtnmr into :fotx.txtnmr from fotx
				where       fotx.firmnr = :apzd.firmnr
				and fotx.txttyp = 'AG'
				and fotx.txtshl like :sql_tmpstr;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotx.txtnmr");
			DBG_log(3, "D111_write_leadset_main: fotx.txtnmr [%f]", fotx.txtnmr);
			/* if exists, combine fotp.txtzle's */
			if (SQLCODE != SQLNOTFOUND ) {	/* found */
				sql_tmpstr[0] = '\0';		/* if row not found, empty */
				EXEC SQL
					select string_agg(fotp.txtzle,' ') into :sql_tmpstr from fotp
					where       fotp.firmnr = :apzd.firmnr
					and fotp.txtnmr = :fotx.txtnmr
					and fotp.txtakt in (1,2);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
				string_trans(sql_tmpstr, ";", ",");
				snprintf(prec_leadset_main->UserText6, MAXLEN_LDS_MAIN_USTXT, "%s", sql_tmpstr);

				sql_tmpstr[0] = '\0';		/* if row not found, empty */
				EXEC SQL
					select string_agg(fotp.txtzle,' ') into :sql_tmpstr from fotp
					where       fotp.firmnr = :apzd.firmnr
					and fotp.txtnmr = :fotx.txtnmr
					and fotp.txtakt in (1,2,3,4);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
				string_trans(sql_tmpstr, ";", ",");
				snprintf(prec_leadset_main->UserText7, MAXLEN_LDS_MAIN_USTXT, "%s", sql_tmpstr);

				sql_tmpstr[0] = '\0';		/* if row not found, empty */
				EXEC SQL
					select string_agg(fotp.txtzle,' ') into :sql_tmpstr from fotp
					where       fotp.firmnr = :apzd.firmnr
					and fotp.txtnmr = :fotx.txtnmr
					and fotp.txtakt in (5,6,7);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
				string_trans(sql_tmpstr, ";", ",");
				snprintf(prec_leadset_main->UserText8, MAXLEN_LDS_MAIN_USTXT, "%s", sql_tmpstr);

				sql_tmpstr[0] = '\0';		/* if row not found, empty */
				EXEC SQL
					select string_agg(fotp.txtzle,' ') into :sql_tmpstr from fotp
					where       fotp.firmnr = :apzd.firmnr
					and fotp.txtnmr = :fotx.txtnmr
					and fotp.txtakt in (8,9,10);
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
				string_trans(sql_tmpstr, ";", ",");
				snprintf(prec_leadset_main->UserText9, MAXLEN_LDS_MAIN_USTXT, "%s", sql_tmpstr);
			}

			snprintf(prec_leadset_main->UserText10, MAXLEN_LDS_MAIN_USTXT, "%d", maks.anzkan);
		}
		else {
			sprintf(prec_leadset_main->CableClass, "%c", cable_type);
			
			prec_leadset_main->BatchSize = 1;
			
/*LD06{*/	
            if (apzd.ltbmng == 0) {
               prec_leadset_main->BatchSize = 1;
              } else {
               prec_leadset_main->BatchSize = apzd.ltbmng;
              }     
			  
 /*LD06}*/
			sprintf(prec_leadset_main->Description, "%s", magd.tnrbe1);
			string_trans(prec_leadset_main->Description, ";", ",");

			rlen = snprintf(prec_leadset_main->Leadset, MAXLEN_LDS_MAIN_LDS, "%s#Zeta", apag.teilnr);
			if (rlen == MAXLEN_LDS_MAIN_LDS) {
				prec_leadset_main->Leadset[MAXLEN_LDS_MAIN_LDS] = '\0';		/* no \0 if too long */
				DBG_err(1, "D111_write_leadset_main: ERROR Leadset name too long '%s # Zeat'", apzd.teilnr);
				rc = ERROR;
			}
		}

		sql_tmpstr[0] = '\0';		/* if row not found, empty */
		EXEC SQL
			select indi41 into :sql_tmpstr from magi
			where  firmnr = :apzd.firmnr
			and magi.teilnr = :apzd.teilnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
		string_trans(sql_tmpstr, ";", ",");
		snprintf(prec_leadset_main->Customer, MAXLEN_CUSTOMER, "%s", sql_tmpstr);


		sql_tmpstr[0] = '\0';		/* if row not found, empty */
		EXEC SQL
			select indi42 into :sql_tmpstr from magi 
			where  firmnr = :apzd.firmnr
			and magi.teilnr = :apzd.teilnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
		string_trans(sql_tmpstr, ";", ",");
		snprintf(prec_leadset_main->Project, MAXLEN_PROJECT, "%s", sql_tmpstr);


		rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
				"%s;%ld;%s;%s;%d;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				prec_leadset_main->Leadset,	prec_leadset_main->ProdVersion,
				prec_leadset_main->Description,	prec_leadset_main->CableClass,
				prec_leadset_main->BatchSize,
				prec_leadset_main->UserText1,	prec_leadset_main->UserText2,
				prec_leadset_main->UserText3,	prec_leadset_main->UserText4,
				prec_leadset_main->UserText5,	prec_leadset_main->UserText6,
				prec_leadset_main->UserText7,	prec_leadset_main->UserText8,
				prec_leadset_main->UserText9,	prec_leadset_main->UserText10,
				prec_leadset_main->Customer,	prec_leadset_main->Project);
		if (rlen == MAX_CSVLINE_LEN) {
			DBG_err(1, "D111_write_leadset_main: [%s] csv line too long", prec_leadset_main->Leadset);
			buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
			return ERROR;
		}
		if(strcmp(p_Leadset_lsm,prec_leadset_main->Leadset)){
			rc = G_write_line(fdg_leadset, buffer_line);

			DBG_log(3, "D111_write_leadset_main: Leadset [%s] [%s] ret:%d",
					prec_leadset_main->Leadset, prec_leadset_main->Description, rc);
		}
		strcpy(p_Leadset_lsm,prec_leadset_main->Leadset);
	}

	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {

		kill_all_blanks(apag.zaufnr);
		if(strlen(apag.zaufnr)) {
			sprintf(prec_leadset_main->Leadset, "%s-%s", apag.teilnr,apag.zaufnr);
		}
		else {
			sprintf(prec_leadset_main->Leadset, "%s-%d", apag.teilnr,apag.arbgnr);
		}

		sql_tmpstr[0] = '\0';		/* if row not found, empty */
		EXEC SQL
			select indi41 into :sql_tmpstr from magi
			where  firmnr = :apag.firmnr
			and magi.teilnr = :apag.teilnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fotp.txtzle");
		string_trans(sql_tmpstr, ";", ",");
		snprintf(prec_leadset_main->Customer, MAXLEN_CUSTOMER, "%s", sql_tmpstr);

		sprintf(prec_leadset_main->Description, "%s", magd.tnrbe1);
		strcpy(prec_leadset_main->CableClass, "P");
		prec_leadset_main->BatchSize = apag.agmibs;
		rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
				"%s;%ld;%s;%s;%d;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				prec_leadset_main->Leadset,	prec_leadset_main->ProdVersion,
				prec_leadset_main->Description,	prec_leadset_main->CableClass,
				prec_leadset_main->BatchSize,
				prec_leadset_main->UserText1,	prec_leadset_main->UserText2,
				prec_leadset_main->UserText3,	prec_leadset_main->UserText4,
				prec_leadset_main->UserText5,	prec_leadset_main->UserText6,
				prec_leadset_main->UserText7,	prec_leadset_main->UserText8,
				prec_leadset_main->UserText9,	prec_leadset_main->UserText10,
				prec_leadset_main->Customer,	prec_leadset_main->Project);
		if (rlen == MAX_CSVLINE_LEN) {
			DBG_err(1, "D111_write_leadset_main: [%s] csv line too long", prec_leadset_main->Leadset);
			buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
			return ERROR;
		}

		if(strcmp(p_Leadset_lsm,prec_leadset_main->Leadset)){
			rc = G_write_line(fdg_leadset, buffer_line);

			DBG_log(3, "D111_write_leadset_main: Leadset [%s] [%s] ret:%d",
					prec_leadset_main->Leadset, prec_leadset_main->Description, rc);
		}
		strcpy(p_Leadset_lsm,prec_leadset_main->Leadset);
	}


	return rc;
}

/**********************************************************************
 *   Name            :  D112_write_workstep
 *   Funktion        :  write workstep
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   				fbwstort, apfw, magd  (teilnr)
 *   				apzd(_ltg2), apag(_ltg2), maks, apvd(T)   (telnr/arbgnr)
 *		mako_ltg1/2  magd_ltg1/2 apzd_ltg2 apag_ltg2
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D112_write_workstep(char cable_type, rec_leadset_main_t *prec_leadset_main)
{
	int rc = OK;
	int rlen, fotx_cnt;
	rec_workstep_t rec_workstep;
	char buffer_line    [MAX_CSVLINE_LEN+1];
	//char buffer_line_p2_1 [];
	//char buffer_line_p2_2 [5];
	//char buffer_line_p2 [MAX_CSVLINE_LEN+1];
	char *p_comma;
	int count_apar=1;
	

	EXEC SQL BEGIN DECLARE SECTION;
	int teilnr_length;
	char s_apag_teilnr[50];
	char op_apag[5];
	EXEC SQL END DECLARE SECTION;

	DBG_log(4, "D112_write_workstep: Leadset [%s]", prec_leadset_main->Leadset);
	memset(&rec_workstep, 0, sizeof(rec_workstep_t));	/* clear all */

	/* fill struct */
	strcpy(rec_workstep.Leadset, prec_leadset_main->Leadset);
	rec_workstep.ProdVersion = 1;


	if ( !strcasecmp(g.do_task, "MAT_P1") ) {		
		snprintf(rec_workstep.workstepinfo, MAXLEN_WS_INFO, "%s %s %s", prec_leadset_main->UserText7, 
				prec_leadset_main->UserText8, prec_leadset_main->UserText9);
		rec_workstep.Workstep = 10;
		if (zeta == 'Y'){
			strcpy(rec_workstep.workstepGroup, "Cutting wire in Zeta");
		}
		else {

			if (!strcmp(g.warehouse_default,"APAG")){

				EXEC SQL select lagort into :apag.lagort 
					FROM apag g
					WHERE firmnr = :apzd.firmnr
					AND   plwerk = :apzd.plwerk
					AND   teilnr = :apzd.teilnr
					AND   arbgnr = :apzd.arbgnr
					AND   ferweg = 1;
				SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpsl warehouse");
				if (SQLCODE == 0) {
					if (!strcmp(apag.lagort,"SR01")){
						strcpy(rec_workstep.workstepGroup, "Cutting");
					}
					else {
						strcpy(rec_workstep.workstepGroup, apag.lagort);
					}
				}
			}
			else {
				strcpy(rec_workstep.workstepGroup, "Cutting");
			}
		}

		rec_workstep.PlanTimeBatch = 0;

		/* get WS desr */
		strcpy(apag.avtxsl, "");		/* clear if not found */
		strcpy(pasy.datenf, "-na-");	/* if not found */
		EXEC SQL
			select apag.avtxsl into :apag_ws.avtxsl from apag
			where       apag.firmnr=:apzd.firmnr
			and apag.plwerk=:apzd.plwerk
			and apag.ferweg = 1
			and apag.arbgal = 0
			and apag.sequnr = 0
			and apag.teilnr = :apag.teilnr
			and apag.arbgnr = :apag.arbgnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apag.avtxsl");
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(1, "D112_write_workstep: WARN avtxsl not available");
		} else {
			DBG_log(3, "D112_write_workstep: apag.avtxsl [%s]", apag_ws.avtxsl);
			strcpy(pasy.spschl, env_sprach);		/* TODO use: env_sprach ? */
			EXEC SQL
				select pasy.datenf into :pasy.datenf from pasy
				where       pasy.firmnr = :apzd.firmnr
				and pasy.werkli = 0
				and pasy.schlgr = 'AVTX'
				and pasy.schlsl = :apag_ws.avtxsl
				and pasy.spschl = :pasy.spschl;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "avtxsl pasy.datenf");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(1, "D112_write_workstep: WARN datenf description not available");
			} else {
				DBG_log(3, "D112_write_workstep: pasy.datenf [%s]", pasy.datenf);
			}
		}
		strcpy(rec_workstep.workstepDescription, pasy.datenf);
		string_trans(rec_workstep.workstepDescription, ";", ",");
		/**************/
		
		if(g.apar==1)
		{
			strcpy(rec_workstep.Attribute2,"");
			strcpy(rec_workstep.Attribute3,"");
			strcpy(rec_workstep.Attribute4,"");
			strcpy(rec_workstep.Attribute5,"");
			EXEC SQL declare c_apar cursor with hold for
			SELECT reskbz into :RESKBZ
            FROM  apbm AB,rsrg RS
            WHERE 
                AB.firmnr = RS.firmnr
            AND AB.bmgrup = RS.resgrp
            AND AB.firmnr =:apag.firmnr
            AND AB.plwerk =:apag.plwerk
            AND AB.teilnr =:apag.teilnr
            AND AB.arbgnr =:apag.arbgnr
            AND AB.ferweg =:apag.ferweg
            AND AB.arbgal =:apag.arbgal
            AND AB.sequnr =:apag.sequnr
            LIMIT 4;
			
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apar");
			EXEC SQL open c_apar;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apar");
			EXEC SQL fetch c_apar;
			while ((SQLCODE!=SQLNOTFOUND)&&(count_apar<=4))
			{
				if(count_apar==1)
					strcpy(rec_workstep.Attribute2,RESKBZ);
				else if(count_apar==2)
					strcpy(rec_workstep.Attribute3,RESKBZ);
				else if(count_apar==3)
					strcpy(rec_workstep.Attribute4,RESKBZ);
				else
					strcpy(rec_workstep.Attribute5,RESKBZ);
				count_apar++;
				EXEC SQL fetch c_apar;
			}
			EXEC SQL close c_apar;
			rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
				"%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				rec_workstep.Leadset,		rec_workstep.ProdVersion,
				rec_workstep.Workstep,		rec_workstep.workstepGroup,
				rec_workstep.workstepDescription, rec_workstep.workstepinfo,
				rec_workstep.Document1,		rec_workstep.Document2,
				rec_workstep.PlanTimeBatch,
				rec_workstep.UserText1,		rec_workstep.UserText2,
				rec_workstep.UserText3,		rec_workstep.UserText4,
				rec_workstep.UserText5,		rec_workstep.UserText6,
				rec_workstep.UserText7,		rec_workstep.UserText8,
				rec_workstep.UserText9,		rec_workstep.UserText10,
				rec_workstep.Attribute2,rec_workstep.Attribute3,
				rec_workstep.Attribute4,rec_workstep.Attribute5 );
		}
		/*****************************/
		else
		rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
				"%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				rec_workstep.Leadset,		rec_workstep.ProdVersion,
				rec_workstep.Workstep,		rec_workstep.workstepGroup,
				rec_workstep.workstepDescription, rec_workstep.workstepinfo,
				rec_workstep.Document1,		rec_workstep.Document2,
				rec_workstep.PlanTimeBatch,
				rec_workstep.UserText1,		rec_workstep.UserText2,
				rec_workstep.UserText3,		rec_workstep.UserText4,
				rec_workstep.UserText5,		rec_workstep.UserText6,
				rec_workstep.UserText7,		rec_workstep.UserText8,
				rec_workstep.UserText9,		rec_workstep.UserText10 );
		if (rlen == MAX_CSVLINE_LEN) {
			DBG_err(1, "D112_write_workstep: [%s] csv line too long", prec_leadset_main->Leadset);
			buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
			return ERROR;
		}
	}

	else if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		if (apag.arbgnr == 10){
			rec_workstep.Workstep = 11;
		}
		else {
			rec_workstep.Workstep = apag.arbgnr;
		}

		//rec_workstep.PlanTimeBatch = apag.agmibs/10 * apag.znor01;
		rec_workstep.PlanTimeBatch = (apag.znor01/apag.zmng01)*apag.agmibs;

		EXEC SQL
			select pasy.datenf into :pasy.datenf from pasy
			where       pasy.firmnr = :apag.firmnr
			and pasy.werkli = 0
			and pasy.schlgr = 'AVTX'
			and pasy.schlsl = :apag.avtxsl
			and pasy.spschl = 'E';
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "avtxsl pasy.datenf");
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(1, "D112_write_workstep: WARN datenf description not available");
		} else {
			DBG_log(3, "D112_write_workstep: pasy.datenf [%s]", pasy.datenf);
		}
		//strcpy(rec_workstep.workstepGroup, pasy.datenf);
		//strcpy(rec_workstep.workstepDescription, pasy.datenf); 
		strcpy(rec_workstep.workstepGroup, "%s");
		strcpy(rec_workstep.workstepDescription, "%s");

		sprintf(op_apag, "%04d", apag.arbgnr);
		sprintf(s_apag_teilnr, "%s%s %s","%",apag.teilnr,"%");
		teilnr_length = strlen(apag.teilnr);
		EXEC SQL declare c_fotx cursor with hold for
			SELECT txtzle into :fotp.txtzle
			FROM fotp
			WHERE txtnmr IN (SELECT txtnmr
					FROM (SELECT txtnmr,
						fotx.txtshl,
						txttyp,
						SUBSTR(fotx.txtshl,3,:teilnr_length) AS Partnumber,
						SUBSTR(fotx.txtshl,27,4) AS Operation
						FROM fotx
						WHERE fotx.firmnr = :apag.firmnr
						and fotx.txttyp = 'AG'
						and txtshl LIKE :s_apag_teilnr ) AS messages
					WHERE Operation = :op_apag)
					ORDER BY txtakt ASC;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_fotx");
		EXEC SQL open c_fotx;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_fotx");

		fotx_cnt = 0;
		while(1) {

			/* get apfw.teilnr  */
			EXEC SQL fetch c_fotx;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_fotx");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(3, "D112_write_workstep: fotx fetch end (number of messages for teilnr[%s] is : %d)", apag.teilnr, fotx_cnt);
				break;
			}
			fotx_cnt++;

			kill_all_blanks(rec_workstep.workstepinfo);
			while((p_comma = strchr(fotp.txtzle,';')) != NULL){
				*p_comma = '/' ;
			}
			sprintf(rec_workstep.workstepinfo, "%s<br>%s", rec_workstep.workstepinfo, fotp.txtzle);

		}
		EXEC SQL close c_fotx;
		//int rlen1,rlen2;
		
		/*rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
				"%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				rec_workstep.Leadset,		rec_workstep.ProdVersion,
				rec_workstep.Workstep,		rec_workstep.workstepGroup,
				rec_workstep.workstepDescription, rec_workstep.workstepinfo,
				rec_workstep.Document1,		rec_workstep.Document2,
				rec_workstep.PlanTimeBatch,
				rec_workstep.UserText1,		rec_workstep.UserText2,
				rec_workstep.UserText3,		rec_workstep.UserText4,
				rec_workstep.UserText5,		rec_workstep.UserText6,
				rec_workstep.UserText7,		rec_workstep.UserText8,
				rec_workstep.UserText9,		rec_workstep.UserText10 );*/		
	}

	if (zeta == 'N')
	{
		if(!strcasecmp(g.do_task, "MAT_P1"))
		{
			rc = G_write_line (fdg_workstep, buffer_line);
			DBG_log(3, "D112_write_workstep: Leadset [%s] Descr [%s]", prec_leadset_main->Leadset,rec_workstep.workstepDescription);
		}
		else if(!strcasecmp(g.do_task, "MAT_P2"))
		{
			strcpy(rec_workstep.workstepGroup, apag.lagort);
			strcpy(rec_workstep.workstepDescription, apag.lagort);
			
			rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,			"%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
			rec_workstep.Leadset,		rec_workstep.ProdVersion,
			rec_workstep.Workstep,		rec_workstep.workstepGroup,
			rec_workstep.workstepDescription, rec_workstep.workstepinfo,
			rec_workstep.Document1,		rec_workstep.Document2,
			rec_workstep.PlanTimeBatch,
			rec_workstep.UserText1,		rec_workstep.UserText2,
			rec_workstep.UserText3,		rec_workstep.UserText4,
			rec_workstep.UserText5,		rec_workstep.UserText6,
			rec_workstep.UserText7,		rec_workstep.UserText8,
			rec_workstep.UserText9,		rec_workstep.UserText10 );
			if (rlen == MAX_CSVLINE_LEN) 
			{
				DBG_err(1, "D112_write_workstep: [%s] csv line too long", prec_leadset_main->Leadset);
				buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
				return ERROR;				
			}			
			rc = G_write_line (fdg_workstep, buffer_line);
			DBG_log(3, "D112_write_workstep: Leadset [%s] Descr [%s]", prec_leadset_main->Leadset,rec_workstep.workstepDescription);
			
			strcpy(rec_workstep.workstepGroup, pasy.datenf);
			strcpy(rec_workstep.workstepDescription, pasy.datenf);
			
			if(g.apar==1)
			{
				strcpy(rec_workstep.Attribute2,"");
			    strcpy(rec_workstep.Attribute3,"");
			    strcpy(rec_workstep.Attribute4,"");
			    strcpy(rec_workstep.Attribute5,"");
			    EXEC SQL declare c_apar_P2 cursor with hold for
				SELECT reskbz into :RESKBZ
                FROM  apbm AB,rsrg RS
                WHERE 
                    AB.firmnr = RS.firmnr
                AND AB.bmgrup = RS.resgrp
                AND AB.firmnr =:apag.firmnr
                AND AB.plwerk =:apag.plwerk
                AND AB.teilnr =:apag.teilnr
                AND AB.arbgnr =:apag.arbgnr
                AND AB.ferweg =:apag.ferweg
                AND AB.arbgal =:apag.arbgal
                AND AB.sequnr =:apag.sequnr
                LIMIT 4;		
				 
			    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apar_P2");
			    EXEC SQL open c_apar_P2;
			    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apar_P2");
			    EXEC SQL fetch c_apar_P2;
			    while ((SQLCODE!=SQLNOTFOUND)&&(count_apar<=4))
			    {
			    	if(count_apar==1)
			    		strcpy(rec_workstep.Attribute2,RESKBZ);
			    	else if(count_apar==2)
			    		strcpy(rec_workstep.Attribute3,RESKBZ);
			    	else if(count_apar==3)
			    		strcpy(rec_workstep.Attribute4,RESKBZ);
			    	else
			    		strcpy(rec_workstep.Attribute5,RESKBZ);
			    	count_apar++;
			    	EXEC SQL fetch c_apar_P2;
			    }
			    EXEC SQL close c_apar_P2;
			    rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,		    	"%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
			    rec_workstep.Leadset,		rec_workstep.ProdVersion,
			    rec_workstep.Workstep,		rec_workstep.workstepGroup,
			    rec_workstep.workstepDescription, rec_workstep.workstepinfo,
			    rec_workstep.Document1,		rec_workstep.Document2,
			    rec_workstep.PlanTimeBatch,
			    rec_workstep.UserText1,		rec_workstep.UserText2,
			    rec_workstep.UserText3,		rec_workstep.UserText4,
			    rec_workstep.UserText5,		rec_workstep.UserText6,
			    rec_workstep.UserText7,		rec_workstep.UserText8,
			    rec_workstep.UserText9,		rec_workstep.UserText10,
			    rec_workstep.Attribute2,    rec_workstep.Attribute3,
			    rec_workstep.Attribute4,    rec_workstep.Attribute5 );
			    	
			    if (rlen == MAX_CSVLINE_LEN) 
			    {
			    	DBG_err(1, "D112_write_workstep: [%s] csv line too long", prec_leadset_main->Leadset);
			    	buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
			    	return ERROR;
			    }	
			    rc = G_write_line (fdg_workstep, buffer_line);
			    DBG_log(3, "D112_write_workstep: Leadset [%s] Descr [%s]", prec_leadset_main->Leadset,rec_workstep.workstepDescription);
			}
			else 
			{
				rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,			    "%s;%ld;%d;%s;%s;%s;%s;%s;%6.3f;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
				rec_workstep.Leadset,		rec_workstep.ProdVersion,
			    rec_workstep.Workstep,		rec_workstep.workstepGroup,
			    rec_workstep.workstepDescription, rec_workstep.workstepinfo,
			    rec_workstep.Document1,		rec_workstep.Document2,
			    rec_workstep.PlanTimeBatch,
			    rec_workstep.UserText1,		rec_workstep.UserText2,
			    rec_workstep.UserText3,		rec_workstep.UserText4,
			    rec_workstep.UserText5,		rec_workstep.UserText6,
			    rec_workstep.UserText7,		rec_workstep.UserText8,
			    rec_workstep.UserText9,		rec_workstep.UserText10 );
			    if (rlen == MAX_CSVLINE_LEN) 
				{
			    	DBG_err(1, "D112_write_workstep: [%s] csv line too long", prec_leadset_main->Leadset);
			    		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
			    	return ERROR;				
			    }	
                rc = G_write_line (fdg_workstep, buffer_line);
			    DBG_log(3, "D112_write_workstep: Leadset [%s] Descr [%s]", prec_leadset_main->Leadset,rec_workstep.workstepDescription);	
			} 	
		}
	}			
	else if(strcmp(p_Leadset_ws,prec_leadset_main->Leadset)){
		rc = G_write_line (fdg_workstep, buffer_line);
		DBG_log(3, "D112_write_workstep: Leadset [%s] Descr [%s]", prec_leadset_main->Leadset,
				rec_workstep.workstepDescription);
		strcpy(p_Leadset_ws,prec_leadset_main->Leadset);
	}

	return rc;
}


/**********************************************************************
 *   Name            :  D113_write_leadset_P10
 *   Funktion        :  write leadset_P10
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   			fbwstort, apfw, magd  (teilnr)
 *   			apzd(_ltg2), apag(_ltg2), maks, apvd(T)   (telnr/arbgnr)
 *			mako_ltg1/2  magd_ltg1/2 apzd_ltg2 apag_ltg2
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D113_write_leadset_P10(char cable_type, rec_leadset_main_t *prec_leadset_main)
{
	int rc = OK;
	int rlen;
	rec_leadset_P10_t rec_leadset_P10;
	char buffer_line [MAX_CSVLINE_LEN+1];
	//begin I-371109
	char *ptr;
	char leadset_div[MAXLEN_LD10_LDS+1];
	
	

	EXEC SQL BEGIN DECLARE SECTION;
	char matken[20];
	EXEC SQL END DECLARE SECTION;

	memset(&rec_leadset_P10, 0, sizeof(rec_leadset_P10_t));	/* clear all */
	/*strcpy(rec_leadset_P10.Leadset, prec_leadset_main->Leadset);
	  ptr = strchr(rec_leadset_P10.Leadset,'#');
	 *ptr = '\0';
	 strcpy(teilnr,rec_leadset_P10.Leadset);
	 strcpy(rec_leadset_P10.Leadset,ptr + 1);
	 ptr = strchr(rec_leadset_P10.Leadset,'#');
	 *ptr = '\0';
	 strcpy(matnum,rec_leadset_P10.Leadset);*/


	EXEC SQL SELECT matken into :matken
		FROM magd
		WHERE firmnr = :apzd.firmnr
		AND   teilnr IN (SELECT komtnr
				FROM mako
				WHERE firmnr = :apzd.firmnr
				AND   plwerk = :apzd.plwerk
				AND   teilnr = :apzd.teilnr
				AND   stponr = :apzd.s1ponr
				AND (eindat <= :real_today or eindat = 0)
				AND (ausdat >= :real_today or ausdat = 0));
	if (SQLCODE == SQLNOTFOUND){
		strcpy(matken," ");
	}
	kill_all_blanks(matken);
	if(!strcmp(matken,"ZINN")||!strcmp(matken,"LOE")){
		rec_leadset_P10.TinningLength1 = apzd.s1abis;
	}

	EXEC SQL SELECT matken into :matken
		FROM magd
		WHERE firmnr = :apzd.firmnr
		AND   teilnr IN (SELECT komtnr
				FROM mako
				WHERE firmnr = :apzd.firmnr
				AND   plwerk = :apzd.plwerk
				AND   teilnr = :apzd.teilnr
				AND   stponr = :apzd.s2ponr
				AND (eindat <= :real_today or eindat = 0)
				AND (ausdat >= :real_today or ausdat = 0));
	if (SQLCODE == SQLNOTFOUND){
		strcpy(matken," ");
	}
	kill_all_blanks(matken);
	if(!strcmp(matken,"ZINN")||!strcmp(matken,"LOE")){
		rec_leadset_P10.TinningLength2 = apzd.s2abis;
	}

	//end I-371109
	DBG_log(4, "D113_write_leadset_P10: Leadset [%s]", prec_leadset_main->Leadset);
	strcpy(rec_leadset_P10.Leadset, prec_leadset_main->Leadset);
	rec_leadset_P10.ProdVersion = 1;
	strcpy(rec_leadset_P10.Font1, "PSRFONT");
	strcpy(rec_leadset_P10.Font2, "PSRFONT");

	/* get mako_term11 */
	memset(&mako_term11, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
	if (apzd.s1meng > 0) {
		EXEC SQL
			select * into :mako_term11 from mako
			where       mako.firmnr = :apzd.firmnr
			and mako.plwerk = :apzd.plwerk
			and mako.teilnr = :apzd.teilnr
			and mako.stponr = :apzd.s1ponr
			and (eindat <= :real_today or mako.eindat = 0)
			and (ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_term11");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_term11");
	}
	/* get mako_term12 */
	memset(&mako_term12, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
	if (apzd.s2meng > 0) {
		EXEC SQL
			select * into :mako_term12 from mako
			where       mako.firmnr = :apzd.firmnr
			and mako.plwerk = :apzd.plwerk
			and mako.teilnr = :apzd.teilnr
			and mako.stponr = :apzd.s2ponr
			and (eindat <= :real_today or mako.eindat = 0)
			and (ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_term12");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_term12");
	}
	/* get mako_seal11 */
	memset(&mako_seal11, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
	if (apzd.z1meng > 0) {
		EXEC SQL
			select * into :mako_seal11 from mako
			where       mako.firmnr = :apzd.firmnr
			and mako.plwerk = :apzd.plwerk
			and mako.teilnr = :apzd.teilnr
			and mako.stponr = :apzd.z1ponr
			and (eindat <= :real_today or mako.eindat = 0)
			and (ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_seal11");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_seal11");
	}
	/* get mako_seal12 */
	memset(&mako_seal12, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
	if (apzd.z2meng > 0) {
		EXEC SQL
			select * into :mako_seal12 from mako
			where       mako.firmnr = :apzd.firmnr
			and mako.plwerk = :apzd.plwerk
			and mako.teilnr = :apzd.teilnr
			and mako.stponr = :apzd.z2ponr
			and (eindat <= :real_today or mako.eindat = 0)
			and (ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_seal12");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_seal12");
	}
	/****  LTG 2 *****/
	if (cable_type != 'S') {		/* apzd_ltg2 */
		/* get mako_term11 */
		memset(&mako_term21, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
		if (apzd_ltg2.s1meng > 0) {
			EXEC SQL
				select * into :mako_term21 from mako
				where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :apzd.teilnr
				and mako.stponr = :apzd_ltg2.s1ponr
				and (eindat <= :real_today or mako.eindat = 0)
				and (ausdat >= :real_today or mako.ausdat = 0);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_term21");
			if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_term21");
		}
		/* get mako_term22 */
		memset(&mako_term22, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
		if (apzd_ltg2.s2meng > 0) {
			EXEC SQL
				select * into :mako_term22 from mako
				where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :apzd.teilnr
				and mako.stponr = :apzd_ltg2.s2ponr
				and (eindat <= :real_today or mako.eindat = 0)
				and (ausdat >= :real_today or mako.ausdat = 0);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_term22");
			if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_term22");
		}
		/* get mako_seal21 */
		memset(&mako_seal21, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
		if (apzd_ltg2.z1meng > 0) {
			EXEC SQL
				select * into :mako_seal21 from mako
				where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :apzd.teilnr
				and mako.stponr = :apzd_ltg2.z1ponr
				and (eindat <= :real_today or mako.eindat = 0)
				and (ausdat >= :real_today or mako.ausdat = 0);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_seal21");
			if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_seal21");
		}
		/* get mako_seal22 */
		memset(&mako_seal22, 0, sizeof(MAKOTYP));             /* set all 0, if not found*/
		if (apzd_ltg2.z2meng > 0) {
			EXEC SQL
				select * into :mako_seal22 from mako
				where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :apzd.teilnr
				and mako.stponr = :apzd_ltg2.z2ponr
				and (eindat <= :real_today or mako.eindat = 0)
				and (ausdat >= :real_today or mako.ausdat = 0);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_seal22");
			if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mako_seal22");
		}
	}

	/* fill struct (int's and str's already memset = 0) */
	rec_leadset_P10.StrippingLength1	 = apzd.s1abis;
	rec_leadset_P10.PartStripLength1	 = round(apzd.s1abis * 0.33);
	rec_leadset_P10.StrippingLength2	 = apzd.s2abis;
	rec_leadset_P10.PartStripLength2	 = round(apzd.s2abis * 0.33);
	if (cable_type != 'S') {
/*LD03*/
		if (cable_type == 'D' && cable_type != 'T' && ( apzd.arbgnr > apzd_ltg2.arbgnr ))
		{
			rec_leadset_P10.StrippingLength1	= apzd_ltg2.s1abis;
			rec_leadset_P10.PartStripLength1	= round(apzd_ltg2.s1abis * 0.33);
			rec_leadset_P10.StrippingLength2	= apzd_ltg2.s2abis;
			rec_leadset_P10.PartStripLength2	= round(apzd_ltg2.s2abis * 0.33);	
			rec_leadset_P10.StrippingLength3	= apzd.s1abis;
			rec_leadset_P10.PartStripLength3	= round(apzd.s1abis * 0.33);
			rec_leadset_P10.StrippingLength4	= apzd.s2abis;
			rec_leadset_P10.PartStripLength4	= round(apzd.s2abis * 0.33);	
		}
		else 
		{
/*LD03*/
		rec_leadset_P10.StrippingLength3	= apzd_ltg2.s1abis;
		rec_leadset_P10.PartStripLength3	= round(apzd_ltg2.s1abis * 0.33);
		rec_leadset_P10.StrippingLength4	= apzd_ltg2.s2abis;
		rec_leadset_P10.PartStripLength4	= round(apzd_ltg2.s2abis * 0.33);
		}
	} else {
		rec_leadset_P10.StrippingLength3	= 0.0;
		rec_leadset_P10.PartStripLength3	= 0.0;
		rec_leadset_P10.StrippingLength4	= 0.0;
		rec_leadset_P10.PartStripLength4	= 0.0;
	}
	if (cable_type == 'T') {
		rec_leadset_P10.TwistWireLength 	= apvd.endvdl * 1000;
		rec_leadset_P10.PitchLength			= apvd.schllg;
		rec_leadset_P10.OpenEndLength1		= apvd.offes1;
		rec_leadset_P10.OpenEndLength2		= apvd.offes2;
		
		/********************************/
		strcpy(leadset_div,rec_leadset_P10.Leadset);
		ptr = strchr(leadset_div,'#');
		*ptr = '\0';
		strcpy(teilnr_div,leadset_div);
		strcpy(leadset_div,ptr + 1);
		ptr = strchr(leadset_div,'#');
		*ptr = '\0';
		strcpy(matnum_ltg1,leadset_div);
		strcpy(matnum_ltg2,ptr + 1);
		EXEC SQL
		select apko.mengfk into :meng_ltg1 from mako
		join apko
		on apko.firmnr=mako.firmnr
		and apko.plwerk=mako.plwerk
		and apko.teilnr=mako.teilnr
		and apko.stponr=mako.stponr
		and apko.eindat=mako.eindat
		and apko.ferweg = 1
		where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :teilnr_div
				and mako.matnum = :matnum_ltg1
				and (mako.eindat <= :real_today or mako.eindat = 0)
				and (mako.ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_wire_shorter");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mengfk in apko");
		EXEC SQL
		select apko.mengfk into :meng_ltg2 from mako
		join apko
		on apko.firmnr =mako.firmnr
		and apko.plwerk=mako.plwerk
		and apko.teilnr=mako.teilnr
		and apko.stponr=mako.stponr
		and apko.eindat=mako.eindat
		and apko.ferweg = 1
		where       mako.firmnr = :apzd.firmnr
				and mako.plwerk = :apzd.plwerk
				and mako.teilnr = :teilnr_div
				and mako.matnum = :matnum_ltg2
				and (mako.eindat <= :real_today or mako.eindat = 0)
				and (mako.ausdat >= :real_today or mako.ausdat = 0);
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "mako_wire_shorter");
		if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D113_write_leadset_P10: WARN no mengfk in apko");
		/********************************/
		rec_leadset_P10.ReducedLeadLength	= fabs((meng_ltg2*1000) - (meng_ltg1*1000));
		if(meng_ltg1 < meng_ltg2)
			rec_leadset_P10.ReducedWire		=1;
		else if(meng_ltg1 == meng_ltg2)
			rec_leadset_P10.ReducedWire		=0;
		else 
			rec_leadset_P10.ReducedWire		=2;
	} else {
		rec_leadset_P10.PitchLength			= 0.0;
		rec_leadset_P10.OpenEndLength1		= 0.0;
		rec_leadset_P10.OpenEndLength2		= 0.0;
	}
	/* Print1 */
	strcpy(rec_leadset_P10.Print1Begin1Text,	  apzd.s1drck);
	rec_leadset_P10.Print1Begin1Offset		= 20;
	strcpy(rec_leadset_P10.Print1End1Text,	  apzd.s2drck);
	rec_leadset_P10.Print1End1Offset		= 20;
	strcpy(rec_leadset_P10.Print1ContText,	  apzd.s1drck);
	rec_leadset_P10.Print1ContOffset		= 20;
	/* Print2 */
	if (cable_type != 'S') {
		strcpy(rec_leadset_P10.Print2Begin1Text,  apzd_ltg2.s1drck);
		rec_leadset_P10.Print2Begin1Offset	= 20;
		strcpy(rec_leadset_P10.Print2End1Text,	  apzd_ltg2.s2drck);
		rec_leadset_P10.Print2End1Offset	= 20;
		strcpy(rec_leadset_P10.Print2ContText,	  apzd_ltg2.s1drck);
		rec_leadset_P10.Print2ContOffset	= 20;
	}
	/* if harness tnrbe2 contains text "alternate" or "print" */
	DBG_log(4, "D113_write_leadset_P10: magd.tnrbe2 [%s]", magd.tnrbe2);
	if (strstr(magd.tnrbe2, MAGD_TNRBE2_ALT) != NULL) {
		DBG_log(4, "D113_write_leadset_P10: magd.tnrbe2 [%s] has '%s'", magd.tnrbe2, MAGD_TNRBE2_ALT);
		rec_leadset_P10.Print1ContAlternateText	= 1;
		rec_leadset_P10.Print2ContAlternateText	= 1;
	}
	if (strstr(magd.tnrbe2, MAGD_TNRBE2_PRINT) != NULL) {
		DBG_log(4, "D113_write_leadset_P10: magd.tnrbe2 [%s] has '%s'", magd.tnrbe2, MAGD_TNRBE2_PRINT);
		rec_leadset_P10.Print1ContAlternateText	= 0;
		rec_leadset_P10.Print2ContAlternateText	= 0;
	}

	rec_leadset_P10.MicroGraphMandatory = 0;

	rec_leadset_P10.PitchLengthTolerancePlus = 0.0;
	rec_leadset_P10.PitchLengthToleranceMinus = 0.0;
	rec_leadset_P10.CuttingOfPulledStrandsA = 0;
	rec_leadset_P10.CuttingOfPulledStrandsB = 0;
	rec_leadset_P10.UseDepositGripper = 0;
	rec_leadset_P10.Workstep = 10;
	/* write line */
	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%d;%s;%s;%.1f;%.1f;%.1f;%.1f;%.1f;%.1f;%.1f;%.1f;"
			"%d;%.1f;%.1f;%.1f;%d;%d;"						/* twisted */
			"%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%d;"	/* Print1 */
			"%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%s;%d;%d;%d;"	/* Print2 */
			"%d;%d;%d;%d;%d;%d;"													/* Hotstamp */
			"%d;"
			"%.1f;%.1f;%.1f;%.1f;"													/* Tinning */
			"%.1f;%.1f;%d;%d;%d;%d\n",
			rec_leadset_P10.Leadset,		rec_leadset_P10.ProdVersion,
			rec_leadset_P10.Font1,			rec_leadset_P10.Font2,
			rec_leadset_P10.StrippingLength1,	rec_leadset_P10.PartStripLength1,
			rec_leadset_P10.StrippingLength2,	rec_leadset_P10.PartStripLength2,
			rec_leadset_P10.StrippingLength3,	rec_leadset_P10.PartStripLength3,
			rec_leadset_P10.StrippingLength4,	rec_leadset_P10.PartStripLength4,

			rec_leadset_P10.TwistWireLength,	rec_leadset_P10.PitchLength,
			rec_leadset_P10.OpenEndLength1,		rec_leadset_P10.OpenEndLength2,
			rec_leadset_P10.ReducedLeadLength,	rec_leadset_P10.ReducedWire,

			rec_leadset_P10.Print1Begin1Text,	rec_leadset_P10.Print1Begin1Offset,
			rec_leadset_P10.Print1Begin1TurnText,
			rec_leadset_P10.Print1Begin2Text,	rec_leadset_P10.Print1Begin2Offset,
			rec_leadset_P10.Print1Begin2TurnText,
			rec_leadset_P10.Print1Begin3Text,	rec_leadset_P10.Print1Begin3Offset,
			rec_leadset_P10.Print1Begin3TurnText,

			rec_leadset_P10.Print1End1Text,		rec_leadset_P10.Print1End1Offset,
			rec_leadset_P10.Print1End1TurnText,
			rec_leadset_P10.Print1End2Text,		rec_leadset_P10.Print1End2Offset,
			rec_leadset_P10.Print1End2TurnText,
			rec_leadset_P10.Print1End3Text,		rec_leadset_P10.Print1End3Offset,
			rec_leadset_P10.Print1End3TurnText,

			rec_leadset_P10.Print1ContText,		rec_leadset_P10.Print1ContOffset,
			rec_leadset_P10.Print1ContTurnText,	rec_leadset_P10.Print1ContAlternateText,

			rec_leadset_P10.Print2Begin1Text,	rec_leadset_P10.Print2Begin1Offset,
			rec_leadset_P10.Print2Begin1TurnText,
			rec_leadset_P10.Print2Begin2Text,	rec_leadset_P10.Print2Begin2Offset,
			rec_leadset_P10.Print2Begin2TurnText,
			rec_leadset_P10.Print2Begin3Text,	rec_leadset_P10.Print2Begin3Offset,
			rec_leadset_P10.Print2Begin3TurnText,

			rec_leadset_P10.Print2End1Text,		rec_leadset_P10.Print2End1Offset,
			rec_leadset_P10.Print2End1TurnText,
			rec_leadset_P10.Print2End2Text,		rec_leadset_P10.Print2End2Offset,
			rec_leadset_P10.Print2End2TurnText,
			rec_leadset_P10.Print2End3Text,		rec_leadset_P10.Print2End3Offset,
			rec_leadset_P10.Print2End3TurnText,

			rec_leadset_P10.Print2ContText,		rec_leadset_P10.Print2ContOffset,
			rec_leadset_P10.Print2ContTurnText,	rec_leadset_P10.Print2ContAlternateText,

			rec_leadset_P10.Hotstamp1BeginOffset,	rec_leadset_P10.Hotstamp1EndOffset,
			rec_leadset_P10.Hotstamp1BeginAndEnd,
			rec_leadset_P10.Hotstamp2BeginOffset,	rec_leadset_P10.Hotstamp2EndOffset,
			rec_leadset_P10.Hotstamp2BeginAndEnd,	

			rec_leadset_P10.MicroGraphMandatory,

			rec_leadset_P10.TinningLength1,			rec_leadset_P10.TinningLength2,			
			rec_leadset_P10.TinningLength3,			rec_leadset_P10.TinningLength4,

			rec_leadset_P10.PitchLengthTolerancePlus, rec_leadset_P10.PitchLengthToleranceMinus,
			rec_leadset_P10.CuttingOfPulledStrandsA,  rec_leadset_P10.CuttingOfPulledStrandsB,
			rec_leadset_P10.UseDepositGripper, 		  rec_leadset_P10.Workstep

				);

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D113_write_leadset_P10: [%s] csv line too long", prec_leadset_main->Leadset);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line (fdg_leadset_P1, buffer_line);

	DBG_log(3, "D113_write_leadset_P10: Leadset [%s] ret: %d", prec_leadset_main->Leadset, rc);
	return rc;
}


/**********************************************************************
 *   Name            :  D119_write_leadset_P20
 *   Funktion        :  write leadset_P20
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   			fbwstort, apfw, magd  (teilnr)
 *   			apzd(_ltg2), apag(_ltg2), maks, apvd(T)   (telnr/arbgnr)
 *			mako_ltg1/2  magd_ltg1/2 apzd_ltg2 apag_ltg2
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D119_write_leadset_P20(char cable_type, rec_leadset_main_t *prec_leadset_main)
{	
	int rc = OK;
	int rlen;
	rec_leadset_P20_t rec_leadset_P20;
	char buffer_line [MAX_CSVLINE_LEN+1];

	DBG_log(4, "D119_write_leadset_P20: Leadset [%s]", prec_leadset_main->Leadset);
	memset(&rec_leadset_P20, 0, sizeof(rec_leadset_P20_t));	/* clear all */
	strcpy(rec_leadset_P20.Leadset, prec_leadset_main->Leadset);
	rec_leadset_P20.ProdVersion = 1;
	if (apag.arbgnr == 10){
		rec_leadset_P20.Workstep = 11;
	}
	else {
		rec_leadset_P20.Workstep = apag.arbgnr;
	}


	/* get apvd */
	memset(&apvd, 0, sizeof(APVD));             /* set all 0, if not found*/
	EXEC SQL
		select * into :apvd from apvd
		where       apvd.firmnr = :apag.firmnr
		and apvd.plwerk = :apag.plwerk
		and apvd.teilnr = :apag.teilnr
		and apvd.ferweg = 1
		and (apvd.arbgnr = :apag.arbgnr or apvd.agnr01 = :apag.arbgnr or 
				apvd.agnr02 = :apag.arbgnr or apvd.agnr03 = :apag.arbgnr or 
				apvd.agnr05 = :apag.arbgnr or apvd.agnr05 = :apag.arbgnr);
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apvd for CAO-P2");
	if (SQLCODE == SQLNOTFOUND) DBG_log(1, "D119_write_leadset_P20: WARN no apvd");

	rec_leadset_P20.TwistWireLength = apvd.endvdl * 1000;
	rec_leadset_P20.PitchLength = apvd.schllg;
	rec_leadset_P20.OpenEndLength1 = apvd.offes1;
	rec_leadset_P20.OpenEndLength2 = apvd.offes2;
	//rec_leadset_P20.PitchLengthTolerancePlus = apvd.;
	//rec_leadset_P20.PitchLengthToleranceMinus = apvd.;


	/* write line */
	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%d;%d;%d;%.1f;%.1f;%.1f;%.1f;%.1f;\n",
			rec_leadset_P20.Leadset,				rec_leadset_P20.ProdVersion,
			rec_leadset_P20.Workstep,				rec_leadset_P20.TwistWireLength,
			rec_leadset_P20.PitchLength,			rec_leadset_P20.OpenEndLength1,
			rec_leadset_P20.OpenEndLength2,			rec_leadset_P20.PitchLengthTolerancePlus,
			rec_leadset_P20.PitchLengthToleranceMinus
		       );

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D119_write_leadset_P20: [%s] csv line too long", prec_leadset_main->Leadset);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line (fdg_leadset_P2, buffer_line);

	DBG_log(3, "D119_write_leadset_P20: Leadset [%s] ret: %d", prec_leadset_main->Leadset, rc);
	return rc;
}

/**********************************************************************
 *   Name            :  D114_write_bom
 *   Funktion        :  write bom (wire, terminal & seal)
 *   Parameter       :
 *   		global set for main teilnr (DONT write !!):
 *   			fbwstort, apfw, magd  (teilnr)
 *   			apzd(_ltg2), apag(_ltg2), maks, apvd(T)   (telnr/arbgnr)
 *			mako_ltg1/2  magd_ltg1/2 apzd_ltg2 apag_ltg2
 *			mako_term.. mako_seal.. (D113_write_leadset_P10)
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D114_write_bom(char cable_type, rec_leadset_main_t *prec_leadset_main)
{
	int rc = OK, status, apko_cnt, apag_cnt;
	rec_bom_t rec_bom;
	mat_ptr_t mtp;		/* material DB ptr */
	int ltgnum;			/* LTG 1/2  (index in mtp) */
	char ltgnum_str[80];
	MAKOTYP *makop;
	MAKOTYP *mako_xp;		/* mako_ltg, mako_term or mako_seal */
	MAGDTYP *magdp;
	APZD    *apzdp;
	char buffer_line [MAX_CSVLINE_LEN+1];
	int rlen;

	DBG_log(3, "k: Leadset '%c' [%s]", cable_type, prec_leadset_main->Leadset);
	memset(&rec_bom, 0, sizeof(rec_bom_t));	/* clear all */
	/* set identicals for all */
	strcpy(rec_bom.Leadset, prec_leadset_main->Leadset);
	rec_bom.ProdVersion = 1;

	if ( !strcasecmp(g.do_task, "MAT_P1") ) {
		rec_bom.workstep = 10;

		strcpy(rec_bom.Phantom, "no");
		if(zeta == 'N'){
			rec_bom.Posnumber = 1;
			rec_bom.MaterialProdVer = 0;
			if (!prec_leadset_main->mako_ltg_swap) {	/* correct matnum order, LTG's swapped */
				mtp.mako_ltg[0] = &mako_ltg1;
				mtp.mako_ltg[1] = &mako_ltg2;
				mtp.magd_ltg[0] = &magd_ltg1;
				mtp.magd_ltg[1] = &magd_ltg2;
				mtp.apzd_ltg[0] = &apzd;
				mtp.apzd_ltg[1] = &apzd_ltg2;
				mtp.mako_term_s1[0] = &mako_term11;	/* ltg 1 */
				mtp.mako_term_s2[0] = &mako_term12;
				mtp.mako_seal_s1[0] = &mako_seal11;
				mtp.mako_seal_s2[0] = &mako_seal12;
				mtp.mako_term_s1[1] = &mako_term21;	/* ltg 2 */
				mtp.mako_term_s2[1] = &mako_term22;
				mtp.mako_seal_s1[1] = &mako_seal21;
				mtp.mako_seal_s2[1] = &mako_seal22;
			} else {					/* swapped ltg */
				mtp.mako_ltg[0] = &mako_ltg2;
				mtp.mako_ltg[1] = &mako_ltg1;
				mtp.magd_ltg[0] = &magd_ltg2;
				mtp.magd_ltg[1] = &magd_ltg1;
				mtp.apzd_ltg[0] = &apzd_ltg2;
				mtp.apzd_ltg[1] = &apzd;
				mtp.mako_term_s1[0] = &mako_term21;	/* ltg 1 */
				mtp.mako_term_s2[0] = &mako_term22;
				mtp.mako_seal_s1[0] = &mako_seal21;
				mtp.mako_seal_s2[0] = &mako_seal22;
				mtp.mako_term_s1[1] = &mako_term11;	/* ltg 2 */
				mtp.mako_term_s2[1] = &mako_term12;
				mtp.mako_seal_s1[1] = &mako_seal11;
				mtp.mako_seal_s2[1] = &mako_seal12;
			}
			/**** LTG1 & LTG2 */
			for (ltgnum = 0; ltgnum < 2; ltgnum++) {
				if (cable_type == 'S' && ltgnum == 1) break;	/* only ltg1 */
				apzdp = mtp.apzd_ltg[ltgnum];
				magdp = mtp.magd_ltg[ltgnum];
				makop = mtp.mako_ltg[ltgnum];

				DBG_log(3, "D114_write_bom: ltg:%d apzd ltponr: %d, s1/2meng: %.1f/%.1f s1/s2ponr: %d/%d z1/z2ponr: %d/%d",
						ltgnum+1, apzdp->ltponr, apzdp->s1meng, apzdp->s2meng,
						apzdp->s1ponr, apzdp->s2ponr, apzdp->z1ponr, apzdp->z2ponr);
				if (apzdp->ltponr) {
					mako_xp = makop;				/* is ltg */
					sprintf(ltgnum_str, "LTG-%d (%s)", ltgnum+1, makop->matnum);
					if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_LTG,
									1 + ltgnum, ltgnum_str)) != OK) {
						return ERROR;
					}
				}
				switch (cable_type) { /* sides already swapped in (both) apzd for 'T & D' */
					default:
					case 'S':
					case 'T':
						if (apzdp->s1ponr) {
							mako_xp = mtp.mako_term_s1[ltgnum];		/* term side1 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_TERM,
											1 + (ltgnum*2), "Term-S1")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->s2ponr) {
							mako_xp = mtp.mako_term_s2[ltgnum];		/* term side2 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_TERM,
											2 + (ltgnum*2), "Term-S2")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->z1ponr) {
							mako_xp = mtp.mako_seal_s1[ltgnum];		/* seal side1 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_SEAL,
											1 + (ltgnum*2), "Seal-S1")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->z2ponr) {
							mako_xp = mtp.mako_seal_s2[ltgnum];		/* seal side2 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_SEAL,
											2 + (ltgnum*2), "Seal-S2")) != OK) {
								return ERROR;
							}
						}
						break;
					case 'D': 		/* S2 is DoubleCrimp -> Side 2 */
						if (apzdp->s1ponr) {
							mako_xp = mtp.mako_term_s1[ltgnum];		/* term side1 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_TERM,
											1 + (ltgnum*2), "Term-S1")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->s2ponr && ltgnum == 0) {
							mako_xp = mtp.mako_term_s2[ltgnum];		/* term side2 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_TERM,
											2 + (ltgnum*2), "DBL-Term-S2")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->z1ponr) {
							mako_xp = mtp.mako_seal_s1[ltgnum];		/* seal side1 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_SEAL,
											1 + (ltgnum*2), "Seal-S1")) != OK) {
								return ERROR;
							}
						}
						if (apzdp->z2ponr) {
							mako_xp = mtp.mako_seal_s2[ltgnum];		/* seal side2 */
							if ((status = D1141_write_bomdata(cable_type, &rec_bom, &mtp, ltgnum, mako_xp, BOM_RES_PLACE_SEAL,
											2 + (ltgnum*2), "Seal-S2")) != OK) {
								return ERROR;
							}
						}
						break;
				}
			}
		}
		else if(zeta == 'Y'){
			if(!strcmp(p_Leadset_bom,rec_bom.Leadset)){
				mako_cnt++;
			}
			else {
				strcpy(p_Leadset_bom,rec_bom.Leadset);
				mako_cnt = 1;
			}
			rec_bom.Posnumber = mako_cnt;
			strcpy(mako.matnum," ");
			strcpy(mako.komtnr," ");

			EXEC SQL --declare c_mako cursor with hold for
				SELECT 	mako.matnum,mako.komtnr,magd.matken,magd.tnrbe1
				INTO :mako.matnum,:mako.komtnr,:magd_bom.matken,:magd_bom.tnrbe1
				FROM apzd
				JOIN mako
				ON mako.firmnr = apzd.firmnr
				AND mako.plwerk = apzd.plwerk
				AND mako.teilnr = apzd.teilnr
				AND mako.stponr = apzd.ltponr
				JOIN magd
				ON magd.firmnr = apzd.firmnr
				AND magd.teilnr = mako.komtnr
				WHERE apzd.firmnr = :apag.firmnr
				AND   apzd.plwerk = :apag.plwerk
				AND   apzd.teilnr = :apag.teilnr
				AND   apzd.ferweg = 1
				AND   apzd.arbgnr = :apag.arbgnr
				AND (mako.eindat <= :real_today or mako.eindat = 0)
				AND (mako.ausdat >= :real_today or mako.ausdat = 0)
				ORDER BY arbgnr;

			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select mako.matnum");

			if(strcmp(magd_bom.matken,"GEH")) {
				kill_all_blanks(mako.matnum);
				if(strlen(mako.matnum) && strcmp(magd_bom.matken,"GEH")) {
					sprintf(rec_bom.MaterialNumber, "%s#%s#0", apag.teilnr, mako.matnum);
					rec_bom.MaterialProdVer = 1;
					rec_bom.RessourceType = 9;
					rec_bom.quantity = 1.0f;
				}
				else {
					sprintf(rec_bom.MaterialNumber, "%s", mako.komtnr);
					rec_bom.MaterialProdVer = 0;

					strcpy(rec_bom.description, magd_bom.tnrbe1);

					kill_all_blanks(magd_bom.matken);
					if( !strcmp(magd_bom.matken,"CIRC") || !strcmp(magd_bom.matken,"LTG") || !strcmp(magd_bom.matken,"SLTG") ){
						rec_bom.RessourceType = 1;
					}
					else if ( !strcmp(magd_bom.matken,"KAB") || !strcmp(magd_bom.matken,"CKONT") || !strcmp(magd_bom.matken,"LKONT") ||
							!strcmp(magd_bom.matken,"OKONT") || !strcmp(magd_bom.matken,"SKONT") || !strcmp(magd_bom.matken,"VKONT")){
						rec_bom.RessourceType = 2;	
					}
					else if( !strcmp(magd_bom.matken,"BLIND") || !strcmp(magd_bom.matken,"EDICH") ){
						rec_bom.RessourceType = 3;
					}
					else {
						rec_bom.RessourceType = 13;	
					}

					EXEC SQL SELECT mengfk into :apko.mengfk
						FROM apko
						WHERE firmnr = :apag.firmnr
						and plwerk   = :apag.plwerk
						and teilnr   = :apag.teilnr
						and arbgnr   = :apag.arbgnr 
						and stponr   = :apzd.ltponr
						order by eindat desc
						limit 1;

					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select apko.mengfk");
					rec_bom.quantity = apko.mengfk;
				}
				rec_bom.RessourcePlace = 1l;
			}
			else if(!strcmp(magd_bom.matken,"GEH")) {
				rec_bom.RessourceType = 12;
				rec_bom.RessourcePlace = 1l;
				rec_bom.MaterialProdVer = 1;
				EXEC SQL declare c_mako cursor with hold for
					SELECT 	mako.komtnr,magd.tnrbe1,apko.mengfk
					INTO :mako.komtnr,:magd_bom.tnrbe1,:apko.mengfk
					FROM apko
					JOIN mako
					ON mako.firmnr = apko.firmnr
					AND mako.plwerk = apko.plwerk
					AND mako.teilnr = apko.teilnr
					AND mako.stponr = apko.stponr
					JOIN magd
					ON magd.firmnr = apko.firmnr
					AND magd.teilnr = mako.komtnr
					WHERE apko.firmnr = :apag.firmnr
					AND   apko.plwerk = :apag.plwerk
					AND   apko.teilnr = :apag.teilnr
					--AND   apko.ferweg = 1
					AND   apko.arbgnr = :apag.arbgnr
					ORDER BY arbgnr;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_mako");
				EXEC SQL open c_mako;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_mako");

				apko_cnt = 0;
				while(1) {
					EXEC SQL fetch c_mako;
					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_mako");
					if (SQLCODE == SQLNOTFOUND) {
						mako_cnt--;
						break;
					}
					rec_bom.Posnumber = mako_cnt;
					strcpy(rec_bom.description, magd_bom.tnrbe1);
					rec_bom.quantity = apko.mengfk;
					sprintf(rec_bom.MaterialNumber, "%s", mako.komtnr);

					rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
							"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
							rec_bom.Leadset,		rec_bom.ProdVersion,
							rec_bom.workstep,		rec_bom.Posnumber,
							rec_bom.MaterialNumber,	rec_bom.MaterialProdVer,
							rec_bom.RessourceType,	rec_bom.RessourcePlace,
							rec_bom.quantity,		rec_bom.Phantom,
							rec_bom.description
						       );

					if (rlen == MAX_CSVLINE_LEN) {
						DBG_err(1, "D114_write_bom: [%s] Pos:%ld csv line too long", rec_bom.Leadset, rec_bom.Posnumber);
						buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
						return ERROR;
					}
					rc = G_write_line (fdg_bom, buffer_line);

					mako_cnt++;					
				}
				EXEC SQL close c_mako;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_mako");
			}
			/* write line */
			if(strcmp(magd_bom.matken,"GEH")) {
				rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
						"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
						rec_bom.Leadset,		rec_bom.ProdVersion,
						rec_bom.workstep,		rec_bom.Posnumber,
						rec_bom.MaterialNumber,	rec_bom.MaterialProdVer,
						rec_bom.RessourceType,	rec_bom.RessourcePlace,
						rec_bom.quantity,		rec_bom.Phantom,
						rec_bom.description
					       );

				if (rlen == MAX_CSVLINE_LEN) {
					DBG_err(1, "D114_write_bom: [%s] Pos:%ld csv line too long", rec_bom.Leadset, rec_bom.Posnumber);
					buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
					return ERROR;
				}
				rc = G_write_line (fdg_bom, buffer_line);
			}
		}
	}

	if ( !strcasecmp(g.do_task, "MAT_P2") ) {
		if (apag.arbgnr == 10){
			rec_bom.workstep = 11;
		}
		else {
			rec_bom.workstep = apag.arbgnr;
		}

		//1st selection
		EXEC SQL declare c_apko cursor with hold for
			SELECT distinct(stponr) into :apko.stponr
			FROM apko
			WHERE firmnr = :apag.firmnr
			and plwerk   = :apag.plwerk
			and teilnr   = :apag.teilnr
			and arbgnr   = :apag.arbgnr
			order by stponr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apko");
		EXEC SQL open c_apko;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apko");

		apko_cnt = 0;
		while(1) {

			/* get apfw.teilnr  */
			EXEC SQL fetch c_apko;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apko");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(3, "D114_write_bom: apko fetch end (number of entries for teilnr[%s] is : %d)", apag.teilnr, apko_cnt);
				break;
			}
			apko_cnt++;
			rec_bom.Posnumber = apko_cnt;

			EXEC SQL SELECT matnum,komtnr into :mako.matnum, :mako.komtnr
				FROM mako
				WHERE firmnr = :apag.firmnr
				and plwerk   = :apag.plwerk
				and teilnr   = :apag.teilnr
				and stponr	 = :apko.stponr
				and (mako.eindat <= :real_today or mako.eindat = 0)
				and (mako.ausdat >= :real_today or mako.ausdat = 0);

			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select mako.matnum");

			kill_all_blanks(mako.matnum);
			if(strlen(mako.matnum)) {
				sprintf(rec_bom.MaterialNumber, "%s#%s", apag.teilnr, mako.matnum);
				rec_bom.MaterialProdVer = 1;
				rec_bom.RessourceType = 9;				
			}
			else {
				sprintf(rec_bom.MaterialNumber, "%s", mako.komtnr);
				rec_bom.MaterialProdVer = 0;

				EXEC SQL SELECT * into :magd_bom
					FROM magd
					WHERE firmnr = :apag.firmnr
					and teilnr   = :mako.komtnr;			
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select magd.matken");

				strcpy(rec_bom.description, magd_bom.tnrbe1);

				kill_all_blanks(magd_bom.matken);
				if( !strcmp(magd_bom.matken,"CIRC") || !strcmp(magd_bom.matken,"LTG") || !strcmp(magd_bom.matken,"SLTG") ){
					rec_bom.RessourceType = 1;
					if ((status = D115_write_wire(cable_type, &rec_bom, &magd_bom)) == ERROR) {
						return ERROR;
					}
				}
				else if ( !strcmp(magd_bom.matken,"KAB") || !strcmp(magd_bom.matken,"CKONT") || !strcmp(magd_bom.matken,"LKONT") ||
						!strcmp(magd_bom.matken,"OKONT") || !strcmp(magd_bom.matken,"SKONT") || !strcmp(magd_bom.matken,"VKONT") ||
						!strcmp(magd_bom.matken,"VERB")|| !strcmp(magd_bom.matken,"BUSB") ){
					rec_bom.RessourceType = 2;	
					if ((status = D116_write_terminal(cable_type, &rec_bom, &magd_bom)) == ERROR) {
						return ERROR;
					}
				}
				else if( !strcmp(magd_bom.matken,"BLIND") || !strcmp(magd_bom.matken,"EDICH") ){
					rec_bom.RessourceType = 3;	
					if ((status = D117_write_seal(cable_type, &rec_bom, &magd_bom)) == ERROR) {
						return ERROR;
					}
				}
				else {
					rec_bom.RessourceType = 13;	
				}
			}

			EXEC SQL SELECT mengfk into :apko.mengfk
				FROM apko
				WHERE firmnr = :apag.firmnr
				and plwerk   = :apag.plwerk
				and teilnr   = :apag.teilnr
				and arbgnr   = :apag.arbgnr 
				and stponr   = :apko.stponr
				order by eindat desc
				limit 1;

			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select apko.mengfk");
			if (rec_bom.RessourceType == BOM_RES_PLACE_LTG) 
				rec_bom.quantity = apko.mengfk  * 1000;//in mm}
			else
				rec_bom.quantity = apko.mengfk ;
			kill_all_blanks(apag.prplan);
			if (!strlen(apag.prplan)){
				if (rec_bom.RessourceType == 2 || rec_bom.RessourceType == 3){

					sprintf(arbplz,"%d",apag.arbgnr);

					EXEC SQL SELECT apzd.s1ponr into :apzd.s1ponr
						FROM apag
						JOIN apzd
						ON apzd.firmnr = :apag.firmnr
						AND apzd.plwerk = :apag.plwerk
						AND apzd.teilnr = :apag.teilnr
						AND apzd.arbgnr = apag.arbgnr
						WHERE apag.firmnr = :apag.firmnr
						AND   apag.plwerk = :apag.plwerk
						AND   apag.teilnr = :apag.teilnr
						AND   apag.arbplz = :arbplz
						ORDER BY apzd.s1ponr DESC
						limit 1;

					SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select mako.matnum");

					//kill_all_blanks(apzd.s1ponr);
					if(apzd.s1ponr){
						rec_bom.RessourcePlace = 2l;
					}
					else {
						rec_bom.RessourcePlace = 1l;
					}
				}
				else {
					rec_bom.RessourcePlace = 1l;
				}
			}
			else {
				rec_bom.RessourcePlace = atol(apag.prplan);
			}

			/* write line */
			rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
					"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
					rec_bom.Leadset,		rec_bom.ProdVersion,
					rec_bom.workstep,		rec_bom.Posnumber,
					rec_bom.MaterialNumber,	rec_bom.MaterialProdVer,
					rec_bom.RessourceType,	rec_bom.RessourcePlace,
					rec_bom.quantity,		rec_bom.Phantom,
					rec_bom.description
				       );

			if (rlen == MAX_CSVLINE_LEN) {
				DBG_err(1, "D114_write_bom: [%s] Pos:%ld csv line too long", rec_bom.Leadset, rec_bom.Posnumber);
				buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
				return ERROR;
			}
			rc = G_write_line (fdg_bom, buffer_line);

		}
		EXEC SQL close c_apko;

		//2nd selection
		EXEC SQL declare c_apag1 cursor with hold for
			SELECT arbgnr into :apag_bom.arbgnr
			FROM apag
			WHERE firmnr = :apag.firmnr
			and plwerk   = :apag.plwerk
			and teilnr   = :apag.teilnr
			and arbplz   = :apag.arbgnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apag1");
		EXEC SQL open c_apag1;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apag1");

		while(1) {

			/* get apfw.teilnr  */
			EXEC SQL fetch c_apag1;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apag1");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(3, "D114_write_bom: apko fetch end (number of entries for teilnr[%s] is : %d)", apag.teilnr, apko_cnt);
				break;
			}

			EXEC SQL declare c_apko1 cursor with hold for
				SELECT distinct(stponr) into :apko.stponr
				FROM apko
				WHERE firmnr = :apag.firmnr
				and plwerk   = :apag.plwerk
				and teilnr   = :apag.teilnr
				and arbgnr   = :apag_bom.arbgnr
				order by stponr;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apko1");
			EXEC SQL open c_apko1;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apko1");

			while(1) {

				/* get apfw.teilnr  */
				EXEC SQL fetch c_apko1;
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apko1");
				if (SQLCODE == SQLNOTFOUND) {
					DBG_log(3, "D114_write_bom: apko fetch end (number of entries for teilnr[%s] is : %d)", apag.teilnr, apko_cnt);
					break;
				}

				EXEC SQL SELECT matnum into :mako.matnum
					FROM mako
					WHERE firmnr = :apag.firmnr
					and plwerk   = :apag.plwerk
					and teilnr   = :apag.teilnr
					and stponr	 = :apko.stponr
					order by aendat desc
					limit 1;

				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select mako.matnum");

				EXEC SQL SELECT dplcrp into :apzd.dplcrp
					FROM apzd
					WHERE firmnr = :apag.firmnr
					and plwerk   = :apag.plwerk
					and teilnr   = :apag.teilnr
					and ltponr	 = :apko.stponr
					order by aendat desc
					limit 1;

				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select mako.matnum");

				kill_all_blanks(mako.matnum);
				kill_all_blanks(apzd.dplcrp);
				if(strlen(mako.matnum)) {
					if(strlen(apzd.dplcrp)) {
						sprintf(rec_bom.MaterialNumber, "%s#%s#%s", apag.teilnr, mako.matnum, apzd.dplcrp);
					}
					else {
						sprintf(rec_bom.MaterialNumber, "%s#%s#0", apag.teilnr, mako.matnum);
					}

					rec_bom.MaterialProdVer = 1;
					rec_bom.RessourceType = 9;
					strcpy(rec_bom.description, mako.matnum);
					apko_cnt++;		
					rec_bom.Posnumber = apko_cnt;	
				}
				else {
					continue;
				}

				rec_bom.quantity = 1;//apko.mengfk;
				rec_bom.RessourcePlace = 1l;
				
				//Building BOM for Twist Wire with correct interconnection					
                EXEC SQL SELECT arbgnr 
					FROM apvd
					WHERE firmnr = :apag.firmnr
					and plwerk   = :apag.plwerk
					and teilnr   = :apag.teilnr
					and arbgnr	 = :apag_bom.arbgnr;
			
				SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "select apvd.arbgnr"); 
                if (SQLCODE == SQLNOTFOUND && strlen(apzd.dplcrp)) {
					DBG_log(3, "D114_write_bom: apvd end write_line for teilnr[%s], arbgnr[%d])", apag.teilnr, apag_bom.arbgnr);
					continue;
				}		
				
				/* write line */
				rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
						"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
						rec_bom.Leadset,		rec_bom.ProdVersion,
						rec_bom.workstep,		rec_bom.Posnumber,
						rec_bom.MaterialNumber,	rec_bom.MaterialProdVer,
						rec_bom.RessourceType,	rec_bom.RessourcePlace,
						rec_bom.quantity,		rec_bom.Phantom,
						rec_bom.description
					       );

				if (rlen == MAX_CSVLINE_LEN) {
					DBG_err(1, "D114_write_bom: [%s] Pos:%ld csv line too long", rec_bom.Leadset, rec_bom.Posnumber);
					buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
					return ERROR;
				}
				rc = G_write_line (fdg_bom, buffer_line);

			}
			EXEC SQL close c_apko1;
		}

		EXEC SQL close c_apag1;	

		apag_cnt = apko_cnt;

		//3rd selection
		EXEC SQL declare c_apag2 cursor with hold for
			SELECT a1.* into :apag_bom
			FROM apag a1
			join apag a2
			on a2.firmnr 	= :apag.firmnr
  			and a2.plwerk   = :apag.plwerk
  			and a2.teilnr   = a1.teilnr
  			and a2.arbgnr 	= cast(NULLIF(TRIM(a1.arbplz),'') as int)
  			--and a2.arbgnr   = cast(NULLIF(TRIM(a2.zaufnr),'') as int)
  			and a2.ferweg	= 1
		WHERE a1.firmnr 	= :apag.firmnr
			and a1.plwerk   = :apag.plwerk
			and a1.teilnr   = :apag.teilnr         
			and a1.arbplz   = :apag.arbgnr
			and a1.ferweg 	= 1			/* CAO_FERWEG */
			and a1.avtxsl between :avtxsl_von and :avtxsl_bis
			-- and a1.arbgnr   = cast(NULLIF(TRIM(a1.zaufnr),'') as int)
			and a1.aendat >= :sql_tmplong;
			
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_apag2");
		EXEC SQL open c_apag2;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_apag2");

		while(1) {

			/* get apfw.teilnr  */
			EXEC SQL fetch c_apag2;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_apag2");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(3, "D114_write_bom: apag fetch end (number of entries for teilnr[%s] is : %d)", apag.teilnr, apag_cnt);
				break;
			}


			kill_all_blanks(apag_bom.zaufnr);
			if(strlen(apag_bom.zaufnr) ) {
				sprintf(rec_bom.MaterialNumber, "%s-%s", apag.teilnr, apag_bom.zaufnr);

				rec_bom.MaterialProdVer = 1;
				rec_bom.RessourceType = 9;
				strcpy(rec_bom.description, rec_bom.MaterialNumber);
				apag_cnt++;		
				rec_bom.Posnumber = apag_cnt;	

				rec_bom.quantity = 1;//apko.mengfk;
				rec_bom.RessourcePlace = 1l;
				/* write line */
				if(strcmp(rec_bom.Leadset,rec_bom.MaterialNumber))
				{
				rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
						"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
						rec_bom.Leadset,		rec_bom.ProdVersion,
						rec_bom.workstep,		rec_bom.Posnumber,
						rec_bom.MaterialNumber,	rec_bom.MaterialProdVer,
						rec_bom.RessourceType,	rec_bom.RessourcePlace,
						rec_bom.quantity,		rec_bom.Phantom,
						rec_bom.description
					       );

				if (rlen == MAX_CSVLINE_LEN) {
					DBG_err(1, "D114_write_bom: [%s] Pos:%ld csv line too long", rec_bom.Leadset, rec_bom.Posnumber);
					buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
					return ERROR;
				}
				rc = G_write_line (fdg_bom, buffer_line);
				}

			}

		}

		EXEC SQL close c_apag2;
	}

	if (g.beauty_out) G_write_line (fdg_bom, "\n");

	DBG_log(3, "D114_write_bom: Leadset [%s] ret: %d", prec_leadset_main->Leadset, rc);
	return rc;

}

/**********************************************************************
 *   Name            :  D1141_write_bomdata
 *   Funktion        :  get on BOM data and write line
 *   Parameter       :  prec_bom: BOM csv file record
 *			mtpp:  DB ptr to ltg1/2
 *			ltgnum: ltg1(0) or ltg2(1)
 *			mako_xp: mako of ltg, term, seal
 *			res_type & res_place
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D1141_write_bomdata(char cable_type, rec_bom_t *prec_bom, mat_ptr_t *mtpp, int ltgnum,
		MAKOTYP *mako_xp, int res_type, int res_place, const char *info)
{
	int rc = OK;
	int status;
	char *teilnr;
	/* MAKOTYP *makop; */
	MAGDTYP *magdp;
	APZD    *apzdp;
	magd_list_t *magd_list_p;	/* ptr to list item of ltg, term or seal */

	apzdp = mtpp->apzd_ltg[ltgnum];
	magdp = mtpp->magd_ltg[ltgnum];
	memcpy(&mako_bomX, mako_xp, sizeof(MAKOTYP));	/* copy mako (ltg/term/seal) in tmp for SQL */

	/* get magd_list
	 * for term/seal search: mako_xp->komtnr = magd.teilnr */
	switch (res_type) {
		case BOM_RES_PLACE_LTG:
			teilnr = magdp->teilnr;		/* ltg */
			break;
		case BOM_RES_PLACE_TERM:
		case BOM_RES_PLACE_SEAL:
			teilnr = mako_xp->komtnr;		/* term or seal */
			break;
		default:
			return ERROR;
			break;
	}
	/* get or add to list */
	magd_list_p = G_get_magd(parm_firma, teilnr);
	if (!magd_list_p) {
		DBG_err(3, "D1141_write_bomdata: magd teilnr [%s] NOT found", teilnr);
		return ERROR;
	}

	if(!strcmp(magd_list_p->magd.matken,"ZINN")||!strcmp(magd_list_p->magd.matken,"LOE")){
		DBG_log(3, "D1141_write_bomdata: %s apzd teilnr [%s] arbgnr:%d (pos:%ld res_type:13 place: %d)",
				info, apzdp->teilnr, apzdp->arbgnr,
				prec_bom->Posnumber, res_place );
	}
	else {
		DBG_log(3, "D1141_write_bomdata: %s apzd teilnr [%s] arbgnr:%d (pos:%ld res_type:%d place: %d)",
				info, apzdp->teilnr, apzdp->arbgnr,
				prec_bom->Posnumber, res_type, res_place );
	}
	DBG_log(3, "D1141_write_bomdata: mako %s teilnr [%s] komtnr [%s] stponr: %d matnum [%s]",
			(res_type == BOM_RES_PLACE_LTG)? "LTG" : "T/S",
			mako_bomX.teilnr, mako_bomX.komtnr, mako_bomX.stponr, mako_bomX.matnum);

	/* get apko */
	apko_bom.mengfk = -1.0;					/* if row not found */
	sql_tmpint = apzdp->arbgnr;
	DBG_log(4, "D1141_write_bomdata: get apko_bom teilnr [%s] ferweg:1 stponr:%d eindat:%ld arbgnr:%d",
			mako_bomX.teilnr, mako_bomX.stponr, mako_bomX.eindat, sql_tmpint);
	EXEC SQL
		select * into :apko_bom from apko
		where       apko.firmnr = :mako_bomX.firmnr
		and apko.plwerk = :mako_bomX.plwerk
		and apko.teilnr = :mako_bomX.teilnr
		and apko.ferweg = 1
		and apko.stponr = :mako_bomX.stponr
		and apko.eindat = :mako_bomX.eindat
		and apko.arbgnr = :sql_tmpint;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apko_bom");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_log(1, "D1141_write_bomdata: WARN no apko_bom [%s] stponr:%d eindat:%ld arbgnr:%d",
				mako_bomX.teilnr, mako_bomX.stponr, mako_bomX.eindat, sql_tmpint);
	}
	DBG_log(4, "D1141_write_bomdata: apko: mengfk: %.2f", apko_bom.mengfk);

	/* fill part dependant fields */
	strcpy(prec_bom->MaterialNumber, mako_xp->komtnr);

	if(!strcmp(magd_list_p->magd.matken,"ZINN")||!strcmp(magd_list_p->magd.matken,"LOE")){
		prec_bom->RessourceType 	= 13;
	}
	else {
		prec_bom->RessourceType 	= res_type;
	}

	prec_bom->RessourcePlace 	= res_place;

	strcpy(matken_P10, " "); 		//matken_P10 saved and will be used in Leadset_P10.csv file
	if (res_type == BOM_RES_PLACE_LTG) {
		prec_bom->quantity		= apko_bom.mengfk * 1000;  /* in mm */
	} else {
		if(!strcmp(magd_list_p->magd.matken,"ZINN")||!strcmp(magd_list_p->magd.matken,"LOE")){
			strcpy(matken_P10, magd_list_p->magd.matken);
			prec_bom->quantity		= apko_bom.mengfk;  /* in gr */
		}
		else {
			prec_bom->quantity		= 1.0;			/* always 1 */
		}

		/* DBG_log(2, "D1141_write_bomdata: WARN apko: mengfk: %.2f for T/S ?", apko_bom.mengfk); */
	}
	snprintf(prec_bom->description, MAXLEN_BOM_DESCR, "%s: %s", info, magd_list_p->magd.tnrbe1);
	prec_bom->description[MAXLEN_BOM_DESCR] = '\0';		/* force \0-termin */

	if ((status = D1142_writeline_bom(prec_bom)) != OK) {
		return ERROR;
	}
	/* write Wire Terminal Seal csv lines (if not already written) */
	if (! magd_list_p->bom_written) {
		switch (res_type) {
			case BOM_RES_PLACE_LTG:
				if ((status = D115_write_wire(cable_type, prec_bom, &magd_list_p->magd)) == ERROR) {
					return ERROR;
				}
				break;
			case BOM_RES_PLACE_TERM:
				if ((status = D116_write_terminal(cable_type, prec_bom, &magd_list_p->magd)) == ERROR) {
					return ERROR;
				}
				break;
			case BOM_RES_PLACE_SEAL:
				if ((status = D117_write_seal(cable_type, prec_bom, &magd_list_p->magd)) == ERROR) {
					return ERROR;
				}
				break;
			default:
				return ERROR;
				break;
		}
		magd_list_p->bom_written = 1;
	} else {
		DBG_log(4, "D1141_write_bomdata: magd teilnr [%s] already written", teilnr);
	}

	prec_bom->Posnumber++;		/* next bom pos */
	DBG_log(4, "D1141_write_bomdata: ret: %d", rc);
	return rc;
}
/**********************************************************************
 *   Name            :  D114_writeline_bom
 *   Funktion        :  write bom csv line
 *   Parameter       :  prec_bom: pointer to BOM record
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D1142_writeline_bom(rec_bom_t *prec_bom)
{
	int rc = OK;
	int rlen;
	char buffer_line [MAX_CSVLINE_LEN+1];

	DBG_log(4, "D1142_writeline_bom: Leadset [%s] Pos:%ld", prec_bom->Leadset, prec_bom->Posnumber);
	/* write line */
	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%ld;%ld;%ld;%s;%ld;%ld;%ld;%.6f;%s;%s\n",
			prec_bom->Leadset,		prec_bom->ProdVersion,
			prec_bom->workstep,		prec_bom->Posnumber,
			prec_bom->MaterialNumber,	prec_bom->MaterialProdVer,
			prec_bom->RessourceType,	prec_bom->RessourcePlace,
			prec_bom->quantity,		prec_bom->Phantom,
			prec_bom->description
		       );

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D1142_writeline_bom: [%s] Pos:%ld csv line too long", prec_bom->Leadset, prec_bom->Posnumber);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line (fdg_bom, buffer_line);
	return rc;
}

/**********************************************************************
 *   Name            :  D115_write_wire
 *   Funktion        :  write Wire.csv line
 *   Parameter       :  prec_bom: BOM csv file record
 *   			magdp  : ptr to magd part
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D115_write_wire(char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp)
{
	int rc = OK;
	int rlen;
	rec_wire_t rec_wire;
	char buffer_line [MAX_CSVLINE_LEN+1];
	caocol_list_t *caocol_list_p;
	if(prec_bom!=NULL)
		DBG_log(4, "D115_write_wire: Leadset [%s] teilnr [%s] matken [%s]", prec_bom->Leadset, magdp->teilnr, magdp->matken);
	memset(&rec_wire, 0, sizeof(rec_wire_t));     /* clear all */

	/* only magd wires */
	if ((strcmp(magdp->matken, "LTG") != 0) && (strcmp(magdp->matken, "SLTG") != 0) && 
			(strcmp(magdp->matken, "SCHL") != 0) && (strcmp(magdp->matken, "SCHRU") != 0) && 
			(strcmp(magdp->matken, "LWL") != 0)) {
		DBG_log(1, "D115_write_wire: SKIP magd teilnr [%s] no WIRE matken [%s]", magdp->teilnr, magdp->matken);
		return OK;
	}
	/* WireType: get magi_ltg  */
	memset(&magi_ltg, 0, sizeof(MAGITYP));		/* clear all if not found */
	sql_tmpstr[0] = '\0';				/* ensure 0-term */
	magi_ltg.firmnr = parm_firma;
	strcpy(magi_ltg.teilnr, magdp->teilnr);
	/* ATTN: only get 1 column due to probably extension of magi */
	/* into :magi_ltg.S_MAGI_INDI_WIRECL not working SQL lib check struct column before replacement */
	EXEC SQL
		select S_MAGI_INDI_WIRECL into :sql_tmpstr from magi
		where    magi.firmnr = :magi_ltg.firmnr
		and  magi.teilnr = :magi_ltg.teilnr;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi_ltg")
		if (SQLCODE == SQLNOTFOUND) {
			DBG_err(1, "D115_write_wire: ERROR no magi teilnr [%s]", magi_ltg.teilnr);
			return ERROR;
		}
	snprintf(rec_wire.WireKey,      MAXLEN_WIRE_WIREKEY,  "%s", magdp->teilnr);
	snprintf(rec_wire.description,  MAXLEN_WIRE_DESCR,    "%s", magdp->tnrkbz);
	snprintf(rec_wire.Barcode,      MAXLEN_WIRE_BARCODE, "P%s", magdp->teilnr);
	snprintf(rec_wire.info,         MAXLEN_WIRE_INFO,     "%s", magdp->tnrbe1);
	if (g.opt_wire_klassf) {
		snprintf(rec_wire.WireType,     MAXLEN_WIRE_WIRETYPE, "%s", magdp->klassf);
	} else {
		kill_all_blanks(sql_tmpstr);
		snprintf(rec_wire.WireType,     MAXLEN_WIRE_WIRETYPE, "%s", sql_tmpstr);
	}
	snprintf(rec_wire.CrossSection, MAXLEN_WIRE_CROSSSEC, "%s", magdp->indiv1);
	snprintf(rec_wire.IsoDiameter,  MAXLEN_WIRE_ISODIA,   "%s", magdp->indiv2);
	
	EXEC SQL
		select klassf into :sql_tmpstr from magd
		where    magd.firmnr = :magi_ltg.firmnr
		and  magd.teilnr = :magi_ltg.teilnr;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd_klassf")
		if (SQLCODE == SQLNOTFOUND) {
			DBG_err(1, "D115_write_wire: ERROR no magd teilnr [%s]", magi_ltg.teilnr);
			return ERROR;
		}
	snprintf(rec_wire.IsoMaterial,  MAXLEN_WIRE_ISOMAT,   "%s", sql_tmpstr);
	
	EXEC SQL
		select indi48 into :sql_tmpstr from magi
		where    magi.firmnr = :magi_ltg.firmnr
		and  magi.teilnr = :magi_ltg.teilnr;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi_indi48")
		if (SQLCODE == SQLNOTFOUND) {
			DBG_err(1, "D115_write_wire: ERROR no magi teilnr [%s]", magi_ltg.teilnr);
			return ERROR;
		}
	kill_all_blanks(sql_tmpstr);
	if (strcmp(sql_tmpstr,"1") == 0)
		snprintf(rec_wire.TwistDirection, MAXLEN_WIRE_TWISTDIR, "%s", "1");
	else
		snprintf(rec_wire.TwistDirection, MAXLEN_WIRE_TWISTDIR, "%s", "0");

	if ((caocol_list_p = G_get_color_code(magdp->indiv3)) == NULL) {
		return ERROR;
	}
	snprintf(rec_wire.color1, MAXLEN_WIRE_COLOR, "%s", caocol_list_p->caocolors.color1);
	snprintf(rec_wire.color2, MAXLEN_WIRE_COLOR, "%s", caocol_list_p->caocolors.color2);
	snprintf(rec_wire.color3, MAXLEN_WIRE_COLOR, "%s", caocol_list_p->caocolors.color3);
	snprintf(rec_wire.color4, MAXLEN_WIRE_COLOR, "%s", caocol_list_p->caocolors.color4);

	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
			rec_wire.WireKey,			rec_wire.description,
			rec_wire.Barcode,			rec_wire.info,
			rec_wire.WireType,			rec_wire.CrossSection,
			rec_wire.IsoDiameter,		rec_wire.IsoMaterial,
			rec_wire.TwistDirection,
			rec_wire.color1,			rec_wire.color2,
			rec_wire.color3,			rec_wire.color4
		       );

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D115_write_wire: [%s] teilnr [%s] line too long", prec_bom->Leadset, magdp->teilnr);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line(fdg_wire, buffer_line);
	return rc;
}
/**********************************************************************
 *   Name            :  D120_write_wire_update
 *   Funktion        :  write all updated wire in Wire.csv 
 *   Parameter       :  prec_bom: BOM csv file record
 *   			magdp  : ptr to magd part
 *   Return Code     :   OK, ERROR
 **********************************************************************/
void D120_write_wire_update ()
{
	int n=0;
	EXEC SQL declare c_wire_updated cursor with hold for
		select magd.* into :magd_bom_update 
		from magd, magi
		where    magi.aendat >= :fbwstort.aendat
		and      magi.teilnr = magd.teilnr
		and 	 magd.matken in ('LTG','SLTG');
		EXEC SQL open c_wire_updated;
		while(1) {

			/* get apfw.teilnr  */
			EXEC SQL fetch c_wire_updated;
			
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(3, "D120_write_wire_update: write wire updated fetch end");
				break;
			}
			D115_write_wire("U", NULL, &magd_bom_update);
			n=n+1;
			
		}
		EXEC SQL close c_wire_updated;
		DBG_log(1, "D120_write_wire_update: write %d wire updated fetch end", n);
	
}
/**********************************************************************
 *   Name            :  D116_write_terminal
 *   Funktion        :  write Terminal.csv line
 *   Parameter       :  prec_bom: BOM csv file record
 *   			magdp  : ptr to magd part
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D116_write_terminal(char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp)
{
	int rc = OK;
	int rlen;
	rec_terminal_t rec_terminal;
	char buffer_line [MAX_CSVLINE_LEN+1];

	DBG_log(4, "D116_write_terminal: Leadset [%s] teilnr [%s]", prec_bom->Leadset, magdp->teilnr);
	memset(&rec_terminal, 0, sizeof(rec_terminal));     /* clear all */

	snprintf(rec_terminal.TerminalKey, 	MAXLEN_TERM_TERMKEY,  "%s", magdp->teilnr);
	snprintf(rec_terminal.description,  MAXLEN_TERM_DESCR,    "%s", magdp->tnrkbz);
	snprintf(rec_terminal.Barcode,      MAXLEN_TERM_BARCODE, "P%s", magdp->teilnr);
	snprintf(rec_terminal.info,  	MAXLEN_TERM_INFO,     "%s", magdp->tnrbe1);
	rec_terminal.TerminalType 		= 0;
	rec_terminal.DoubleCrimpHorizontal	= 0;
	rec_terminal.FeedingType 		= 1;
	rec_terminal.TerminalLength		= 0;
	rec_terminal.TerminalWidth 		= 0;
	rec_terminal.TerminalOverlength	= 0;

	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%s;%s;%s;%d;%d;%d;%d;%d;%d\n",
			rec_terminal.TerminalKey,		rec_terminal.description,
			rec_terminal.Barcode,		rec_terminal.info,
			rec_terminal.TerminalType,		rec_terminal.DoubleCrimpHorizontal,
			rec_terminal.FeedingType,		rec_terminal.TerminalLength,
			rec_terminal.TerminalWidth,		rec_terminal.TerminalOverlength
		       );

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D116_write_terminal: [%s] teilnr [%s] line too long", prec_bom->Leadset, magdp->teilnr);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line(fdg_terminal, buffer_line);
	return rc;
}

/**********************************************************************
 *   Name            :  D117_write_seal
 *   Funktion        :  write Terminal.csv line
 *   Parameter       :  prec_bom: BOM csv file record
 *   			magdp  : ptr to magd part
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int D117_write_seal(char cable_type, rec_bom_t *prec_bom, MAGDTYP *magdp)
{
	int rc = OK;
	int rlen;
	rec_seal_t rec_seal;
	char buffer_line [MAX_CSVLINE_LEN+1];
	caocol_list_t *caocol_list_p;

	DBG_log(4, "D117_write_seal: Leadset [%s] teilnr [%s]", prec_bom->Leadset, magdp->teilnr);
	memset(&rec_seal, 0, sizeof(rec_seal));     /* clear all */

	snprintf(rec_seal.SealKey, 		MAXLEN_SEAL_SEALKEY,  "%s", magdp->teilnr);
	snprintf(rec_seal.description,  	MAXLEN_SEAL_DESCR,    "%s", magdp->tnrkbz);
	snprintf(rec_seal.Barcode,      	MAXLEN_SEAL_BARCODE, "P%s", magdp->teilnr);
	snprintf(rec_seal.info,  		MAXLEN_SEAL_INFO,     "%s", magdp->tnrbe1);
	rec_seal.SealLength 		= 0;
	rec_seal.SealWidth			= 0;
	rec_seal.SealPositionTool 		= 0;

	if ((caocol_list_p = G_get_color_code(magdp->indiv3)) == NULL) {
		return ERROR;
	}
	snprintf(rec_seal.Color, MAXLEN_SEAL_COLOR, "%s", caocol_list_p->caocolors.color1);
	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%s;%s;%s;%d;%d;%d;%s\n",
			rec_seal.SealKey,		rec_seal.description,
			rec_seal.Barcode,		rec_seal.info,
			rec_seal.SealLength,	rec_seal.SealWidth,
			rec_seal.SealPositionTool,	rec_seal.Color
		       );

	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D117_write_seal: [%s] teilnr [%s] line too long", prec_bom->Leadset, magdp->teilnr);
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line(fdg_seal, buffer_line);
	return rc;
}

/**********************************************************************
 *   Name       : G_get_magd
 *   Funktion   : get magd entry
 *   Parameter  : firmnr & teilnr
 *   Return 	: ptr to found or added magd_list_t
NULL : if magd entry not found in DB
 **********************************************************************/

magd_list_t * G_get_magd(int firmnr, char *teilnr)
{
	magd_list_t	 magd_list;
	magd_list_t	*magd_list_p = NULL;


	magd_list_p = list_link_search(&g.magd_list, 's', teilnr);

	if (! magd_list_p) {
		/* get from DB */
		strcpy(sql_tmpstr, teilnr);
		sql_tmpint = firmnr;
		EXEC SQL
			select * into :magd_getX from magd
			where       magd.firmnr = :sql_tmpint
			and magd.teilnr = :sql_tmpstr;
		SQLCODE_ERROR_PUTERRDB_RETVAL(__LINE__, "LOG", "caofors.ec", "magd_getX", NULL);
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(1, "G_get_magd: ERROR no magd teilnr [%s]", teilnr);
			return NULL;
		}
		DBG_log(4, "G_get_magd: DB found magd teilnr [%s] tnrkbz [%s]", magd_getX.teilnr, magd_getX.tnrkbz);

		/* add to list */
		magd_list.bom_written = 0;
		memcpy(&magd_list.magd, &magd_getX, sizeof(MAGDTYP));
		magd_list_p = list_link_add(&g.magd_list, 's', teilnr, sizeof(magd_list_t), &magd_list);
		DBG_log(4, "G_get_magd: added  magd_list teilnr [%s]", teilnr);

	} else {
		DBG_log(4, "G_get_magd: found magd_list bom_written: %d teilnr [%s]",
				magd_list_p->bom_written, magd_list_p->magd.teilnr);
	}
	return magd_list_p;
}

/**********************************************************************
 *   Name            :  D118_write_font
 *   Funktion        :  write FONT CSV file
 *   Paramter        :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int  D118_write_font(void)
{
	int rc = OK;
	int rlen;
	char buffer_line [MAX_CSVLINE_LEN+1];
	rec_font_t fontT;

	strcpy(fontT.FontKey, "PSRFONT");
	strcpy(fontT.Name, "PSRFONT");
	strcpy(fontT.info, "");
	fontT.Height = 5;
	fontT.TextCharWidthNormal = 0;
	fontT.TextCharWidthBold = 0;
	fontT.TextCharChimney = 0;
	fontT.Color = 1;

	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%s;%s;%ld;%ld;%ld;%ld;\n",
			fontT.FontKey, fontT.Name, fontT.info,
			fontT.Height, fontT.TextCharWidthNormal, fontT.TextCharWidthBold,
			fontT.Color
		       );
	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D118_write_font: FONT  csv line too long");
		buffer_line[MAX_CSVLINE_LEN] = '\0';		/* ensure \0 if line too long */
		return ERROR;
	}
	rc = G_write_line(fdg_font, buffer_line);
	DBG_log(4, "D118_write_font ret: %d", rc);
	return rc;
}

/**********************************************************************
 *  ORDER create
 **********************************************************************/

/**********************************************************************
 *   Name            :  D300_order
 *   Funktion        :  ORDER INTERFACE
 *   Parameter        :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int D300_order()
{
	int rc = OK;
	int status;
	int ord_cnt;
	int fdg_order;
	char filename_order [MAX_FILENAME_LEN + EN];
	char buffer_line[MAX_CSVLINE_LEN+1];
	int force_delete = 0;

	set_fonatran("ORDC");
	sprintf(filename_order, "%s/cao_ordmain%s.csv", g.tmpdir, get_current_time(7, NULL, NULL));
	DBG_log(4, "D300_order: %s", filename_order);
	fdg_order = open_output (filename_order);

	if (fdg_order == - 1) {
		DBG_log(1, "ERROR: open fdg_order =%s (errno: %d, %s)", filename_order, errno, strerror(errno));
		return ERROR;
	}

	if ((ord_cnt = D310_write_orders(fdg_order, filename_order)) == ERROR) {
		return ERROR;
	}
	if (ord_cnt > 0) { /* new data written, add final line */
		strcpy(buffer_line,"99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;");
		if ((status = G_write_line (fdg_order, buffer_line)) != OK) {
			return ERROR;
		}
		/* close fd before copy / delete */
		if ((status = close_output(fdg_order, filename_order)) != OK) {
			DBG_err(1, "ERROR close (errno: %d, %s)", errno, strerror(errno));
			return ERROR;
		}
		if ((status = G_copy_file(filename_order, g.cao_in_srv, g.cao_in_dir)) != OK ) {
			return ERROR;
		}
		DBG_log(2, "D300_order: %d new orders [%s]", ord_cnt, filename_order);

	} else {
		DBG_log(2, "D300_order: no new orders");
		close_output(fdg_order, filename_order);
		force_delete = 1;		/* ignore option -k, delete empty file */
	}
	if ( G_delete_file (filename_order, force_delete) != OK) {
		return ERROR;
	}
	return rc;
}

/**********************************************************************
 *   Name            :  D310_write_orders
 *   Funktion        :  Extrat order interface data
 *   Paramter        :
 *   Return Code     :   0  : ok, but no new data
 *   			 cnt: count of written orders
 *                      -1  : ERROR
 **********************************************************************/

int D310_write_orders(int fdg_in, char * csv_file_name)
{
	int fpsl_cnt = 0;	/* show count of total entries read */
	int ord_cnt = 0;	/* return count of new orders */
	char cable_type = 'S';
	int status_fpsl1, status_fpsl2;
	rec_order_data_t rec_order_data;
	int fpsl_switch;


	DBG_log(4, "D310_write_orders: %s", csv_file_name);
	pasy.firmnr = parm_firma;
	EXEC SQL					/* only one default machine */
		select pasy.datenf into :pasy.datenf from pasy
		where   firmnr = :pasy.firmnr
		and werkli = 0
		and schlgr = 'LDW0'
		and schlsl = '';
	if (SQLCODE != 0) {
		DBG_err(1, "D310_write_orders: NO pasy (werkli=0,schlgr='LDW0',schlsl='')");
		return ERROR;
	}
	if (strlen(pasy.datenf) > 36) {
		strncpy (fpsl_1.einres, pasy.datenf + 36, 8);		/* extract machine name */
		fpsl_1.einres[8] = '\0';					/* 0-termin */
		DBG_log(4, "D310_write_orders: pasy machine: [%s]", fpsl_1.einres);
	} else {
		DBG_err(1, "D310_write_orders: NO pasy machine in pasy.datenf: [%s] (<36chars)", pasy.datenf);
		return ERROR;
	}

	EXEC SQL declare c_fpsl cursor with hold for
		select * into :fpsl_1 from fpsl
		where fpsl.einres = :fpsl_1.einres		/* machine */
		order by saufnr,saufpo;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare cursor c_fpsl");

	EXEC SQL open c_fpsl;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_fpsl");
	while (1) {				/* until SQL not found */
		EXEC SQL fetch c_fpsl;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_fpsl");
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(2, "D310_write_orders: end fpsl (einres: %s) total: %d", fpsl_1.einres, fpsl_cnt);
			break;
		}
		fpsl_cnt++;
		DBG_log(4, "D310_write_orders: fpsl (saufnr/po: %ld / %ld) [%s]", fpsl_1.saufnr, fpsl_1.saufpo, fpsl_1.teilnr);
		if (strcmp(fpsl_1.avtxsl, "2000") >= 0) {
			DBG_log(4, "D310_write_orders: SKIP fpsl_1.avtxsl >= '2000'");
			continue;
		}
		status_fpsl1 = D320_caojobs(&fpsl_1, 0);
		if (status_fpsl1 != 1) continue;		/* not new */
		DBG_log(2, "D310_write_orders: NEW fpsl (saufnr/po: %ld / %ld teilnr [%s] arbgnr:%ld ltgtnr [%s] "
			"ltgnum [%s] avtxsl [%s] dplcrp [%s])",
				fpsl_1.saufnr, fpsl_1.saufpo, fpsl_1.teilnr, fpsl_1.arbgnr, fpsl_1.ltgtnr,
				fpsl_1.ltgnum, fpsl_1.avtxsl, fpsl_1.dplcrp);

		kill_all_blanks(fpsl_1.dplcrp);		/* if only spaces, SQLlib returns " " */
		if (strlen(fpsl_1.dplcrp) > 0) {	/* double or twisted */
			cable_type = 'T';
			/* check if twisted or double.  D = apzd[ltgnum].s1meng||s2meng == 0.5 */
			memset(&apzd, 0, sizeof(APZD));
			apzd.firmnr = parm_firma;
			apzd.plwerk = parm_werk;
			strcpy(apzd.teilnr, fpsl_1.teilnr);
			apzd.arbgnr = fpsl_1.arbgnr;
			EXEC SQL
				select * into :apzd from apzd
				where       apzd.firmnr = :apzd.firmnr
				and apzd.plwerk = :apzd.plwerk
				and apzd.teilnr = :apzd.teilnr
				and apzd.arbgnr = :apzd.arbgnr
				and apzd.ferweg = 1 ;                /* CAO_FERWEG */
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "apzd");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(1, "D310_write_orders: ERROR no apzd teilnr [%s] arbgnr:%d", apzd.teilnr, apzd.arbgnr);
				continue;
			}
			DBG_log(2, "D310_write_orders: apzd teilnr [%s] arbgnr:%d s1/2meng: %f / %f)",
				apzd.teilnr, apzd.arbgnr, apzd.s1meng, apzd.s2meng);
			if ((apzd.s1meng == 0.5) || (apzd.s2meng == 0.5)) {
				cable_type = 'D';
			}
		} else {
			cable_type = 'S';
		}
		DBG_log(4, "D310_write_orders: fpsl (saufnr/po: %ld / %ld) cable_type: %c",
				fpsl_1.saufnr, fpsl_1.saufpo, cable_type);

		/* only if new fpsl from D320_caojobs, then create 2.nd */
		if ((cable_type == 'D') || (cable_type == 'T')) {

			/* in fact the lookup would be via fpsl_2.ltgnum = fpsl_1.dplcrp */
			memcpy(&fpsl_2, &fpsl_1, sizeof(FPSL));		/* copy whole 1.st fpsl */
			fpsl_2.saufpo = fpsl_1.saufpo + 1;			/* by convention its always (saufpo+1) */
			status_fpsl2 = D320_caojobs(&fpsl_2, 0);

			EXEC SQL
				select * into :fpsl_2 from fpsl
				where   fpsl.saufnr = :fpsl_2.saufnr
				and fpsl.saufpo > :fpsl_1.saufpo
				and fpsl.teilnr = :fpsl_1.teilnr
				and fpsl.ltgnum = :fpsl_1.dplcrp 
				limit 1;

			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpsl");
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(2, "D310_write_orders: INFO no fpsl_2 (saufnr/po: %ld / %ld) SKIP (saufnr/po: %ld / %ld)",
						fpsl_2.saufnr, fpsl_2.saufpo, fpsl_1.saufnr, fpsl_1.saufpo);
				snprintf(rec_order_data.ERPno,   MAXLEN_ORD_ERPNO, "%ld#%ld#0#0", fpsl_1.saufnr, fpsl_1.saufpo);
				snprintf(rec_order_data.Leadset, MAXLEN_ORD_LEADSET, "%s#%s#0", fpsl_1.teilnr, fpsl_1.ltgnum);

				rec_order_data.ProdVersion = 1;
				snprintf(rec_order_data.description, MAXLEN_ORD_DESCR, "Type '%c' prod. start:%ld - %ld", cable_type,
						fpsl_1.strdat, fpsl_1.enddat);
				rec_order_data.Quantity = fpsl_1.fermng - fpsl_1.ruemng;
				sprintf(rec_order_data.TargetDate, "%ld%s", fpsl_1.enddat, "235900"); /* target time fix or current time ? */
				strcpy(rec_order_data.TargetLoc, "");
				strcpy(rec_order_data.Scheduler, "");
				strcpy(rec_order_data.RevisionNo, "");
				strcpy(rec_order_data.Charge, "");
				strcpy(rec_order_data.Customer, "");
				strcpy(rec_order_data.Project,  "");
				rec_order_data.PushOrderNoFors = fpsl_1.fertnr;
				rec_order_data.KanbanCardNo1 = fpsl_1.kanban;
				rec_order_data.KanbanCardNo2 = 0;
				strcpy(rec_order_data.WarehouseDestination, "");

				D311_writeline_order(&rec_order_data, fdg_in, csv_file_name);
				//ord_cnt++;
				continue;
			}
		}
		/* now have fpsl_1 (& fpsl_2) insert into caojobs */
		status_fpsl1 = D320_caojobs(&fpsl_1, 1);
		if ((cable_type == 'D') || (cable_type == 'T')) {
			status_fpsl2 = D320_caojobs(&fpsl_2, 1);
		}

		ord_cnt++; 				/* new order  */
		/* get makt */
		memset(&makt, 0, sizeof(MAKTTYP));              /* clear all if not found */
		EXEC SQL
			select * into :makt from makt
			where makt.firmnr = :fpsl_1.firmnr
			and makt.werktl = :fpsl_1.plwerk
			and makt.teilnr = :fpsl_1.teilnr
			and makt.kanbnr = :fpsl_1.kanban
			and makt.ferweg = 1
			and makt.kbntyp = '3'
			and makt.arbgnr = :fpsl_1.arbgnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "makt");
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(1, "D310_write_orders: WARN no makt teilnr [%s] kanbnr: %ld arbgnr: %ld",
					fpsl_1.teilnr, fpsl_1.kanban, fpsl_1.arbgnr);
		}
		DBG_log(4, "D310_write_orders: makt [%s] nlagpl [%s] nlagor [%s]", fpsl_1.teilnr, makt.nlagpl, makt.nlagor);
		/* get magi */
		memset(&magi, 0, sizeof(MAGITYP));              /* clear all if not found */
		sql_tmpstr1[0] = '\0';				/* ensure 0-term */
		sql_tmpstr2[0] = '\0';				/* ensure 0-term */
		/* ATTN: only get 2 columns due to probably extension of magi */
		EXEC SQL
			select S_MAGI_INDI_CUST,S_MAGI_INDI_PROJ into :sql_tmpstr1,:sql_tmpstr2 from magi
			where    magi.firmnr = :fpsl_1.firmnr
			and  magi.teilnr = :fpsl_1.teilnr;			/* TODO ltgnr ? */
		kill_all_blanks(sql_tmpstr1);
		kill_all_blanks(sql_tmpstr2);
		strcpy(magi.MAGI_INDI_CUST, sql_tmpstr1);
		strcpy(magi.MAGI_INDI_PROJ, sql_tmpstr2);

		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi")
			if (SQLCODE == SQLNOTFOUND) {
				DBG_log(1, "D310_write_orders: WARN no magi teilnr=[%s]", fpsl_1.teilnr);
				/* ignore not exist magi, use empty strings */
			}
		DBG_log(4, "D310_write_orders: magi teilnr [%s] cust/proj (%s/%s) [%s]/[%s]",
				fpsl_1.teilnr,
				_STRDEF(MAGI_INDI_CUST), _STRDEF(MAGI_INDI_PROJ),
				magi.MAGI_INDI_CUST, magi.MAGI_INDI_PROJ);

		if (cable_type == 'S') {
			snprintf(rec_order_data.ERPno,   MAXLEN_ORD_ERPNO, "%ld#%ld#0#0", fpsl_1.saufnr, fpsl_1.saufpo);
			snprintf(rec_order_data.Leadset, MAXLEN_ORD_LEADSET, "%s#%s#0", fpsl_1.teilnr, fpsl_1.ltgnum);
			fpsl_switch = 0;
		} else {
			if (strcmp(fpsl_1.ltgnum, fpsl_2.ltgnum) < 0) {	/* correct order */
				fpsl_switch = 0;
			} else {
				fpsl_switch = 1;
			}
			snprintf(rec_order_data.ERPno,   MAXLEN_ORD_ERPNO, "%ld#%ld#%ld#%ld",
					fpsl_1.saufnr, (!fpsl_switch)? fpsl_1.saufpo : fpsl_2.saufpo,
					fpsl_1.saufnr, (!fpsl_switch)? fpsl_2.saufpo : fpsl_1.saufpo);
			snprintf(rec_order_data.Leadset, MAXLEN_ORD_LEADSET, "%s#%s#%s",
					fpsl_1.teilnr,
					(!fpsl_switch)? fpsl_1.ltgnum : fpsl_2.ltgnum,
					(!fpsl_switch)? fpsl_2.ltgnum : fpsl_1.ltgnum);
		}
		rec_order_data.ProdVersion = 1;
		snprintf(rec_order_data.description, MAXLEN_ORD_DESCR, "Type '%c' prod. start:%ld - %ld", cable_type,
				fpsl_1.strdat, fpsl_1.enddat);
		rec_order_data.Quantity = fpsl_1.fermng - fpsl_1.ruemng;
		sprintf(rec_order_data.TargetDate, "%ld%s", fpsl_1.enddat, "235900"); /* target time fix or current time ? */
		strcpy(rec_order_data.TargetLoc, makt.nlagpl);
		strcpy(rec_order_data.Scheduler, "");
		strcpy(rec_order_data.RevisionNo, "");
		strcpy(rec_order_data.Charge, "");
		strcpy(rec_order_data.Customer, magi.MAGI_INDI_CUST);
		strcpy(rec_order_data.Project,  magi.MAGI_INDI_PROJ);
		rec_order_data.PushOrderNoFors = fpsl_1.fertnr;
		rec_order_data.KanbanCardNo1 = (!fpsl_switch)? fpsl_1.kanban : fpsl_2.kanban;
		if (cable_type == 'S') {					/* single */
			rec_order_data.KanbanCardNo2 = 0;
		} else {
			rec_order_data.KanbanCardNo2 = (!fpsl_switch)? fpsl_2.kanban : fpsl_1.kanban;
		}
		/* strcpy(rec_order_data.WarehouseDestination, fpsl_1.lagort); */
		strcpy(rec_order_data.WarehouseDestination, makt.nlagor);

		D311_writeline_order(&rec_order_data, fdg_in, csv_file_name);
	}

	EXEC SQL close c_fpsl;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_fpsl");
	DBG_log(4, "D310_write_orders: new orders %d", ord_cnt);
	return ord_cnt;
}

/**********************************************************************
 *   Name            :  D311_writeline_order
 *   Funktion        :  Fill & write order CSV line
 *   Parameter       :
 *   Return Code     :   OK, ERROR
 **********************************************************************/
int  D311_writeline_order(rec_order_data_t *order_dataTp, int fdg, char *csv_file_name)
{
	int rc = OK;
	char buffer_line[MAX_CSVLINE_LEN+1];
	char pushorder[40];

	if (order_dataTp->PushOrderNoFors == 0) {
		strcpy(pushorder, "");
	} else {
		sprintf(pushorder, "%d", order_dataTp->PushOrderNoFors);
	}
	rc = snprintf(buffer_line, MAX_CSVLINE_LEN, "%s;%s;%ld;%s;%ld;%s;%s;%s;%s;%s;%s;%s;%s;%d;%d;%s\n",
			order_dataTp->ERPno, 		order_dataTp->Leadset,
			order_dataTp->ProdVersion, 		order_dataTp->description,
			order_dataTp->Quantity,
			order_dataTp->TargetDate, 		order_dataTp->TargetLoc,
			order_dataTp->Scheduler, 		order_dataTp->RevisionNo,
			order_dataTp->Charge, 		order_dataTp->Customer,
			order_dataTp->Project, 		pushorder,
			order_dataTp->KanbanCardNo1,	order_dataTp->KanbanCardNo2,
			order_dataTp->WarehouseDestination
		     );
	if (rc == MAX_CSVLINE_LEN) {
		DBG_err(1, "D311_writeline_order: order csv line too long");
		buffer_line[MAX_CSVLINE_LEN] = '\0';			/* ensure \n if line too long */
	}
	rc = G_write_line (fdg, buffer_line);
	return rc;
}

/**********************************************************************
 *   Name            :  D320_caojobs
 *   Funktion        :  check existing saufnr,saufpo in caojobs & fpms
 *                      if not exist: insert
 *   Parameter       :  fpslp : pointer to FPSL struct =global (DONT change!)
 *   			use fpsl_chk!
 *			do_insert: 0: check only,  1: insert into caojobs
 *   Return Code     : OK(0) job already on caojobs
 *                     1: inserted new job
 *                     ERROR
 **********************************************************************/
int D320_caojobs (FPSL *fpslp, int do_insert)
{
	DBG_log(4, "D320_caojobs: (fpsl.saufnr/po: %ld / %ld) do_insert: %d", fpslp->saufnr, fpslp->saufpo, do_insert);
	memcpy(&fpsl_chk, fpslp, sizeof(FPSL));             /* copy whole given fpsl (use only this !) */

	EXEC SQL
		select * into :fpsl_chk from fpsl
		where   fpsl.saufnr = :fpsl_chk.saufnr
		and fpsl.saufpo = :fpsl_chk.saufpo
		and not exists (
				select saufnr, saufpo from caojobs
				where caojobs.saufnr  = :fpsl_chk.saufnr
				and caojobs.saufpo  = :fpsl_chk.saufpo
			       )
		and not exists (
				select saufnr, saufpo from fpms
				where  fpms.saufnr = :fpsl_chk.saufnr
				and fpms.saufpo = :fpsl_chk.saufpo
			       ) ;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "check caojobs exist", "fpsl-caojobs");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_log(4, "D320_caojobs: already in caojobs & fpms (fpsl.saufnr/po: %ld / %ld)",
				fpslp->saufnr, fpslp->saufpo);
		return OK;		/* old order */
	}
	/* got fpsl_chk */
	if (SQLCODE == 0) {
		if (do_insert) {
			/* sql_tmplong = fpsl_chk.fermng - fpsl_chk.ruemng; */
			EXEC SQL
				insert into caojobs
				values (:fpsl_chk.firmnr, :fpsl_chk.saufnr, :fpsl_chk.saufpo, :fpsl_chk.einres, 1);
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "insert into", "caojobs");
			DBG_log(3, "D320_caojobs: added caojobs saufnr/po: %ld / %ld status: %ld",
					fpslp->saufnr, fpslp->saufpo, sql_tmplong);
		}
		return 1;		/* new order */
	}
	return ERROR;
}

/**********************************************************************
 *   Name            :  D320_caojobs_update
 *   Funktion        :  update state
 *   Parameter       :  fpslp : pointer to FPSL struct =global (DONT change!)
 *                   :  state: 1: order started, 2: partial answer, 0: final answer
 *   Return Code     : OK, 1: not exist, ERROR
 **********************************************************************/
int D900_caojobs_update (FPMS *fpmsp, const char *state)
{
	DBG_log(4, "D900_caojobs_update: (fpsl.saufnr/po: %ld / %ld) state: %s", fpmsp->saufnr, fpmsp->saufpo, state);

	caojobs.firmnr = fpmsp->firmnr;
	caojobs.saufnr = fpmsp->saufnr;
	caojobs.saufpo = fpmsp->saufpo;
	strcpy(sql_tmpstr, state);
	EXEC SQL
		update caojobs set state = :sql_tmpstr
		where caojobs.firmnr = :caojobs.firmnr
		and caojobs.saufnr = :caojobs.saufnr
		and caojobs.saufpo = :caojobs.saufpo;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caojobs update", "caojobs");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_log(4, "D900_caojobs_update: (fpsl.saufnr/po: %ld / %ld) NOT EXIST", fpmsp->saufnr, fpmsp->saufpo);
		return 1;
	}
	return OK;
}

/**********************************************************************
 *   Name            :  D320_caojobs
 *   Funktion        :  check existing saufnr,saufpo in caojobs & fpms
 *                      if not exist: insert
 *   Parameter       :  fpslp : pointer to FPSL struct =global (DONT change!)
 *   			use fpsl_chk!
 *			do_insert: 0: check only,  1: insert into caojobs
 *   Return Code     : OK(0) job already on caojobs
 *                     1: inserted new job
 *                     ERROR
 **********************************************************************/
int D900_caojobs_clean (void)
{
	int rc = OK;
	int delcnt = 0;

	DBG_log(4, "D900_caojobs_clean");
	set_fonatran("ORDF");
	memset(&caojobs, 0, sizeof(CAOJOBS));
	caojobs.firmnr = parm_firma;

	EXEC SQL declare c_caojobs cursor with hold for
		select * into :caojobs from caojobs
		where   caojobs.firmnr = :caojobs.firmnr
		and caojobs.state = '0'
		and not exists (
				select firmnr,saufnr,saufpo from fpsl
				where fpsl.firmnr = caojobs.firmnr
				and fpsl.saufnr = caojobs.saufnr
				and fpsl.saufpo = caojobs.saufpo)
		order by saufnr,saufpo;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare cursor c_caojobs");
	EXEC SQL open c_caojobs;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open cursor c_caojobs");

	while (1) {
		EXEC SQL fetch c_caojobs;
		SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fetch cursor c_caojobs");
		if (SQLCODE < 0 ) {	/* close cursor */
			rc = ERROR;
			break;
		}
		if (SQLCODE == SQLNOTFOUND) {
			break;
		}
		DBG_log(3, "D900_caojobs_clean: delete caojob saufnr/po: %ld/%ld", caojobs.saufnr, caojobs.saufpo);
		EXEC SQL delete from caojobs
			where caojobs.firmnr = :caojobs.firmnr
			and caojobs.saufnr = :caojobs.saufnr
			and caojobs.saufpo = :caojobs.saufpo;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch cursor c_caojobs");
		delcnt++;
	}
	EXEC SQL close c_caojobs;
	DBG_log(2, "D900_caojobs_clean: deleted %d jobs", delcnt);
	return rc;
}

/**********************************************************************
 *  ORDER feedback
 **********************************************************************/

/**********************************************************************
 *   Name            :  D400_feedback
 *   Funktion        :  read all order feedback files
 *   Parameter       :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int D400_feedback(void)
{ 
	int rc = OK, rd;
	int status;
	int fback_cnt = 0;
	dirent_list_t *direntp;
	char *fname;
	char * dirpath;

	if (strlen(g.tmparg) > 0 ) {	/* TEST if given with -t use this */
		dirpath = g.tmpdir;
	} else {
		dirpath = g.cao_out_dir;
	}
	set_fonatran("ORDF");
	DBG_log(4, "D400_feedback: opendir [%s] min. fileage: %d sec", dirpath, CAO_FDB_FILEAGE);
	/* read all files in CAO-out-dir
	 * !! ATTN !! must be timesorted, due to incomplete orders read first
	 *  min file modtime sec: skip just copying file
	 */
	direntp = dirent_open(dirpath, DIRENT_SORT_NAMEALPHA, CAO_ORDFDB_REGEX, CAO_FDB_FILEAGE);
	if (! direntp) {
		DBG_err(1, "D400_feedback: ERROR opendir [%s]", dirpath);
		return ERROR;
	}

	/* my own feedback file, written with all unprocessed lines (fpms still exists, after partial feedback)
	 * filename/age is like, that on next loop it is processed FIRST ! */
	sprintf(g.tmp_order_fdb, "%s/" CAO_ORDFDB_UNPROC, dirpath, get_current_time(7, NULL, NULL));
	g.tmp_order_fdb_cnt = 0;

	DBG_log(4, "D400_feedback: found files %d", direntp->file_cnt);
	while ( (fname = dirent_next(direntp)) ) {
		DBG_log(2, "D400_feedback: file [%s]", fname);
		status = D410_feedback_file(dirpath, fname);
		if (status == ERROR) {
			rc = ERROR;
			break;		/* break to free dirent mem at end */
		}
		fback_cnt++;
	}
	rd = dirent_close(direntp);
	DBG_log(2, "D400_feedback: processed files %d", fback_cnt);
	if (g.tmp_order_fdb_cnt) {
		DBG_log(2, "D400_feedback: unprocessed lines %d in [%s]", g.tmp_order_fdb_cnt, g.tmp_order_fdb);
	}

	return rc;
}

/**********************************************************************
 *   Name            : D410_feedback_file
 *   Funktion        : read one order feedback file
 *   Parameter       : dirpath / fname
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D410_feedback_file(const char *dirpath, const char * fname)
{
	int rc = OK, rl, rc_x = 0;
	int status;
	char fullname[MAX_FILENAME_LEN];	/* path/filename */
	char *filedata = NULL;		/* file loaded (0-term) */
	char *lineptr;			/* fileread pos (line start)*/
	int linecnt = 1, linelen = 0;	/* line cnt & len */
	char *endline;			/* ptr to end of line \n */
	rec_order_feedback_t  fdb_data;
	char linebuf[MAX_CSVLINE_LEN +1];	/* copy of whole line */
	int colcnt;				/* parsed column 0.. */
	char *tokptr, *colptr;		/* found sep-char, column start ptr */

	rl = snprintf(fullname, MAX_FILENAME_LEN, "%s/%s", dirpath, fname);
	if (rl == MAX_FILENAME_LEN) return ERROR;
	DBG_log(4, "D410_feedback_file: %s", fullname);
	filedata = (char *) load_file (fullname);
	if (filedata == NULL) {
		DBG_err(1, "D410_feedback_file: ERROR load [%s]", fullname);
		return ERROR;
	}
	lineptr = filedata;
	DBG_log(5, "D410_feedback_file: %p", lineptr);
	while ((endline = strchr(lineptr, '\n')) ) {
		linelen = (int)(endline - lineptr) + 1;		/* incl \r\n  orig line not \0 term ! */ 
		DBG_log(5, "D410_feedback_file: %p - %p len:%d", lineptr, endline, linelen);
		/**I-333691 : E-Kanban orders should be ignored, these start by ';' **/
		if (linelen <3 || lineptr[0] == '#' || lineptr[0] == ';') { 		/* skip empty(\r\n) & comment line (test edit) */
			//linecnt++;
			strncpy(linebuf, lineptr, linelen - 2);
			DBG_log(2, "D410_feedback_file: line '%s' will be skipped", linebuf);
			lineptr = endline + 1;
			continue;
		}
		strncpy(linebuf, lineptr, linelen);		/* copy line, it is modfied \0-terms, need orig line later */
		linebuf[linelen - 1] = '\0';					/* \n -> \0 */
		if (linebuf[linelen - 2] == '\r') linebuf[linelen - 2] = '\0';	/* cut dos file \r\n */
		colptr = linebuf;
		colcnt = 0;
		DBG_log(4, "D410_feedback_file: line (%d) [%s]", linecnt, linebuf);
		while ( (tokptr = strchr(colptr, ';'))) {

			*tokptr = '\0';
			DBG_log(5, "D410_feedback_file: col %d [%s]", colcnt, colptr);
			switch (colcnt++) {
				case 0:
					snprintf(fdb_data.ERPno, MAXLEN_ORDFB_ERPNO, "%s", colptr);
					rl = sscanf(colptr, "%d#%d#%d#%d", &fdb_data.saufnr1, &fdb_data.saufpo1,
							&fdb_data.saufnr2, &fdb_data.saufpo2);
					break;
				case 1:
					snprintf(fdb_data.Leadset, MAXLEN_ORDFB_ERPNO, "%s", colptr);
					break;
				case 2:
					fdb_data.ProdVersion = atol(colptr);
					break;
				case 3:
					fdb_data.GoodParts = atof(colptr);
					break;
				case 4:
					fdb_data.Wasteparts = atof(colptr);
					break;
				case 5:
					fdb_data.CAOnumber = atol(colptr);
					break;
				case 6:
					snprintf(fdb_data.FinalMessage, MAXLEN_ORDFB_FINALMSG, "%s", colptr);
					break;
				default:
					break;
			}
			colptr = tokptr + 1;
		}
		DBG_log(4, "D410_feedback_file: line %d got columns %d [%s(%d,%d,%d,%d)|%s|%ld|%lf|%lf|%ld|%s]", linecnt, colcnt,
				fdb_data.ERPno, fdb_data.saufnr1, fdb_data.saufpo1, fdb_data.saufnr2, fdb_data.saufpo2,
				fdb_data.Leadset, fdb_data.ProdVersion,
				fdb_data.GoodParts, fdb_data.Wasteparts, fdb_data.CAOnumber, fdb_data.FinalMessage);
		if (colcnt < 7) {
			DBG_err(1, "D410_feedback_file: ERROR line %d colcnt %d [%.*s]", linecnt, colcnt, linelen, lineptr);
			FONA_MSG(__LINE__, "ERROR ORDfeedb %s line %d", fname, linecnt);
			rc = ERROR;
			break;
		}
		if ((status = D411_feedback_line(&fdb_data)) != 1) {		/* not inserted or error, write line tmp */
			g.tmp_order_fdb_cnt++;
			rc_x  += G_write_fileappend(g.tmp_order_fdb,  lineptr, linelen);
		}
		linecnt++;
		lineptr = endline + 1;
	}
	free(filedata);
	/* delete / move file */
	if (g.keep_files || rc != OK) {
		char new_fullname[MAX_FILENAME_LEN];
		sprintf(new_fullname, "%s.%s",  fullname, (rc==OK)?"done":"error");
		rc_x += G_rename_file(fullname, new_fullname);
	} else {
		rc_x += G_delete_file(fullname, 0);
	}
	if (rc_x != OK) {	/* tmpfile write or del/move */
		rc = ERROR;
	}
	return rc;
}

/**********************************************************************
 *   Name            : D411_feedback_line
 *   Funktion        : process on feedback data line
 *   Parameter       : order_fdb_p: struct read feedback data from line
 *   Return Code     : 0: cannot insert in fpms (already in uniqe INDEX) do later
 *		       1: OK inserted
 *		       ERROR
 **********************************************************************/
int D411_feedback_line(rec_order_feedback_t *order_fdb_p)
{
	int rc = 0;		/* FPMS not inserted */
	int status, fpms_exist_1, fpms_exist_2 = 0;
	int is_final = 0;

	DBG_log(3, "D411_feedback_line: (%d,%d,%d,%d) good: %.2f final: [%s]",
			order_fdb_p->saufnr1, order_fdb_p->saufpo1, order_fdb_p->saufnr2, order_fdb_p->saufpo2,
			order_fdb_p->GoodParts, order_fdb_p->FinalMessage);

	if (order_fdb_p->FinalMessage[0] == 'X') is_final = 1;
	fpms_1.firmnr = 3;
	fpms_1.saufnr = order_fdb_p->saufnr1;
	fpms_1.saufpo = order_fdb_p->saufpo1;
	fpms_1.lfdpos = order_fdb_p->CAOnumber;
	fpms_1.rmmeng = order_fdb_p->GoodParts;
	fpms_1.ausmng = 0.0;
	sprintf(fpms_1.persnr, "%ld", order_fdb_p->CAOnumber);
	strcpy(fpms_1.erfben, "CAO");
	fpms_1.erfdat = atol(get_current_time(1, NULL, NULL));
	fpms_1.erfuhr = atol(get_current_time(8, NULL, NULL));

	if ((fpms_exist_1 = D412_feedback_check_fpms(&fpms_1)) == ERROR) {
		return ERROR;
	}

	if (order_fdb_p->saufpo2 > 0) {
		memcpy(&fpms_2, &fpms_1, sizeof(FPMS));
		fpms_2.saufnr = order_fdb_p->saufnr2;
		fpms_2.saufpo = order_fdb_p->saufpo2;
		if ((fpms_exist_2 = D412_feedback_check_fpms(&fpms_2)) == ERROR) {
			return ERROR;
		}
	}

	if (fpms_exist_1 || fpms_exist_2) {	/* at least on exists, write line out in tmp file, process next time*/
		rc = 0;
	} else {
		D900_caojobs_update(&fpms_1, (is_final)? "0" : "2");
		if ((status = D413_feedback_check_insert_fpms(&fpms_1)) == ERROR) {
			return ERROR;
		}
		if (order_fdb_p->saufpo2 > 0) {
			D900_caojobs_update(&fpms_2, (is_final)? "0" : "2");
			if ((status = D413_feedback_check_insert_fpms(&fpms_2)) == ERROR) {
				return ERROR;
			}
		}
		rc = 1;
	}
	DBG_log(3, "D411_feedback_line: (%d,%d,%d,%d) return [%d]",
			order_fdb_p->saufnr1, order_fdb_p->saufpo1, order_fdb_p->saufnr2, order_fdb_p->saufpo2, rc);

	return rc;
}

/**********************************************************************
 *   Name            : D412_feedback_check_fpms
 *   Funktion        : check if FPMS exists
 *   Parameter       : fpms_p: fpms struct ptr
 *   Return Code     : 0 : not exist 1: exist , ERROR
 **********************************************************************/
int D412_feedback_check_fpms(FPMS *fpms_p)
{
	int rc = 0;		/* not exist */

	DBG_log(5, "D412_feedback_check_fpms: saufnr/po %ld,%ld",
			fpms_p->saufnr, fpms_p->saufpo);
	memcpy(&fpms_chk, fpms_p, sizeof(FPMS));
	EXEC SQL select * into :fpms_chk from fpms
		where   firmnr = :fpms_chk.firmnr
		and saufnr = :fpms_chk.saufnr
		and saufpo = :fpms_chk.saufpo
		and lfdpos = :fpms_chk.lfdpos; //something has to be added here when Kurt Binder does the changes in fpms table
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpms_chk");
	if (SQLCODE == 0) {		/* already exists (unique index in DB) */
		rc = 1;
	}
	DBG_log(3, "D412_feedback_check_fpms: saufnr/po %ld,%ld exists: %d",
			fpms_p->saufnr, fpms_p->saufpo, rc);
	return rc;
}
/**********************************************************************
 *   Name            : D412_feedback_check_fpmr
 *   Funktion        : check if FPMS exists
 *   Parameter       : fpms_p: fpms struct ptr
 *   Return Code     : 0 : not exist 1: exist , ERROR
 **********************************************************************/
int D412_feedback_check_fpmr(FPMR *fpmr_p)
{
	int rc = 0;		/* not exist */

	DBG_log(5, "D412_feedback_check_fpms: saufnr/po %ld,%ld",
			fpmr_p->saufnr, fpmr_p->saufpo);
	memcpy(&fpmr_chk, fpmr_p, sizeof(FPMR));
	EXEC SQL select * into :fpmr_chk from fpmr
		where   firmnr = :fpmr_chk.firmnr
		and saufnr = :fpmr_chk.saufnr
		and saufpo = :fpmr_chk.saufpo
		and lfdpos = :fpmr_chk.lfdpos
		and fpmrh0 = :fpmr_chk.fpmrh0; //something has to be added here when Kurt Binder does the changes in fpms table
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpms_chk");
	if (SQLCODE == 0) {		/* already exists (unique index in DB) */
		fpmr_p->lfdpos=fpmr_p->lfdpos+1;
	
	}
	DBG_log(3, "D412_feedback_check_fpmr: saufnr/po %ld,%ld exists: %d",
			fpmr_p->saufnr, fpmr_p->saufpo, rc);
	return rc;
}

/**********************************************************************
 *   Name            : D412_feedback_check_insert_fpms
 *   Funktion        : insert FPMS
 *   Parameter       : fpms_p: fpms struct ptr
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D413_feedback_check_insert_fpms(FPMS *fpms_p)
{
	int rc = OK;

	DBG_log(3, "D412_feedback_check_insert_fpms: saufnr/po %ld,%ld",
			fpms_p->saufnr, fpms_p->saufpo);
	memcpy(&fpms_chk, fpms_p, sizeof(FPMS));
	EXEC SQL select * into :fpms_chk from fpms
		where   firmnr = :fpms_chk.firmnr
		and saufnr = :fpms_chk.saufnr
		and saufpo = :fpms_chk.saufpo
		and lfdpos = :fpms_chk.lfdpos; //something has to be added here when Kurt Binder does the changes in fpms table
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpms_chk");
	if (SQLCODE == 0) {		/* already exists error (unique index in DB) */
		DBG_err(2, "D412_feedback_check_insert_fpms: WARN saufnr/po %ld,%ld already exists",
				fpms_p->saufnr, fpms_p->saufpo);
		return ERROR;
	}
	EXEC SQL insert into fpms values (:fpms_chk);
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpms insert");

	return rc;
}

/**********************************************************************
 *  ORDER feedback
 **********************************************************************/

/**********************************************************************
 *   Name            :  D800_rawmat
 *   Funktion        :  read all order rawmat files
 *   Parameter       :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int D800_rawmat(void)
{
	int rc = OK, rd;
	int status;
	int fback_cnt = 0;
	dirent_list_t *direntp;
	char *fname;
	char * dirpath;

	if (strlen(g.tmparg) > 0 ) {	/* TEST if given with -t use this */
		dirpath = g.tmpdir;
	} else {
		dirpath = g.cao_out_dir;
	}
	set_fonatran("ORDF");
	DBG_log(4, "D800_rawmat: opendir [%s] min. fileage: %d sec", dirpath, CAO_FDB_FILEAGE);
	/* read all files in CAO-out-dir
	 * !! ATTN !! must be timesorted, due to incomplete orders read first
	 *  min file modtime sec: skip just copying file
	 */
	direntp = dirent_open(dirpath, DIRENT_SORT_NAMEALPHA, CAO_RAWMAT_REGEX, CAO_FDB_FILEAGE);
	if (! direntp) {
		DBG_err(1, "D800_rawmat: ERROR opendir [%s]", dirpath);
		return ERROR;
	}

	/* my own feedback file, written with all unprocessed lines (fpms still exists, after partial feedback)
	 * filename/age is like, that on next loop it is processed FIRST ! */
	sprintf(g.tmp_order_rawmat, "%s/" CAO_RAWMAT_UNPROC, dirpath, get_current_time(7, NULL, NULL));
	sprintf(g.tmp_order_rawmat_hu, "%s/" CAO_RAWMAT_HU_UNPROC, dirpath, get_current_time(7, NULL, NULL));
	g.tmp_order_rawmat_cnt = 0;
	g.tmp_order_rawmat_hu_cnt = 0;

	DBG_log(4, "D800_rawmat: found files %d", direntp->file_cnt);
	millesecond = 0;
	while ( (fname = dirent_next(direntp)) ) {
		DBG_log(2, "D800_rawmat: file [%s]", fname);
		status = D810_rawmat_file(dirpath, fname);
		if (status == ERROR) {
			rc = ERROR;
			break;		/* break to free dirent mem at end */
		}
		fback_cnt++;
	}
	rd = dirent_close(direntp);
	DBG_log(2, "D800_rawmat: processed files %d", fback_cnt);
	if (g.tmp_order_rawmat_cnt) {
		DBG_log(2, "D800_rawmat: unprocessed lines %d in [%s]", g.tmp_order_rawmat_cnt, g.tmp_order_rawmat);
	}
	if (g.tmp_order_rawmat_hu_cnt) {
		DBG_log(2, "D800_rawmat_hu: unprocessed lines %d in [%s]", g.tmp_order_rawmat_hu_cnt, g.tmp_order_rawmat);
	}

	return rc;
}

/**********************************************************************
 *   Name            : D810_rawmat_file
 *   Funktion        : read one order rawmat file
 *   Parameter       : dirpath / fname
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D810_rawmat_file(const char *dirpath, const char * fname)
{
	int rc = OK,rc_hu = OK, rl, rc_x = 0,rc_hu_x = 0;
	int status;
	char fullname[MAX_FILENAME_LEN];	/* path/filename */
	char *filedata = NULL;		/* file loaded (0-term) */
	char *lineptr;			/* fileread pos (line start)*/
	int linecnt = 1, linelen = 0;	/* line cnt & len */
	char *endline;			/* ptr to end of line \n */
	rec_order_rawmat_t  rawmat_data;
	rec_order_rawmat_hu_t rawmat_data_hu_t;
	char linebuf[MAX_CSVLINE_LEN +1];	/* copy of whole line */
	int colcnt,colcnt_hu;				/* parsed column 0.. */
	char *tokptr, *colptr, *hashptr;		/* found sep-char, column start ptr, '#' separator */
	char process;		/* set to 'Y' if line has been scraped before if not 'N' */
	char handUnit[17];

	rl = snprintf(fullname, MAX_FILENAME_LEN, "%s/%s", dirpath, fname);
	if (rl == MAX_FILENAME_LEN) return ERROR;
	DBG_log(4, "D810_rawmat_file: %s", fullname);
	filedata = (char *) load_file (fullname);
	double_crimp=0;
	char d_crimp[50];
	strcpy(d_crimp,"");
	if (filedata == NULL) {
		DBG_err(1, "D810_rawmat_file: ERROR load [%s]", fullname);
		return ERROR;
	}
	lineptr = filedata;
	DBG_log(5, "D810_rawmat_file: %p", lineptr);
	while ((endline = strchr(lineptr, '\n')) ) {
		linelen = (int)(endline - lineptr) + 1;		/* incl \r\n  orig line not \0 term ! */ 
		DBG_log(5, "D810_rawmat_file: %p - %p len:%d", lineptr, endline, linelen);
		if (linelen <3 ) { 		/* process lines start with ';' (E-Kanban) and skip the other */
			//linecnt++;
			strncpy(linebuf, lineptr, linelen - 2);
			DBG_log(2, "D810_rawmat_file: line '%s' will be skipped", linebuf);
			lineptr = endline + 1;
			continue;
		}
		else if(lineptr[0] != ';')
		{
			strncpy(linebuf, lineptr, linelen);		/* copy line, it is modfied \0-terms, need orig line later */
			linebuf[linelen - 1] = ';';					/* \n -> \0 */
			if (linebuf[linelen - 2] == '\r') linebuf[linelen - 2] = ';';	/* cut dos file \r\n */
			colptr = linebuf;
			colcnt = 0;
			DBG_log(4, "D810_rawmat_file: line (%d) [%s]", linecnt, linebuf);
			process = 'Y';
		
			while ( (tokptr = strchr(colptr, ';'))) 
			{

				*tokptr = '\0';
				DBG_log(5, "D810_rawmat_file: col %d [%s]", colcnt_hu, colptr);
				switch (colcnt++) 
				{
					case 0:
						snprintf(rawmat_data_hu_t.ERPno, MAXLEN_RAWMAT_ERPNO, "%s", colptr);
						rl = sscanf(rawmat_data_hu_t.ERPno, "%d#%d#%d#%d", &rawmat_data_hu_t.saufnr1, &rawmat_data_hu_t.saufpo1,
								&rawmat_data_hu_t.saufnr2, &rawmat_data_hu_t.saufpo2);
						break;
					case 1:
						snprintf(rawmat_data_hu_t.MatNbr, MAXLEN_RAWMAT_MATNBR, "%s", colptr);
						break;
					case 2:
						rawmat_data_hu_t.Quantity = atof(colptr);
						break;
					case 3:
						snprintf(rawmat_data_hu_t.Unit, MAXLEN_RAWMAT_HANDUNIT, "%s", colptr);
						if((rawmat_data_hu_t.saufpo2!=0)&&(strcmp(rawmat_data_hu_t.ERPno,d_crimp)))
						{
							strcpy(d_crimp,rawmat_data_hu_t.ERPno);
							double_crimp=0;
						}
						if((rawmat_data_hu_t.saufpo2==0)&&(double_crimp!=0))
							double_crimp=0;
						if((!strcmp(rawmat_data_hu_t.Unit,"M"))&&(rawmat_data_hu_t.saufpo2!=0)&&(double_crimp==0))
							double_crimp=1;
						else if(!strcmp(rawmat_data_hu_t.Unit,"M")&&(rawmat_data_hu_t.saufpo2!=0)&&(double_crimp==1))
							double_crimp=2;
						
						//strcpy (rawmat_data.HandUnit, rawmat_data.HandUnit +1);
						break;
					case 4:
						rawmat_data_hu_t.CAOnumber = atol(colptr);
						break;
					case 5: 
						rawmat_data_hu_t.Scrap = atol(colptr);
						if(rawmat_data_hu_t.Scrap==1){
							process = 'N';
							//DBG_log(3, "D810_rawmat_file: line %d  ;%s;%.3f;%s;%ld;%s;%s --> already scrapped", linecnt, rawmat_data_hu_t.MatNbr,
								//	rawmat_data_hu_t.Quantity, rawmat_data_hu_t.Unit, rawmat_data_hu_t.CAOnumber, colptr, tokptr + 1);
						}
						break;
					case 6:
						snprintf(handUnit, MAXLEN_RAWMAT_HANDUNIT, "%s", colptr+1);
						if(!strcmp(handUnit,""))
							rawmat_data_hu_t.HandUnit=0;
						else
							rawmat_data_hu_t.HandUnit=atol(handUnit);
						break;
					case 7:
						rawmat_data_hu_t.Workstep = atol(colptr);
						break;
					case 8:
						score = 'N';
						snprintf(rawmat_data_hu_t.Leadset, MAXLEN_RAWMAT_LEADSET, "%s", colptr);
						if((hashptr = strchr(colptr, '#')) != NULL){
							*hashptr = '\0';
							snprintf(rawmat_data_hu_t.teilnr, MAXLEN_TEILNR + 1, colptr);
							colptr = hashptr + 1;
							if((strcmp(colptr, "ZETA")))
							{
							  hashptr = strchr(colptr, '#'); 
							  *hashptr = '\0';
							  snprintf(rawmat_data_hu_t.matnum, MAXLEN_MATNUM1 + 1, colptr);
							}
							else 
								snprintf(rawmat_data_hu_t.matnum, MAXLEN_MATNUM1 + 1, colptr);
						}
						else if((hashptr = strchr(colptr, '-')) != NULL)
						{
							*hashptr = '\0';
							snprintf(rawmat_data_hu_t.teilnr, MAXLEN_TEILNR + 1, colptr);
							colptr = hashptr + 1;
							snprintf(rawmat_data_hu_t.matnum, MAXLEN_MATNUM1 + 1, colptr);
							score = 'Y';
						}
						else 
						{
							process = 'N';
						}

					//rl = sscanf(colptr, "%s#%s#0", &rawmat_data.teilnr, &rawmat_data.matnum );
						break;
					default:
						break;
				}
				if(process == 'N')
				{
					double_crimp=0;
					break;
				}
				colptr = tokptr + 1;
			}
			if(process == 'Y')
			{
			DBG_log(4, "D810_rawmat_file_HU: line %d got columns %d [%d|%d|%d|%d|%s|%s|%lf|%s|%ld|%ld|%ld|%ld|%s(%s|%s)]", linecnt, colcnt,
				rawmat_data_hu_t.saufnr1, rawmat_data_hu_t.saufpo1,	rawmat_data_hu_t.saufnr2, rawmat_data_hu_t.saufpo2, rawmat_data_hu_t.ERPno, rawmat_data_hu_t.MatNbr, rawmat_data_hu_t.Quantity, rawmat_data_hu_t.Unit, rawmat_data_hu_t.CAOnumber,
				rawmat_data_hu_t.Scrap, rawmat_data_hu_t.HandUnit,
				rawmat_data_hu_t.Workstep, rawmat_data_hu_t.Leadset, rawmat_data_hu_t.teilnr, rawmat_data_hu_t.matnum);
			if (colcnt < 8) 
			{
				DBG_err(1, "D810_rawmat_file: ERROR line %d colcnt %d [%.*s]", linecnt, colcnt, linelen, lineptr);
				FONA_MSG(__LINE__, "ERROR ORDfeedb %s line %d", fname, linecnt);
				rc_hu = ERROR;
				break;
			}

				if ((status = D811_rawmat_line_HU(&rawmat_data_hu_t)) != OK) 
				{		/* not inserted or error, write line tmp */
					g.tmp_order_rawmat_hu_cnt++;
					rc_hu_x  += G_write_fileappend(g.tmp_order_rawmat_hu,  lineptr, linelen);
				}
			}
			linecnt++;
			lineptr = endline + 1;
		
		}
		else 
		{
			strncpy(linebuf, lineptr, linelen);		/* copy line, it is modfied \0-terms, need orig line later */
			linebuf[linelen - 1] = ';';					/* \n -> \0 */
			if (linebuf[linelen - 2] == '\r') linebuf[linelen - 2] = ';';	/* cut dos file \r\n */
			colptr = linebuf;
			colcnt = 0;
			DBG_log(4, "D810_rawmat_file: line (%d) [%s]", linecnt, linebuf);
			process = 'Y';
			while ( (tokptr = strchr(colptr, ';'))) 
			{
				*tokptr = '\0';
				DBG_log(5, "D810_rawmat_file: col %d [%s]", colcnt, colptr);
				switch (colcnt++) 
				{
					case 0:
						snprintf(rawmat_data.ERPno, MAXLEN_RAWMAT_ERPNO, "%s", colptr);
						break;
					case 1:
						snprintf(rawmat_data.MatNbr, MAXLEN_RAWMAT_MATNBR, "%s", colptr);
						break;
					case 2:
						rawmat_data.Quantity = atof(colptr);
						break;
					case 3:
						snprintf(rawmat_data.Unit, MAXLEN_RAWMAT_UNIT, "%s", colptr);
						break;
					case 4:
						rawmat_data.CAOnumber = atol(colptr);
						break;
					case 5: 
						rawmat_data.Scrap = atol(colptr);
						if(rawmat_data.Scrap){
							process = 'N';
							DBG_log(3, "D810_rawmat_file: line %d  ;%s;%.3f;%s;%ld;%s;%s --> already scrapped", linecnt, rawmat_data.MatNbr,
									rawmat_data.Quantity, rawmat_data.Unit, rawmat_data.CAOnumber, colptr, tokptr + 1);
						}
						break;
					case 6:
						snprintf(handUnit, MAXLEN_RAWMAT_HANDUNIT, "%s", colptr+1);
						rawmat_data.HandUnit=atol(handUnit);
						break;
					case 7:
						rawmat_data.Workstep = atol(colptr);
						break;
					case 8:
						score = 'N';
						snprintf(rawmat_data.Leadset, MAXLEN_RAWMAT_LEADSET, "%s", colptr);
						if((hashptr = strchr(colptr, '#')) != NULL)
						{
							*hashptr = '\0';
							snprintf(rawmat_data.teilnr, MAXLEN_TEILNR + 1, colptr);
							colptr = hashptr + 1;
							if((strcmp(colptr, "ZETA")))
							{
							  hashptr = strchr(colptr, '#'); 
							  *hashptr = '\0';
							  snprintf(rawmat_data.matnum, MAXLEN_MATNUM1 + 1, colptr);
							}
							else 
								snprintf(rawmat_data.matnum, MAXLEN_MATNUM1 + 1, colptr);
						}
						else if((hashptr = strchr(colptr, '-')) != NULL)
						{
							*hashptr = '\0';
							snprintf(rawmat_data.teilnr, MAXLEN_TEILNR + 1, colptr);
							colptr = hashptr + 1;
							snprintf(rawmat_data.matnum, MAXLEN_MATNUM1 + 1, colptr);
							score = 'Y';
						}
						else 
						{
							process = 'N';
						}

				//rl = sscanf(colptr, "%s#%s#0", &rawmat_data.teilnr, &rawmat_data.matnum );
						break;
					default:
						break;
				}
				if(process == 'N')
				{
					break;
				}
				colptr = tokptr + 1;
			}
			if(process == 'Y')
			{
			DBG_log(4, "D810_rawmat_file: line %d got columns %d [%s|%s|%lf|%s|%ld|%ld|%s|%ld|%s(%s|%s)]", linecnt, colcnt,
				rawmat_data.ERPno, rawmat_data.MatNbr, rawmat_data.Quantity, rawmat_data.Unit, rawmat_data.CAOnumber,
				rawmat_data.Scrap, rawmat_data.HandUnit,
				rawmat_data.Workstep, rawmat_data.Leadset, rawmat_data.teilnr, rawmat_data.matnum);
			if (colcnt < 8) 
			{
				DBG_err(1, "D810_rawmat_file: ERROR line %d colcnt %d [%.*s]", linecnt, colcnt, linelen, lineptr);
				FONA_MSG(__LINE__, "ERROR ORDfeedb %s line %d", fname, linecnt);
				rc = ERROR;
				break;
			}
			if ((status = D811_rawmat_line(&rawmat_data)) != OK)
			{		/* not inserted or error, write line tmp */
				g.tmp_order_rawmat_cnt++;
				rc_x  += G_write_fileappend(g.tmp_order_rawmat,  lineptr, linelen);
			}
			}
			linecnt++;
			lineptr = endline + 1;
		}
	}
	free(filedata);
	/* delete / move file */
	if (g.keep_files || rc != OK) {
		char new_fullname[MAX_FILENAME_LEN];
		sprintf(new_fullname, "%s.%s",  fullname, (rc==OK)?"done":"error");
		rc_x += G_rename_file(fullname, new_fullname);
	} else {
		rc_x += G_delete_file(fullname, 0);
	}
	if (rc_x != OK) {	/* tmpfile write or del/move */
		rc = ERROR;
	}
	/****************/
		/* delete / move file */
	if (g.keep_files || rc_hu != OK) {
		char new_fullname[MAX_FILENAME_LEN];
		sprintf(new_fullname, "%s.%s",  fullname, (rc_hu==OK)?"done":"error");
		rc_x += G_rename_file(fullname, new_fullname);
	} else {
		rc_hu_x += G_delete_file(fullname, 0);
	}
	if (rc_hu_x != OK) {	/* tmpfile write or del/move */
		rc_hu = ERROR;
	}
	/**************/
	return rc;
}

/**********************************************************************
 *   Name            : D811_rawmat_line
 *   Funktion        : process on rawmat data line
 *   Parameter       : order_rawmat_p: struct read rawmat data from line
 *   Return Code     : 0: cannot insert in fpmc (already in uniqe INDEX) do later
 *		       1: OK inserted
 *		       ERROR
 **********************************************************************/
int D811_rawmat_line(rec_order_rawmat_t *order_rawmat_p)
{
	int rc = 0;		/* FPMC not inserted */
	char datetime [20];

	DBG_log(3, "D811_rawmat_line: (%s,%s) material number: [%s] quantity: %.2f unit: %s",
			order_rawmat_p->teilnr, order_rawmat_p->matnum, order_rawmat_p->MatNbr, 
			order_rawmat_p->Quantity, order_rawmat_p->Unit);

	sprintf(datetime,"%ld%ld%02d", get_dbdate (), get_dbtime(), millesecond);
	kill_all_blanks(datetime);
	fpmc.fpmch0 = atol(datetime);
	fpmc.firmnr = 3;
	fpmc.plwerk = parm_werk;
	sprintf(fpmc.komtnr, "%s", order_rawmat_p->MatNbr);
	fpmc.rmmeng = order_rawmat_p->Quantity;
	fpmc.caonum = order_rawmat_p->CAOnumber;
	sprintf(fpmc.teilnr, "%s", order_rawmat_p->teilnr);
	sprintf(fpmc.matnum, "%s", order_rawmat_p->matnum);
	fpmc.weuih0=order_rawmat_p->HandUnit;
	fpmc.arbgnr = 0;
	//searching for operation number.
	if ( score == 'Y' ) {
		EXEC SQL SELECT apag.arbgnr
			into :apag.arbgnr
			FROM apag
			JOIN apko
			ON apko.firmnr = :fpmc.firmnr
			AND apko.plwerk = :fpmc.plwerk
			AND apko.teilnr = apag.teilnr
			AND apko.arbgnr = apag.arbgnr
			JOIN mako
			ON mako.firmnr = :fpmc.firmnr
			AND mako.plwerk = :fpmc.plwerk
			AND mako.teilnr = apag.teilnr
			AND mako.stponr = apko.stponr
			AND mako.komtnr = :fpmc.komtnr
			WHERE apag.firmnr = :fpmc.firmnr
			AND   apag.plwerk = :fpmc.plwerk
			AND   apag.teilnr = :fpmc.teilnr
			AND   apag.zaufnr = :fpmc.matnum
			LIMIT 1; 
		SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P2 fpmc operation number");

		if ((SQLCODE == OK) || (SQLCODE == SQLNOTFOUND)) {
			if ( apag.arbgnr == 0){
				fpmc.arbgnr = atoi(fpmc.matnum);
			}
			else {
				fpmc.arbgnr = apag.arbgnr ;
			}
			DBG_log(3, "D811_rawmat_line: fpmc.arbgnr [%d]", fpmc.arbgnr);
		}
		sprintf(fpmc.matnum, " ");

	}


	if ((rc = D812_rawmat_insert_fpmc(&fpmc)) == ERROR) { 
		return ERROR;
	}
	DBG_log(3, "D811_rawmat_line: (%s,%s,%s) return [%d]",
			order_rawmat_p->teilnr, order_rawmat_p->matnum, order_rawmat_p->MatNbr, rc);

	millesecond++;
	millesecond %=100 ;
	if (!millesecond){
		sleep(1);
	}

	return rc;
}


/**********************************************************************
 *   Name            : D811_rawmat_line
 *   Funktion        : process on rawmat data line
 *   Parameter       : order_rawmat_p: struct read rawmat data from line
 *   Return Code     : 0: cannot insert in fpmc (already in uniqe INDEX) do later
 *		       1: OK inserted
 *		       ERROR
 **********************************************************************/
int D811_rawmat_line_HU(rec_order_rawmat_hu_t *order_rawmat_p)
{
	int rc = 0;		/* FPMC not inserted */
	char datetime [20];
	int status, fpmr_exist_1, fpmr_exist_2 = 0;
	int is_final = 0;
	long double time_ldfpos;
	DBG_log(3, "D811_rawmat_line: (%s) material number: [%s] quantity: %.2f unit: %s",
			order_rawmat_p->teilnr,  order_rawmat_p->MatNbr, 
			order_rawmat_p->Quantity, order_rawmat_p->Unit);
	time_ldfpos=get_dbtime();
	sprintf(datetime,"%ld%ld%02d", get_dbdate(), get_dbtime(), millesecond);
	kill_all_blanks(datetime);
	fpmr_1.fpmrh0 = atol(datetime);
	fpmr_1.firmnr = 3;
	fpmr_1.plwerk = parm_werk;
	sprintf(fpmr_1.komtnr, "%s", order_rawmat_p->MatNbr);
	fpmr_1.rmmeng = order_rawmat_p->Quantity;
	fpmr_1.caonum = order_rawmat_p->CAOnumber;
	fpmr_1.weuih0 = order_rawmat_p->HandUnit;
	sprintf(fpmr_1.teilnr, "%s", order_rawmat_p->teilnr);
	fpmr_1.saufnr = order_rawmat_p->saufnr1;
	if((double_crimp==2)&&(!strcmp(order_rawmat_p->Unit,"M")))
	{
		fpmr_1.saufpo = order_rawmat_p->saufpo2;
		double_crimp=0;
	}
	else
		fpmr_1.saufpo = order_rawmat_p->saufpo1;
	fpmr_1.lfdpos = order_rawmat_p->CAOnumber;
/*	sprintf(matnum, "%s", order_rawmat_p->matnum);
	fpmr.arbgnr = 0;
	//searching for operation number.
	if ( score == 'Y' ) {
		EXEC SQL SELECT apag.arbgnr
			into :apag.arbgnr
			FROM apag
			JOIN apko
			ON apko.firmnr = :fpmr.firmnr
			AND apko.plwerk = :fpmr.plwerk
			AND apko.teilnr = apag.teilnr
			AND apko.arbgnr = apag.arbgnr
			JOIN mako
			ON mako.firmnr = :fpmr.firmnr
			AND mako.plwerk = :fpmr.plwerk
			AND mako.teilnr = apag.teilnr
			AND mako.stponr = apko.stponr
			AND mako.komtnr = :fpmr.komtnr
			WHERE apag.firmnr = :fpmr.firmnr
			AND   apag.plwerk = :fpmr.plwerk
			AND   apag.teilnr = :fpmr.teilnr
			AND   apag.zaufnr = :matnum
			LIMIT 1; 
		SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P2 fpmc operation number");

		if ((SQLCODE == OK) || (SQLCODE == SQLNOTFOUND)) {
			if ( apag.arbgnr == 0){
				fpmc.arbgnr = atoi(fpmc.matnum);
			}
			else {
				fpmc.arbgnr = apag.arbgnr ;
			}
			DBG_log(3, "D811_rawmat_line: fpmc.arbgnr [%d]", fpmc.arbgnr);
		}
		sprintf(fpmc.matnum, " ");

	}
*/
	if ((fpmr_exist_1 = D412_feedback_check_fpmr(&fpmr_1)) == ERROR) {
		return ERROR;
	}

	
/*	if (order_rawmat_p->saufpo2 > 0) {
		memcpy(&fpmr_2, &fpmr_1, sizeof(FPMS));
		fpmr_2.saufnr = order_rawmat_p->saufnr2;
		fpmr_2.saufpo = order_rawmat_p->saufpo2;
		sprintf(fpmr_2.teilnr, "%s", order_rawmat_p->teilnr);
		if ((fpmr_exist_2 = D412_feedback_check_fpmr(&fpmr_2)) == ERROR) {
			return ERROR;
		}
	}
	*/
	if ((fpmr_exist_1 )||(fpmr_1.weuih0 ==0)) {	/* at least on exists, write line out in tmp file, process next time*/
		rc = 0;
	} else {
		if ((status = D812_rawmat_insert_fpmr(&fpmr_1)) == ERROR) {
			return ERROR;
		}
	/*	if (order_rawmat_p->saufpo2 > 0) {
			D900_caojobs_update(&fpmr_2, (is_final)? "0" : "2");
			if ((status = D812_rawmat_insert_fpmr(&fpmr_2)) == ERROR) {
				return ERROR;
			}
		}*/
		rc = status;
	}

	DBG_log(3, "D811_rawmat_HU_line: (%s, %s) return [%d]",
			order_rawmat_p->teilnr,  order_rawmat_p->MatNbr, rc);
	millesecond++;
	millesecond %=100 ;
	if (!millesecond){
		sleep(1);
	}

	return rc;
}


/**********************************************************************
 *   Name            : D812_rawmat_insert_fpmc
 *   Funktion        : insert FPMC
 *   Parameter       : fpmc_p: fpmc struct ptr
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D812_rawmat_insert_fpmc(FPMC *fpmc_p)
{
	int rc = OK;
	char buffer[500];
	if ( score == 'N' ) {
		DBG_log(3, "D812_rawmat_insert_fpmc: teilnr/matnum/material number %s,%s,%s",
				fpmc_p->teilnr, fpmc_p->matnum, fpmc_p->komtnr);
	}
	else {
		DBG_log(3, "D812_rawmat_insert_fpmc: teilnr/arbgnr/komtnr number %s,%d,%s",
				fpmc_p->teilnr, fpmc_p->arbgnr, fpmc_p->komtnr);
	}

	memset(&fpmc_chk,'\0', sizeof(FPMC));
	memcpy(&fpmc_chk, fpmc_p, sizeof(FPMC));
	sprintf(buffer,"D812_rawmat_insert_fpmc: fpmc values [%ld,%d,%d,%s,%f,%ld,%s,%s,%d]",
			fpmc_p->fpmch0, fpmc_p->firmnr, fpmc_p->plwerk,fpmc_p->komtnr, fpmc_p->rmmeng, fpmc_p->caonum,
			fpmc_p->teilnr, fpmc_p->matnum, fpmc_p->arbgnr);
	DBG_log(3, buffer);
	EXEC SQL insert into fpmc values (:fpmc_chk);
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpmc insert");
	if (SQLCODE != 0) {		// already exists error (unique index in DB) 
		DBG_err(2, "%s WARN not inserted", buffer);
		return ERROR;
	}

	return rc;
}
/**********************************************************************
 *   Name            : D812_rawmat_insert_fpmc
 *   Funktion        : insert FPMC
 *   Parameter       : fpmc_p: fpmc struct ptr
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D812_rawmat_insert_fpmr(FPMR *fpmr_p)
{
	int rc = OK;

	DBG_log(3, "D812_rawmat_insert_fpmr: saufnr/po %ld,%ld",
			fpmr_p->saufnr, fpmr_p->saufpo);
	memcpy(&fpmr_chk, fpmr_p, sizeof(FPMR));
	EXEC SQL select * into :fpmr_chk from fpmr
		where   firmnr = :fpmr_chk.firmnr
		and saufnr = :fpmr_chk.saufnr
		and saufpo = :fpmr_chk.saufpo
		and lfdpos = :fpmr_chk.lfdpos
		and fpmrh0 = :fpmr_chk.fpmrh0; //something has to be added here when Kurt Binder does the changes in fpms table
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpmr_chk");
	if (SQLCODE == 0) {		/* already exists error (unique index in DB) */
		DBG_err(2, "D412_feedback_check_insert_fpmr: WARN saufnr/po %ld,%ld already exists",
				fpmr_p->saufnr, fpmr_p->saufpo);
				
		return ERROR;
	}
	EXEC SQL insert into fpmr values (:fpmr_chk);
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fpms insert");

	return rc;
}
/**********************************************************************
 *  scrap feedback
 **********************************************************************/

/**********************************************************************
 *   Name            :  A500_scrap_init
 *   Funktion        :  init scrap requirements
 *   Parameter       :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int A500_scrap_init(void)
{
	int rc = OK, status;
	char cmd_buf[512];		/* execute cmd */

	/* set environment var for do-bi cmd SUFFIX */
	if ((status = putenv("FORS_BI_SUFFIX=cao")) != OK) {
		DBG_err(1, "A500_scrap_init ERROR putenv(FORS_BI_SUFFIX=cao) (errno: %d, %s)", errno, strerror(errno))
			return ERROR;
	}
	/* check if table exists, if not call do-bi --create */
	EXEC SQL select * from bi_lalf_cao where werknr=-1;
	if (SQLCODE < 0 ) {
		DBG_log(3, "A500_scrap_init test exist DB:bi_lalf_cao (SQL: %ld)", SQLCODE);

		sprintf (cmd_buf, "do-bi lalf --create 1>>%s 2>>%s", pbp.log_file, pbp.log_file);
		DBG_log(3, "A500_scrap_init exec [%s]", cmd_buf);
		if ((status = system(cmd_buf)) != 0) {
			DBG_err(1, "A500_scrap_init ERROR do_bi lalf --create return: %d)", status);
			/* ignore, does 'do-bi' return correct exit code ? */
		}
		DBG_log(2, "A500_scrap_init do_bi lalf --create return: %d)", status);
	} else {
		DBG_log(2, "A500_scrap_init bi_lalf_cao exists.");
	}
	/* get default warehouse */
	memset(&ffsso002p, 0, sizeof(FFSSO002P));
	ffsso002p.firmnr = parm_firma;
	ffsso002p.werknr = parm_werk;
	strcpy(ffsso002p.parkey, FFSSO002P_LALF_WAREHOUSE);
	EXEC SQL select * into :ffsso002p from ffsso002p
		where  firmnr = :ffsso002p.firmnr
		and werknr = :ffsso002p.werknr
		and parkey = :ffsso002p.parkey;
	if (SQLCODE < 0 || SQLCODE == SQLNOTFOUND) {
		strcpy(g.warehouse_default, LALF_DEFAULTWAREHOUSE);
		DBG_log(2, "A500_scrap_init using default warehouse [%s]", g.warehouse_default);
	} else {
		strcpy(g.warehouse_default, ffsso002p.parval);
		DBG_log(2, "A500_scrap_init using default warehouse FFSSO002P_LALF_WAREHOUSE [%s]", g.warehouse_default);
	}
	/* set scrap_recfunc = SCR or SRT(de) */
	/*if (!strcmp(env_sprach, "D")) {
	  strcpy(g.scrap_recfunc, "SRT");
	  } else {
	  strcpy(g.scrap_recfunc, "SCR");
	  }*/
	/*update 05.04.2017 hakh1013
	  g.scrap_recfunc is the 5th caofors parameter */
	DBG_log(2, "A500_scrap_init using scrap_recfunc [%s] (lang: %s)", g.scrap_recfunc, env_sprach);

	return rc;
}

/**********************************************************************
 *   Name            :  D500_scrapmat
 *   Funktion        :  read all scrap feedback files
 *   Parameter       :
 *   Return Code     :  OK, ERROR
 **********************************************************************/
int D500_scrapmat(void)
{
	int rc = OK, rd;
	int status;
	int fback_cnt = 0;
	dirent_list_t *direntp;
	char *fname;
	char * dirpath;

	if (strlen(g.tmparg) > 0 ) {	/* TEST if given with -t use this */
		dirpath = g.tmpdir;
	} else {
		dirpath = g.cao_out_dir;
	}
	set_fonatran("SCRP");
	/* D540_check_bilalf(0); */
	/* D540_check_bilalf(1); */
	D530_clean_bilalf(1, 0);
	DBG_log(4, "D500_scrapmat: opendir [%s] min. fileage: %d sec", dirpath, CAO_FDB_FILEAGE);
	/* read all files in CAO-out-dir (filename date/time sorted)
	 *  min file modtime sec: skip just copying file
	 */
	direntp = dirent_open(dirpath, DIRENT_SORT_NAMEALPHA, CAO_SCRAP_REGEX, CAO_FDB_FILEAGE);
	if (! direntp) {
		DBG_err(1, "D500_scrapmat: ERROR opendir [%s]", dirpath);
		return ERROR; 
	}

	DBG_log(4, "D500_scrapmat: found files %d", direntp->file_cnt);
	while ( (fname = dirent_next(direntp)) ) {
		DBG_log(2, "D500_scrapmat: file [%s]", fname);
		status = D510_scrapmat_file(dirpath, fname);
		if (status == ERROR) {
			rc = ERROR;
			break;		/* break to free dirent mem at end */
		}
		fback_cnt++;
		break;
	} 
	rd = dirent_close(direntp);
	DBG_log(2, "D500_scrapmat: processed files %d", fback_cnt);
	return rc;
}

/**********************************************************************
 *   Name            : D510_scrapmat_file
 *   Funktion        : read one scrap file
 *   Parameter       : dirpath / fname
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D510_scrapmat_file(const char *dirpath, const char * fname)
{
	int rc = OK, rl, rc_x = 0;
	int status;
	char fullname[MAX_FILENAME_LEN];	/* path/filename */
	char *filedata = NULL;		/* file loaded (0-term) */
	int filelen;			/* length of file */
	char *lineptr;			/* fileread pos (line start)*/
	int linecnt = 1, linelen = 0;	/* line cnt & len */
	char *endline;			/* ptr to end of line \n */
	rec_order_scrap_t  scrap_data;
	char linebuf[MAX_CSVLINE_LEN +1];	/* copy of whole line */
	int colcnt;				/* parsed column 0.. */
	char *tokptr, *colptr;		/* found sep-char ']', column start ptr */
	char *pos = 0;		/* found sep-char '#' */
	char process;
	char handUnit[17];
	char timestamp_errorscrap[15];  

	rl = snprintf(fullname, MAX_FILENAME_LEN, "%s/%s", dirpath, fname);
	if (rl == MAX_FILENAME_LEN) return ERROR;
	DBG_log(4, "D510_scrapmat_file: %s", fullname);
	filedata = (char *) load_file (fullname);
	if (filedata == NULL) {
		DBG_err(1, "D510_scrapmat_file: ERROR load [%s]", fullname);
		return ERROR;
	}
	filelen = strlen(filedata);
	lineptr = filedata + filelen - strlen(CAO_SCRAP_ELINE) -2 - 10; 	/* last line pos (-\r\n) and a little before */
	DBG_log(4, "D510_scrapmat_file: loaded len %d [%s]", filelen, fname);
	/* check correct file 1.line and last line (lines have dos format \r\n) */
	if (strncmp(filedata, CAO_SCRAP_FLINE, strlen(CAO_SCRAP_FLINE)) || !strstr(lineptr, CAO_SCRAP_ELINE) ) {
		DBG_log(2, "D510_scrapmat_file: SKIP incomplete file %s", fullname);
		DBG_log(3, "D510_scrapmat_file: 1.line [%.*s] fileend [%s]", (int)strlen(CAO_SCRAP_FLINE), filedata, lineptr);
		free(filedata);
		return rc;
	}
	lineptr = filedata;		/* 1. line */
	DBG_log(5, "D510_scrapmat_file: lineptr: %p", lineptr);
	while ((endline = strchr(lineptr, '\n')) ) {
		linelen = (int)(endline - lineptr) + 1;		/* incl \r\n  orig line not \0 term ! */
		DBG_log(5, "D510_scrapmat_file: %p - %p len:%d [%.*s]", lineptr, endline, linelen, linelen-2, lineptr);
		/* skip empty(\r\n) & comment line (test edit)  and 1. line */
		if (linelen <3 || lineptr[0] == '#' || !strncmp(lineptr, CAO_SCRAP_FLINE, strlen(CAO_SCRAP_FLINE))) {
			linecnt++;
			lineptr = endline + 1;		/* next line after \n */
			continue;
		}
		if (!strncmp(lineptr, CAO_SCRAP_ELINE, strlen(CAO_SCRAP_ELINE))) {	/* EOF line */
			break;
		}
		strncpy(linebuf, lineptr, linelen);		/* copy line, it is modfied \0-terms, need orig line later */
		linebuf[linelen - 1] = '\0';					/* \n -> \0 */
		if (linebuf[linelen - 2] == '\r') linebuf[linelen - 2] = '\0';	/* cut dos file \r\n */
		colptr = linebuf;
		colcnt = 0;
		DBG_log(4, "D510_scrapmat_file: line (%d) [%s]", linecnt, linebuf);
		process = 'J';
		if(g.scrap==10)
		{
			while ( (tokptr = strchr(colptr, ']'))) {
			*tokptr = '\0';
			DBG_log(5, "D510_scrapmat_file: col %d [%s]", colcnt, colptr);
			switch (colcnt++) {
				case 0:
					snprintf(scrap_data.teilnr, MAXLEN_SCRAP_TEILNR, "%s", colptr);
					break;
				case 1:
					scrap_data.quantity = atof(colptr);
					break;
				case 2:
					snprintf(scrap_data.reason, MAXLEN_SCRAP_REASON + 1, "%s", colptr);
					break;
				case 3:
					snprintf(scrap_data.kstnst, MAXLEN_SCRAP_COSTCENT, "%s", colptr);
					break;
				case 4:
					snprintf(scrap_data.user, MAXLEN_SCRAP_USER, "%s", colptr);
					break;
				case 5:
					scrap_data.beldat = atol(colptr);
					break;
				case 6:
					scrap_data.beltim = atol(colptr);
					break;
				case 7:
					if (scrap_data.beltim){
						if((pos = strchr(colptr,'-')) != NULL){
							sprintf(scrap_data.leadset, "%s", colptr);
						}
						else {
							//snprintf(scrap_data.leadset, 11, "%s",colptr);
							snprintf(scrap_data.leadset, MAXLEN_LEADSET, "%s",colptr);
						}
					}
					else{
						scrap_data.saufnr = atol(colptr);
					}		    
					break;
				case 8:
					if (scrap_data.beltim){
						if((pos = strchr(colptr,'#')) != NULL){
							*pos = '\0' ;	//strncpy(buf, colptr, pos - colptr);
						}
						snprintf(scrap_data.matnum, MAXLEN_MATNUM + 1, "%s", colptr);
					}
					else{
						scrap_data.saufpo = atol(colptr);
					}
					//scrap_data.saufpo = atol(colptr);		/* truncates #xxx */
					break;
				case 9:
					snprintf(scrap_data.creat_time, MAXLEN_SCRAP_CTIME, "%s", colptr);
					break;
				default:
					break;
			}
			if(process == 'N') break;
			colptr = tokptr + 1;
			}
		}
		else
		while ( (tokptr = strchr(colptr, ']'))) {
			*tokptr = '\0';
			DBG_log(5, "D510_scrapmat_file: col %d [%s]", colcnt, colptr);
			switch (colcnt++) {
				case 0:
					snprintf(scrap_data.teilnr, MAXLEN_SCRAP_TEILNR, "%s", colptr);
					break;
				case 1:
					snprintf(scrap_data.handUnit, MAXLEN_HU+1, "%s", colptr+2);
					
					break;					
					
				case 2:
					scrap_data.quantity = atof(colptr);
					break;
				case 3:
					snprintf(scrap_data.reason, MAXLEN_SCRAP_REASON + 1, "%s", colptr);
					break;
				case 4:
					snprintf(scrap_data.kstnst, MAXLEN_SCRAP_COSTCENT, "%s", colptr);
					break;
				case 5:
					snprintf(scrap_data.user, MAXLEN_SCRAP_USER, "%s", colptr);
					break;
				case 6:
					scrap_data.beldat = atol(colptr);
					break;
				case 7:
					scrap_data.beltim = atol(colptr);
					break;
				case 8:
					if (scrap_data.beltim){
						if((pos = strchr(colptr,'-')) != NULL){
							sprintf(scrap_data.leadset, "%s", colptr);
						}
						else {
							//snprintf(scrap_data.leadset, 11, "%s",colptr);
							snprintf(scrap_data.leadset, MAXLEN_LEADSET, "%s",colptr);
						}
					}
					else{
						scrap_data.saufnr = atol(colptr);
					}		    
					break;
				case 9:
					if (scrap_data.beltim){

						//if((pos = strchr(colptr,'#')) != NULL)
						if ((pos = strchr(colptr, '#')) != NULL ) 
						{

							*pos = '\0' ;	//strncpy(buf, colptr, pos - colptr);
/*LD04{*/
							typp1= 1;
/*LD04}*/
						}
/*LD08{*/						
						else if ( strcmp(colptr, "ZETA") == 0 || strcmp(colptr, "Zeta") == 0 || strcmp(colptr, "zeta") == 0) 
						{
							typp1= 1; 
						}
/*LD08}*/
/*LD05{*/						
						else {
							typp1= 0;
						}
/*LD05}*/						
						snprintf(scrap_data.matnum, MAXLEN_MATNUM + 1, "%s", colptr);
					}
					else{
						scrap_data.saufpo = atol(colptr);
					}
					//scrap_data.saufpo = atol(colptr);		/* truncates #xxx */
					break;
				case 10:
					snprintf(scrap_data.creat_time, MAXLEN_SCRAP_CTIME, "%s", colptr);
					break;
				default:
					break;
			}
			if(process == 'N') break;
			colptr = tokptr + 1;
		}
		if(process == 'N') break;
		DBG_log(4, "D510_scrapmat_file: line %d got columns %d [%s|%lf|%s|%s|%s|%ld|%ld|%ld|%ld|%s]", linecnt, colcnt,
				scrap_data.teilnr, scrap_data.quantity, scrap_data.reason, scrap_data.kstnst, scrap_data.user,
				scrap_data.beldat, scrap_data.beltim, scrap_data.saufnr, scrap_data.saufpo, scrap_data.creat_time);
		if (((g.scrap==11)&& (colcnt < 11))||((g.scrap==10)&& (colcnt < 10))) {
			DBG_err(1, "D510_scrapmat_file: ERROR line %d colcnt %d [%.*s]", linecnt, colcnt, linelen, lineptr);
			rc = ERROR;
			break;
		}
		if ((status = D511_scrapmat_line(&scrap_data)) != OK) {		/* not inserted */
			rc = ERROR;
			break;
		}
		/* call do-bi lalf (set $FORS_BI_SUFFIX=cao) and delete bi_lalf_cao */
		if (rc == OK) {
			char cmd_buf[512];
			int bicnt_cao, bicnt_err;
			/* execute do-bi lalf */
			//sprintf (cmd_buf, "do-bi lalf --import 1>>%s 2>>%s", pbp.log_file, pbp.log_file);
			sprintf (cmd_buf, "do-bi lalf --import");
			if (!g.opt_noexec_do_bi_scrap) {
				DBG_log(3, "D510_scrapmat_file exec [%s]", cmd_buf);
				if ((status = system(cmd_buf)) != OK) {
					DBG_err(1, "D510_scrapmat_file: ERROR do_bi lalf --import return: %d)", status);
					/* ignore, does 'do-bi' return correct exit code ? */
				}
				DBG_log(2, "D510_scrapmat_file: do_bi lalf --import return: %d)", status);
				/* check import bi_lalf_cao and bierror empty */
				bicnt_cao = D540_check_bilalf(0);
				bicnt_err = D540_check_bilalf(1);
				if (bicnt_cao || bicnt_err) {
					DBG_err(1, "D510_scrapmat_file: ERROR after import: bi_lalf_cao: %d bierror: %d)", bicnt_cao, bicnt_err);
					FONA_MSG(__LINE__, "ERROR scrap(do-bi) %s", fname);
					char new_fullname[MAX_FILENAME_LEN];
					sprintf(new_fullname, "%s.error",  fullname);
					rc_x  += G_write_fileappend(new_fullname,  lineptr, linelen);
					status = ERROR;			/* if any error rename file .error */
				}
				strncpy (timestamp_errorscrap, fullname + 59, 14);
				//insert into scrap errors before being deleted from the table bierror
				D550_insert_errorscrap(scrap_data,timestamp_errorscrap);
				
				D530_clean_bilalf(1, 0);		/* clean bi_lalf_cao, but do-bi does it already */
				D530_clean_bilalf(0, 1);
			} else {
				DBG_log(3, "D510_scrapmat_file TEST NOEXEC [%s]", cmd_buf);
				status = OK;		/* can view results in bi_lalf_cao */
			}
		}
		linecnt++;
		lineptr = endline + 1;
	}
	free(filedata);
	rc_x += G_delete_file(fullname, 0);
	if (rc_x != OK) {	/* error del/move */
		rc = ERROR;
	}
	return rc;
}

/**********************************************************************
 *   Name            : D511_scrapmat_line
 *   Funktion        : process one scrap data line
 *   Parameter       : scrapmat_p: struct read scrap from line
 *   Return Code     : OK, ERROR
 **********************************************************************/
int D511_scrapmat_line(rec_order_scrap_t *scrapmat_p)
{
	int rc = OK;
	//char * lagort;

	memset(&bi_lalf, 0, sizeof(BI_LALF));		/* all not set cols to 0 */

	bi_lalf.werknr = parm_werk;
	snprintf(bi_lalf.lagort, 7, "%s", D520_get_warehouse(scrapmat_p));
	strcpy(bi_lalf.lagpla, " ");
	strcpy(bi_lalf.bstart, "0");
	strcpy(bi_lalf.elekzz, "0");
	bi_lalf.werktl = parm_werk;
	snprintf(bi_lalf.teilnr, 23, "%s", scrapmat_p->teilnr);
	strcpy(bi_lalf.fremde, " ");
	bi_lalf.abgnrr = 0;

	bi_lalf.verdat = 0;
	bi_lalf.zugdat = 0;
	bi_lalf.mengee = scrapmat_p->quantity;		/* mm -> m ?? */
	strcpy(bi_lalf.belgkz, " ");
	if(scrapmat_p->beltim){
		snprintf(bi_lalf.belegn, 18, "%s %ld", scrapmat_p->reason, scrapmat_p->beltim);
		DBG_log(3, "D511_scrapmat_line: (reason matnum: %s %ld)", scrapmat_p->reason, scrapmat_p->beltim);
	}
	else{
		DBG_log(3, "D511_scrapmat_line: (%s saufnr/po: %ld/%ld)", scrapmat_p->teilnr, scrapmat_p->saufnr, scrapmat_p->saufpo);
		snprintf(bi_lalf.belegn, 18, "%s %ld/%ld", scrapmat_p->reason, scrapmat_p->saufnr, scrapmat_p->saufpo);
	}
	bi_lalf.beldat = scrapmat_p->beldat;
	snprintf(bi_lalf.kstnst, 7, "%s", scrapmat_p->kstnst);
	if(g.scrap==10)
			strcpy(bi_lalf.charge, " ");
	else
		snprintf(bi_lalf.charge,MAXLEN_HU+1,scrapmat_p->handUnit); 
	strcpy(bi_lalf.liefkz, " ");
	bi_lalf.werkli = parm_werk;
	strcpy(bi_lalf.liefnr, " ");
	strcpy(bi_lalf.kosttr, " ");
	bi_lalf.anzbel = 0;
	strcpy(bi_lalf.tranid, "LALF");
	strcpy(bi_lalf.funktn1, g.scrap_recfunc);
	strcpy(bi_lalf.funktn2, " ");
	strcpy(bi_lalf.funktn3, " ");
	strcpy(bi_lalf.funktn4, " ");
	strcpy(bi_lalf.funktn5, " ");

	EXEC SQL insert into bi_lalf_cao values (:bi_lalf);
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bi_lalf_cao insert");

	return rc;
}

/**********************************************************************
 *   Name            : D520_get_warehouse
 *   Funktion        : get warehouse for saufnr/po
 *   Parameter       : scrapmat_p: struct read scrap from line
 *   Return 	     : warehouse name
 **********************************************************************/
char * D520_get_warehouse(rec_order_scrap_t *scrapmat_p)
{
	static char warehouse[80];		/* buffer is returned */
	char *hashptr;

	strcpy(warehouse, g.warehouse_default);		/* if not found */
	matl.firmnr = parm_firma;
	matl.werknr = parm_werk;
	strcpy(matl.tltype, "0");
	strcpy(matl.teilnr,scrapmat_p->teilnr);
	strcpy(matl.teilnr, "");
	
	//Scrap booking for Module "MO"  and for Quality "Qu", the warehouse should be taken from MATL because scrap must be booked from prod (table matl ) warehouse not cutting warehouse (table ffsso002p) (SR01 CUTTING ROOM WAREHOUSE)
	if(!strcmp(scrapmat_p->reason,"Qu") || !strcmp(scrapmat_p->reason,"MO") ){
		//if (scrapmat_p->beltim == 0)
		//{
		//	/* get fpsl saufnr/po */
		//	memset(&fpsl_chk, 0, sizeof(FPSL));
		//	fpsl_chk.firmnr = parm_firma;
		//	fpsl_chk.saufnr = scrapmat_p->saufnr;
		//	fpsl_chk.saufpo = scrapmat_p->saufpo;
		//	EXEC SQL select * 
		//	    into :fpsl_chk 
		//		from fpsl
		//		where   firmnr = :fpsl_chk.firmnr
		//		and saufnr = :fpsl_chk.saufnr
		//		and saufpo = :fpsl_chk.saufpo;
		//	SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpsl warehouse");
		//	if (SQLCODE == 0) {
		//		DBG_log(3, "D520_get_warehouse: got fpsl.lagort [%s]", fpsl_chk.lagort);
		//		kill_all_blanks(fpsl_chk.teilnr);
		//		if (strlen(fpsl_chk.teilnr)){
		//			strcpy(matl.teilnr, fpsl_chk.teilnr);
		//			goto out1;
		//		}				    
		//	}
		//	/* fpld saufnr/po */
		//	memset(&fpld, 0, sizeof(FPLD));
		//	fpld.firmnr = parm_firma;
		//	fpld.saufnr = scrapmat_p->saufnr;
		//	fpld.saufpo = scrapmat_p->saufpo;
		//	EXEC SQL select * 
		//	    into :fpld 
		//		from fpld
		//		where   firmnr = :fpld.firmnr
		//		and saufnr = :fpld.saufnr
		//		and saufpo = :fpld.saufpo
		//		limit 1;
		//	SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpld warehouse");
		//	kill_all_blanks(fpld.teilnr);
		//	if (strlen(fpld.teilnr))
		//		strcpy(matl.teilnr, fpld.teilnr);
		//	if (SQLCODE != 0) {
		//		DBG_log(3, "D520_get_warehouse: NO fpld");
		//		goto out;
		//	}
		//}
		//else 
			strcpy(matl.teilnr , scrapmat_p->leadset);
			
			/* matl.teilnr */
//out1:   	
			//if (strlen(fpsl_chk.teilnr) || strlen(fpld.teilnr) || strlen(matl.teilnr)){
/*LD04{*/
			if (typp1 == 1)
			{
/*LD04}*/
				EXEC SQL
				select lagorf 
				into :matl.lagorf
				from  matl 
				where  firmnr = :matl.firmnr
				and    werknr = :matl.werknr
				and    tltype = :matl.tltype
				and    teilnr = :matl.teilnr;
				SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "matl warehouse");
				if (SQLCODE == 0) {
					DBG_log(3, "D520_get_warehouse: got matl.teilnr [%s] lagorf [%s]", matl.teilnr, matl.lagorf);
					strcpy(warehouse, matl.lagorf);
				}
			//}
/*LD04{*/
			}
			else 
			{
				if((hashptr = strchr(scrapmat_p->leadset, '-')) != NULL){
					*hashptr = '\0';
					snprintf(matl.teilnr, MAXLEN_TEILNR + 1, scrapmat_p->leadset);
				}
				else 
				{
				strcpy(matl.teilnr , scrapmat_p->leadset);	
				}
				
				kill_all_blanks(matl.teilnr);
				
				EXEC SQL
				select lagorf 
				into :matl.lagorf
				from  matl 
				where  firmnr = :matl.firmnr
				and    werknr = :matl.werknr
				and    tltype = :matl.tltype
				and    teilnr = :matl.teilnr;
				SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "matl warehouse");
				if (SQLCODE == 0) {
					DBG_log(3, "D520_get_warehouse: got matl.teilnr [%s] lagorf [%s]", matl.teilnr, matl.lagorf);
					strcpy(warehouse, matl.lagorf);
					
				}
			}
/*LD04}*/
			//else {
				//DBG_log(3, "D520_get_warehouse: saufnr [%d] saufpo [%d] does not exist neither in FPSL nor in FPLD", fpld.saufnr, fpld.saufpo);
			//}
		}
		else
		{
				if (scrapmat_p->beltim){
					memset(&apag, 0, sizeof(APAG));
					apag.firmnr = parm_firma;
					apag.plwerk = parm_werk;
					kill_all_blanks(scrapmat_p->matnum);
					/*field 10 != 0*/
					if(strlen(scrapmat_p->matnum)){
/*LD04*/			if (typp1 == 1) {						
						
						strcpy(apag.teilnr, scrapmat_p->leadset);
						strcpy(mako.matnum, scrapmat_p->matnum);

/*LD07{*/
/*						EXEC SQL select * into :apag 
							FROM apag g
							WHERE firmnr = :apag.firmnr
							AND   plwerk = :apag.plwerk
							AND   teilnr = :apag.teilnr
							AND   arbgnr = (SELECT arbgnr
									FROM apzd d
									WHERE firmnr = :apag.firmnr
									AND   plwerk = :apag.plwerk
									AND   d.teilnr = g.teilnr
									AND   ltponr = (SELECT stponr
										FROM mako o
										WHERE firmnr = :apag.firmnr
										AND   plwerk = :apag.plwerk
										AND   o.teilnr = g.teilnr
										AND   matnum = :mako.matnum
										and (eindat <= :real_today or eindat = 0)
										and (ausdat >= :real_today or ausdat = 0)))
							AND   ferweg = 1;
						SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P1 apag warehouse");
						if (SQLCODE == 0) {
							DBG_log(3, "D520_get_warehouse: got apag.teilnr [%s] lagort [%s]", apag.teilnr, apag.lagort);
							strcpy(warehouse, apag.lagort);
						}*/
						
						EXEC SQL select * into :apag  
							FROM apag g
							inner join apzd d
							on
							d.firmnr = g.firmnr AND
							d.plwerk = g.plwerk AND
							d.teilnr = g.teilnr AND
							d.arbgnr = g.arbgnr
							inner join mako o 
							on
							o.stponr = d.ltponr AND
							o.firmnr = d.firmnr AND
							o.plwerk = d.plwerk AND
							o.teilnr = d.teilnr
							WHERE g.firmnr = :apag.firmnr
							AND   g.plwerk = :apag.plwerk
							AND   g.teilnr = :apag.teilnr
							AND   o.matnum = :mako.matnum
							AND  (o.eindat <= :real_today or o.eindat = 0)
							AND  (o.ausdat >= :real_today or o.ausdat = 0)
							AND   g.ferweg = 1;
						SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P1 apag warehouse");
						if (SQLCODE == 0) {
							DBG_log(3, "D520_get_warehouse: got apag.teilnr [%s] lagort [%s]", apag.teilnr, apag.lagort);
							strcpy(warehouse, apag.lagort);
						}
/*LD07}*/
					} 
					
/*LD04{*/			
					else
					{ 
						strcpy(apag.teilnr, scrapmat_p->leadset);
						apag.arbgnr = atol(scrapmat_p->matnum);

							EXEC SQL select * into :apag 
								FROM apag g
								WHERE firmnr = :apag.firmnr
								AND   plwerk = :apag.plwerk
								AND   teilnr = :apag.teilnr
								AND   arbgnr = :apag.arbgnr
								AND   ferweg = 1;
							SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P2 apag warehouse");
							if (SQLCODE == 0) {
								DBG_log(3, "D520_get_warehouse: got apag.teilnr [%s] lagort [%s]", apag.teilnr, apag.lagort);
								strcpy(warehouse, apag.lagort);							
					}
/*LD04}*/
					}	}				
					/*field 10 == 0  ""OK""*/
					else {
						if((hashptr = strchr(scrapmat_p->leadset, '-')) != NULL){
							*hashptr = '\0';
							snprintf(apag.teilnr, MAXLEN_TEILNR + 1, scrapmat_p->leadset);
							//scrapmat_p->leadset = hashptr + 1;
							apag.arbgnr = atol(hashptr + 1);

							EXEC SQL select * into :apag 
								FROM apag g
								WHERE firmnr = :apag.firmnr
								AND   plwerk = :apag.plwerk
								AND   teilnr = :apag.teilnr
								AND   arbgnr = :apag.arbgnr
								AND   ferweg = 1;
							SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "CAO-P2 apag warehouse");
							if (SQLCODE == 0) {
								DBG_log(3, "D520_get_warehouse: got apag.teilnr [%s] lagort [%s]", apag.teilnr, apag.lagort);
								strcpy(warehouse, apag.lagort);
							}
						}
					}
				}
				else{
					if (!strcmp(warehouse, "APAG") || !strcmp(warehouse, "FPSL") == 0) {  /* if FFSSO002P_LALF_WAREHOUSE */
						/* get fpsl saufnr/po */
						memset(&fpsl_chk, 0, sizeof(FPSL));
						fpsl_chk.firmnr = parm_firma;
						fpsl_chk.saufnr = scrapmat_p->saufnr;
						fpsl_chk.saufpo = scrapmat_p->saufpo;
						EXEC SQL select * into :fpsl_chk from fpsl
							where   firmnr = :fpsl_chk.firmnr
							and saufnr = :fpsl_chk.saufnr
							and saufpo = :fpsl_chk.saufpo;
						SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpsl warehouse");
						if (SQLCODE == 0) {
							DBG_log(3, "D520_get_warehouse: got fpsl.lagort [%s]", fpsl_chk.lagort);
							strcpy(warehouse, fpsl_chk.lagort);
							goto out;
						}
						/* fpld saufnr/po */
						memset(&fpld, 0, sizeof(FPLD));
						fpld.firmnr = parm_firma;
						fpld.saufnr = scrapmat_p->saufnr;
						fpld.saufpo = scrapmat_p->saufpo;
						EXEC SQL select * into :fpld from fpld
							where   firmnr = :fpld.firmnr
							and saufnr = :fpld.saufnr
							and saufpo = :fpld.saufpo
							limit 1;
						SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpld warehouse");
						if (SQLCODE != 0) {
							DBG_log(3, "D520_get_warehouse: NO fpld");
							//goto out;
							/* fpsc saufnr/po */
							memset(&fpsc, 0, sizeof(FPSC));
							fpsc.firmnr = parm_firma;
							fpsc.saufnr = scrapmat_p->saufnr;
							fpsc.saufpo = scrapmat_p->saufpo;
							EXEC SQL select * into :fpsc from fpsc
								where   firmnr = :fpsc.firmnr
								and saufnr = :fpsc.saufnr
								and saufpo = :fpsc.saufpo
								limit 1;
							SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fpsc warehouse");
							if (SQLCODE != 0) {
								DBG_log(3, "D520_get_warehouse: NO fpsc");
								goto out;
							}
						}
						/* apag.teilnr */
						memset(&apag, 0, sizeof(APAG));
						apag.firmnr = parm_firma;
						apag.plwerk = parm_werk;
						EXEC SQL select * into :apag from apag
							where   firmnr = :apag.firmnr
							and plwerk = :apag.plwerk
							and teilnr = :fpld.teilnr
							and arbgnr = :fpld.arbgnr
							and ferweg = 1;
						SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "apag warehouse");
						if (SQLCODE == 0) {
							DBG_log(3, "D520_get_warehouse: got apag.teilnr [%s] lagort [%s]", apag.teilnr, apag.lagort);
							strcpy(warehouse, apag.lagort);
						}
					}
				}
			}
out:
	return warehouse;
}


/**********************************************************************
 *   Name            : D530_clean_bilalf
 *   Funktion        : delete tables
 *   Parameter       : del_bi_lalf: 1 del bi_lalf_cao   del_bierror: 1=delete
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D530_clean_bilalf(int del_bi_lalf, int del_bierror)
{
	int rc = OK;

	DBG_log(4, "D530_clean_bilalf: del_bi_lalf:%d del_bierror: %d", del_bi_lalf, del_bierror);

	if (del_bi_lalf) {
		EXEC SQL delete from bi_lalf_cao;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bi_lalf_cao delete");
	}
	if (del_bierror) {
		EXEC SQL delete from bierror
			where tran = 'lalf' and suffix = 'cao';
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bierror delete");
	}
	return rc;
}

/**********************************************************************
 *   Name            : D540_check_bilalf
 *   Funktion        : check count of  tables
 *   Parameter       : table: 0: return bi_lalf_count, 1: return bierror count
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D540_check_bilalf(int check_table)
{

	sql_tmpint = 0;

	DBG_log(5, "D540_check_bilalf: check table: %d", check_table);

	if (check_table == 0) {
		EXEC SQL select count(werknr) into :sql_tmpint from bi_lalf_cao;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bi_lalf_cao count");
		DBG_log(3, "D540_check_bilalf: count bi_lalf_cao: %d", sql_tmpint);

	} else if (check_table == 1) {
		EXEC SQL select count(tran) into :sql_tmpint from bierror where suffix='cao';
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bierror count");
		DBG_log(3, "D540_check_bilalf: count bierror (suffix=cao): %d", sql_tmpint);
	}
	return sql_tmpint;
}
/**********************************************************************
 *   Name            : D550_insert_errorscrap
 *   Funktion        : insert errorscrap
 *   Parameter       : scrap_data and  errfld,errtxt from bierror
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D550_insert_errorscrap(rec_order_scrap_t  scrap_data, char  * timestamp_errorscrap)
{
    int rc = OK;
	char buffer[600];
	char terrorscrap[15];
		
	sql_tmpint = 0;
	EXEC SQL select count(tran) into :sql_tmpint from bierror where suffix='cao' and tran = 'lalf';
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bierror count");
	DBG_log(3, "D550_insert_errorscrap: count bierror (suffix=cao): %d", sql_tmpint);
	
	// Only one error allowed 
    if ( sql_tmpint == 1)
	{
	    EXEC SQL select errfld,errtxt into :sql_errfld,:sql_errtxt from bierror where suffix='cao' and tran = 'lalf';	
	    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "bierror errfld");
			
	    errorscrap.firmnr=3;
	    errorscrap.werknr=atol(scrap_data.user);
	    strncpy(errorscrap.teilnr,scrap_data.teilnr,sizeof(errorscrap.teilnr));
		errorscrap.quantity= (float) scrap_data.quantity;
		snprintf(terrorscrap, sizeof(terrorscrap), "%s", timestamp_errorscrap);
	    errorscrap.beldat=atol(terrorscrap);
	    
	    if (scrap_data.beltim)
		    snprintf(errorscrap.caonum, sizeof(errorscrap.caonum), "%d", scrap_data.beltim);
	    else 
	        snprintf(errorscrap.caonum, sizeof(errorscrap.caonum), "%d-%d", scrap_data.saufnr,scrap_data.saufpo);
         
	    strncpy(errorscrap.kstnst,scrap_data.kstnst,sizeof(errorscrap.kstnst));	
	    strcpy(errorscrap.userid, "VSR1");
	    strncpy(errorscrap.errfld,sql_errfld,sizeof(errorscrap.errfld));
	    strncpy(errorscrap.errtxt,sql_errtxt,sizeof(errorscrap.errtxt));
            
	    sprintf(buffer,"D550_insert_errorscrap: errorscrap values [%d,%d,%s,%lf,%ld,%s,%s,%s,%s:%s]",
	        errorscrap.firmnr, errorscrap.werknr, errorscrap.teilnr, errorscrap.quantity, errorscrap.beldat, errorscrap.caonum, errorscrap.kstnst, errorscrap.userid, errorscrap.errfld, errorscrap.errtxt);
	    DBG_log(3, buffer);
	                        
	    EXEC SQL 
	    insert into errorscrap  values (:errorscrap);
	    SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "errorscrap insert");
	    if (SQLCODE != 0) 
	    {
	        // already exists error (unique index in DB)
	        DBG_err(2, "%s WARN not inserted", buffer);
	        return ERROR;
	    }        	
	}   
	
	return rc;
}
/**********************************************************************
 *  Handling unit
 **********************************************************************/
/**********************************************************************
 *   Name            : D200_handling_unit
 *   Funktion        : generate handling_units.csv file on the ftp server from FORS
 *   Parameter       :
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D200_handling_unit(void)
{
	int rc = OK, status;
	char handlingUnit_fname[MAX_FILENAME_LEN];
	int hunit_fd;
	int weui_cnt = 0;
	int cnt_err = 0, cnt_errnomagd = 0, cnt_nofbwparam = 0, cnt_nofpekoda2 = 0;	/* err counters */
	char buffer_line [MAX_CSVLINE_LEN+1];
	DBG_log(1, "D200_handling_unit");
	set_fonatran("HU");
	
	sprintf(handlingUnit_fname, "%s/handling_units%s.csv", g.tmpdir, g.timestamp_start);
	hunit_fd = open_output (handlingUnit_fname);
	if (hunit_fd == - 1) 
	{
		DBG_log(1, "ERROR: open Handling Unit file  [%s] (errno: %d, %s)", handlingUnit_fname, errno, strerror(errno));
		return ERROR;
	}
	fbwstort.firmnr = parm_firma;
	fbwstort.werknr = parm_werk;
	strcpy(fbwstort.arbgeb, "CAO_HU");
		
	EXEC SQL
		select * into :fbwstort from fbwstort
		where      fbwstort.firmnr = :fbwstort.firmnr
		AND        fbwstort.werknr = :fbwstort.werknr
		AND        fbwstort.arbgeb = :fbwstort.arbgeb;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "SELECT from fbwstort");
	if (SQLCODE == SQLNOTFOUND) 
	{
		DBG_err(1, "caofors: no fbwstort F/W (%d/%d) arbgeb [%s] found",
			fbwstort.firmnr, fbwstort.werknr, fbwstort.arbgeb);
			return ERROR;
	}
	DBG_log(1, "D200_handling_unit: CAO current (last run) [fbwstort.lexdat: %ld]", fbwstort.lexdat);
	weui.firmnr = parm_firma;
	weui.liwerk = parm_werk;
	
	EXEC SQL declare c_weui cursor with hold for
	select * into :weui from weui
	where   weui.firmnr = :weui.firmnr
	and 	weui.liwerk = :weui.liwerk
	and 	((weui.aendat>= :fbwstort.aendat) or (weui.erfdat>= :fbwstort.aendat))
	order by charge, teilnr;                        /* to have always same order in CSV */
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_weui");
	EXEC SQL open c_weui;
	//SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_weui");
	DBG_log(1,"open cur");
	while (1) 
	{
		DBG_log(1,"Execute while");
		EXEC SQL fetch c_weui;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_weui");
		if (SQLCODE == SQLNOTFOUND) {
			break;
		}
		weui_cnt++;
		/* each 1000 entries print progress */
		if ((weui_cnt % 1000) == 0) DBG_log(1, "D200_handling_unit: ===== weui processed count %d", weui_cnt);
		DBG_log(3, "D200_handling_unit: ===== weui (%d) charge [%s] teilnr [%s] liefnr [%s]",
				weui_cnt, weui.charge, weui.teilnr,  weui.liefnr);
		if ((status = D210_handling_unit_exp(hunit_fd, handlingUnit_fname, &weui)) != OK ) {
				DBG_log(1, "D200_handling_unit: WARN weui [%s] charge: %s", weui.teilnr, weui.charge);
			}
			/* continue skip error part */
		
	}
	DBG_log(1, "D200_handling_unit: weui total: %d (ERRORs: %d, no MAGD: %d WARNs: no fbwparam[DEFTOL]: %d, no fpekoda2v2: %d)", weui_cnt, cnt_err, cnt_errnomagd, cnt_nofbwparam, cnt_nofpekoda2);

	EXEC SQL close c_weui;
	
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_weui");


	sprintf(buffer_line,"99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999;99999999");

	G_write_line(hunit_fd, buffer_line);
	
		/* close file and copy */
	close_output(hunit_fd, handlingUnit_fname);
	if ((status = G_copy_file(handlingUnit_fname, g.cao_in_srv, g.cao_in_dir)) != OK ) {
		return ERROR;
	}
	if ( G_delete_file (handlingUnit_fname, 0) != OK) {
		return ERROR;
	}
	return rc;
	
}
/**********************************************************************
 *  CRIMP
 **********************************************************************/
/**********************************************************************
 *   Name            : D600_crimp_exp
 *   Funktion        : crimp export CSV to CAO
 *   Parameter       :
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D600_crimp_exp(void)
{
	int rc = OK, status;
	int fpeko_cnt = 0;
	char crimp_fname[MAX_FILENAME_LEN];
	int crimp_fd;
	int cnt_err = 0, cnt_errnomagd = 0, cnt_nofbwparam = 0, cnt_nofpekoda2 = 0;	/* err counters */

	DBG_log(1, "D600_crimp_exp");
	set_fonatran("CRPE");

	sprintf(crimp_fname, "%s/crimpstd%s.csv", g.tmpdir, g.timestamp_start);
	crimp_fd = open_output (crimp_fname);
	if (crimp_fd == - 1) {
		DBG_log(1, "ERROR: open CRIMPSTD [%s] (errno: %d, %s)", crimp_fname, errno, strerror(errno));
		return ERROR;
	}
	fpekodatv2.firmnr = parm_firma;
	fpekodatv2.werknr = parm_werk;

	EXEC SQL declare c_fpekodatv2 cursor with hold for
		select * into :fpekodatv2 from fpekodatv2
		where   fpekodatv2.firmnr = :fpekodatv2.firmnr
		and fpekodatv2.werknr = :fpekodatv2.werknr
		order by teilnr,leit01,kundnr;                        /* to have always same order in CSV */
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare c_fpekodatv2");
	EXEC SQL open c_fpekodatv2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_fpekodatv2");
	while (1) {
		EXEC SQL fetch c_fpekodatv2;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_fpekodatv2");
		if (SQLCODE == SQLNOTFOUND) {
			break;
		}
		fpeko_cnt++;
		/* each 1000 entries print progress */
		if ((fpeko_cnt % 1000) == 0) DBG_log(1, "D600_crimp_exp: ===== fpekodatv2 processed count %d", fpeko_cnt);
		DBG_log(3, "D600_crimp_exp: ===== fpekodatv2 (%d) teilnr [%s] leit01 [%s] kundnr [%s]",
				fpeko_cnt, fpekodatv2.teilnr, fpekodatv2.leit01, fpekodatv2.kundnr);
		if ((status = D610_crimp_exp_part(crimp_fd, crimp_fname, &fpekodatv2)) != OK ) {
			if (status & D610_CRIMP_EXP_ERROR)			cnt_err++;
			if (status & D610_CRIMP_EXP_ERROR_NOMAGD)		cnt_errnomagd++;
			if (status & D610_CRIMP_EXP_WARN_NOFBWPARAM)	cnt_nofbwparam++;
			if (status & D610_CRIMP_EXP_WARN_NOFPEKODA2V2)	cnt_nofpekoda2++;
			if (status & D610_CRIMP_EXP_ERROR_MASK) {
				DBG_err(1, "D600_crimp_exp: ERROR fpekodatv2 [%s] status: %04x", fpekodatv2.teilnr, status);
			} else if (status & D610_CRIMP_EXP_WARN_MASK) {
				DBG_log(1, "D600_crimp_exp: WARN fpekodatv2 [%s] status: %04x", fpekodatv2.teilnr, status);
			}
			/* continue skip error part */
		}
	}
	DBG_log(1, "D600_crimp_exp: fpekodatv2 total: %d (ERRORs: %d, no MAGD: %d WARNs: no fbwparam[DEFTOL]: %d, no fpekoda2v2: %d)", fpeko_cnt, cnt_err, cnt_errnomagd, cnt_nofbwparam, cnt_nofpekoda2);

	EXEC SQL close c_fpekodatv2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_fpekodatv2");

	if (cnt_err || cnt_errnomagd) {
		FONA_MSG(__LINE__, "ERROR crimp-export");
	} else if (cnt_nofbwparam || cnt_nofpekoda2) {
		FONA_MSG(__LINE__, "WARN crimp-export");
	}

	/* close file and copy */
	close_output(crimp_fd, crimp_fname);
	if ((status = G_copy_file(crimp_fname, g.cao_in_srv, g.cao_in_dir)) != OK ) {
		return ERROR;
	}
	if ( G_delete_file (crimp_fname, 0) != OK) {
		return ERROR;
	}
	return rc;
}
/**********************************************************************
 *   Name            : split_leit
 *   Funktion        : split leitung string using -
 *   Parameter       : del_bi_lalf: 1 del bi_lalf_cao   del_bierror: 1=delete
 *   Return 	     : OK, ERROR
 **********************************************************************/
void split_leit(char *leit, char *leit1, char *leit2 )
{
	char *ptr;
   ptr = strchr(leit,'-');
   if(ptr!=NULL)
   {
	 *ptr = '\0';
	 strcpy(leit1,leit);
	 strcpy(leit,ptr + 1);
	 ptr = strchr(leit,'-');
	 if(ptr!=NULL)
	 {
		*ptr = '\0';
		strcpy(leit2,leit);
	 }
	 else
		strcpy(leit2,leit);
	}
	else
	{ 
		strcpy(leit1,leit);
		strcpy(leit2,"");
	}
}
/**********************************************************************
 *   Name            : D210_handling_unit_exp
 *   Funktion        : export Handling Unit file
 *   Parameter       :
 *   Return 	     : OK, or bitmask see #define D610_CRIMP_EXP_*
 **********************************************************************/
 int D210_handling_unit_exp(int fd, const char * fname, WEUITYP *weui_p)
{
	int rc = OK;
	char *ptr;
	record_handling_unit_exp_t handling_unit_data;

	DBG_log(5, "D210_handling_unit_exp charge [%s] teilnr [%s] liefnr [%s] ",
			weui_p->charge, weui_p->teilnr, weui_p->liefnr );
	kill_all_blanks(weui_p->teilnr);
	kill_all_blanks(weui_p->charge);
	snprintf(handling_unit_data.plant_id, MAXLEN_PLANT_ID,"%d",weui_p->liwerk);
	snprintf(handling_unit_data.material,MAXLEN_MATERIAL,"%s",weui_p->teilnr);
	snprintf(handling_unit_data.handling_unit,18,"H%lf",weui_p->weuih0);	
	//need to check charge
	//snprintf(handling_unit_data.charge,MAXLEN_CHARGE,"%s",weui_p->charge);	
	strcpy(handling_unit_data.charge,"0");
	handling_unit_data.quantity=weui_p->fulmng;
	handling_unit_data.sender=0;
		/* get magd */
	memset(&magd_hu, 0, sizeof(MAGDTYP));
	magd_hu.firmnr       = weui_p->firmnr;
	strcpy(magd_hu.teilnr, weui_p->teilnr);
	EXEC SQL
		select * into :magd_hu from magd
		where   firmnr = :magd_hu.firmnr
		and teilnr = :magd_hu.teilnr;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd_hu");
	if (SQLCODE == OK) 
	{
		if((strcmp(magd_hu.matken, "LTG")==0)||(strcmp(magd_hu.matken, "SLTG")==0))
		{
			handling_unit_data.unit=2;
			snprintf(handling_unit_data.bme,MAXLEN_BME,"M");
		}
		else
		{
			handling_unit_data.unit=0;
			snprintf(handling_unit_data.bme,MAXLEN_BME,"Psc");			
		}
		
	}
	else
	{
		DBG_err(1, "D210_handling_unit_exp: ERROR no magd teilnr [%s]", magd_hu.teilnr);
		handling_unit_data.unit=99;
		snprintf(handling_unit_data.bme,MAXLEN_BME,"Err");
	}
	
	/* get labs */
	memset(&labs_hu, 0, sizeof(LABSTYP));
	labs_hu.firmnr       = weui_p->firmnr;
	strcpy(labs_hu.charge, weui_p->charge);
	EXEC SQL
		select * into :labs_hu from labs
		where   firmnr = :labs_hu.firmnr
		and charge 	   = :labs_hu.charge limit 1;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "labs_hu");
	if (SQLCODE == OK) 
	{
		snprintf(handling_unit_data.sloc,MAXLEN_SLOC,labs_hu.lagort);		
		snprintf(handling_unit_data.snr ,MAXLEN_SNR ,labs_hu.lagpla);	
	}
	else
	{
		DBG_err(1, "D210_handling_unit_exp: ERROR no labs charge [%s]", labs_hu.charge);
		snprintf(handling_unit_data.sloc,MAXLEN_SLOC,"N/A");		
		snprintf(handling_unit_data.snr ,MAXLEN_SNR ,"N/A");		
	}
	rc |= D220_handling_unit_exp_line(fd, fname, &handling_unit_data);
	
	return rc;

}
/**********************************************************************
 *   Name            : D600_crimp_exp_part
 *   Funktion        : export one part
 *   Parameter       :
 *   Return 	     : OK, or bitmask see #define D610_CRIMP_EXP_*
 **********************************************************************/
int D610_crimp_exp_part(int fd, const char * fname, FPEKODATV2TYP *fpekodatv2_p)
{
	int rc = OK;
	int written = 0;
	int counter = 0;
	record_crimp_exp_t crimp_dat;
	int fpekoda2v2_found = 0;			/* search best matching kundnr: 0=not_found 1=SUPP  2=real_custom */
	int fbwparam_found = 0;			/* search best matching kundnr: 0=not_found 1=SUPP  2=real_custom */
	FPEKODA2V2 fpekoda2v2_data;			/* found DB row */
	FBWPARAM   fbwparam_data;			/* found DB row */
	struct fbwparam_schlsl_s {
		char leityp[5+1];			/* fbwparam[DEFTOL].schlsl format 5 + 5 + x */
		double leityp_f;			/* as float val */
		char awgtyp[5+1];
		char kundnr[20+1];
	} fbwparam_schlsl;
	char quer_1[5];
	char quer_2[5];
	
	char quer_3[5];//quer_1, used to save the first part of leit01, quer_2, the second part of leit02
	float quer_1_number=0;
	int quer_2_number=0;
	float val1=0;
	char leit[20];
	char *ptr;
	DBG_log(5, "D600_crimp_exp_line teilnr [%s] kundnr [%s] leit01 [%s] quer01: %.2lf",
			fpekodatv2_p->teilnr, fpekodatv2_p->kundnr, fpekodatv2_p->leit01, fpekodatv2_p->quer01);
	kill_all_blanks(fpekodatv2_p->kundnr);

	/* get magd */
	memset(&magd_cmp, 0, sizeof(MAGDTYP));
	magd_cmp.firmnr       = fpekodatv2_p->firmnr;
	strcpy(magd_cmp.teilnr, fpekodatv2_p->teilnr);
	EXEC SQL
		select * into :magd_cmp from magd
		where   firmnr = :magd_cmp.firmnr
		and teilnr = :magd_cmp.teilnr;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magd_cmp");
	if (SQLCODE == SQLNOTFOUND) {
		DBG_err(1, "D610_crimp_exp_part: ERROR no magd teilnr [%s]", magd_cmp.teilnr);
		return (D610_CRIMP_EXP_ERROR_NOMAGD);
	}
	/* get fpekoda2v2  for sealkey */
	memset(&fpekoda2v2, 0, sizeof(FPEKODA2V2));
	memset(&fpekoda2v2_data, 0, sizeof(FPEKODA2V2));
	fpekoda2v2.firmnr = fpekodatv2_p->firmnr;
	fpekoda2v2.werknr = fpekodatv2_p->werknr;
	strcpy(fpekoda2v2.konttn, fpekodatv2_p->teilnr);
	strcpy(quer_3,"");
	if(strcmp(fpekodatv2_p->leit02," "))
	{
	
		/*leitung 1*/
		strcpy(leit, fpekodatv2_p->leit01);
		ptr = strchr(leit,'-');
		*ptr = '\0';
		strcpy(quer_1,leit);
		quer_1_number += atof(quer_1);
		strcpy(leit,ptr + 1);
		ptr = strchr(leit,'-');
		if(ptr!=NULL)
		{
			*ptr = '\0';
			strcpy(quer_2,leit);
			strcpy(quer_3,ptr + 1);
		}
		else
			strcpy(quer_2,leit);
		quer_2_number+= atoi(quer_2);
		/*leitung 2*/
		strcpy(leit, fpekodatv2_p->leit02);
		split_leit(leit,quer_1,quer_2);
		quer_1_number += strtod(quer_1, NULL);
		if(strcmp(quer_2,""))
			quer_2_number = quer_2_number + atoi(quer_2);
		//leitung 3
		if(strcmp(fpekodatv2_p->leit03," "))
		{
			strcpy(leit, fpekodatv2_p->leit03);
			split_leit(leit,quer_1,quer_2);
			quer_1_number += atof(quer_1);
			if(strcmp(quer_2,""))
				quer_2_number = quer_2_number + atoi(quer_2);
			if(strcmp(fpekodatv2_p->leit04," "))
			{
			  strcpy(leit, fpekodatv2_p->leit04);
			  split_leit(leit,quer_1,quer_2);
			  quer_1_number += atof(quer_1);
			  if(strcmp(quer_2,""))
				quer_2_number = quer_2_number + strtod(quer_2, NULL);
			  if(strcmp(fpekodatv2_p->leit05," "))
			  { 
			    strcpy(leit, fpekodatv2_p->leit05);
				split_leit(leit,quer_1,quer_2);
				quer_1_number += strtod(quer_1, NULL);
				if(strcmp(quer_2,""))
					quer_2_number = quer_2_number + strtod(quer_2, NULL);
				if(strcmp(fpekodatv2_p->leit06," "))
				{
			      strcpy(leit, fpekodatv2_p->leit06);
				  split_leit(leit,quer_1,quer_2);
				  quer_1_number += strtod(quer_1, NULL);
				  if(strcmp(quer_2,""))
					quer_2_number = quer_2_number + strtod(quer_2, NULL);
				  if(strcmp(fpekodatv2_p->leit07," "))
				  {
			        strcpy(leit, fpekodatv2_p->leit07);
					split_leit(leit,quer_1,quer_2);
					quer_1_number += strtod(quer_1, NULL);
					if(strcmp(quer_2,""))
						quer_2_number = quer_2_number + strtod(quer_2, NULL);
					if(strcmp(fpekodatv2_p->leit08," "))
					{
						strcpy(leit, fpekodatv2_p->leit08);
						split_leit(leit,quer_1,quer_2);
						quer_1_number += strtod(quer_1, NULL);
						if(strcmp(quer_2,""))
							quer_2_number = quer_2_number + strtod(quer_2, NULL);
						if(strcmp(fpekodatv2_p->leit09," "))
						{
							strcpy(leit, fpekodatv2_p->leit09);
							split_leit(leit,quer_1,quer_2);
							quer_1_number += strtod(quer_1, NULL);
							if(strcmp(quer_2,""))
								quer_2_number = quer_2_number + strtod(quer_2, NULL);
					    if(strcmp(fpekodatv2_p->leit10," "))
						{
							strcpy(leit, fpekodatv2_p->leit10);
							split_leit(leit,quer_1,quer_2);
							quer_1_number += strtod(quer_1, NULL);
							if(strcmp(quer_2,""))
								quer_2_number = quer_2_number + strtod(quer_2, NULL);
					
						}						
						}							
					}						
				  }	
				  
				}
			
			  }
				
		}
	    }
	  if(strcmp(quer_3,""))
		sprintf(fpekoda2v2.ltggrp,"%f-%d-%s",quer_1_number,quer_2_number, quer_3);
	  else
		  sprintf(fpekoda2v2.ltggrp,"%.2f-%d",quer_1_number,quer_2_number);
	}
	else
		strcpy(fpekoda2v2.ltggrp, fpekodatv2_p->leit01);
	EXEC SQL declare c_fpekoda2v2 cursor with hold for
		select * into :fpekoda2v2 from fpekoda2v2
		where   firmnr = :fpekoda2v2.firmnr
		and werknr = :fpekoda2v2.werknr
		and konttn = :fpekoda2v2.konttn
		and ltggrp = :fpekoda2v2.ltggrp;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare cursor c_fpekoda2v2");
	EXEC SQL open c_fpekoda2v2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open cursor c_fpekoda2v2");
	while (1) {
		EXEC SQL fetch c_fpekoda2v2;
		SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fetch cursor c_fpekoda2v2");
		if (SQLCODE < 0 || SQLCODE == SQLNOTFOUND) {     /* error or not found */
			if ( counter != 0 ) break;
		}
		counter++;
		if (SQLCODE != SQLNOTFOUND) {
			DBG_log(5, "D610_crimp_exp_part: found fpekoda2v2 konttn [%s] ltggrp [%s] kundnr [%s]",
					fpekoda2v2.konttn, fpekoda2v2.ltggrp, fpekoda2v2.kundnr);
			kill_all_blanks(fpekoda2v2.kundnr);
			kill_all_blanks(fpekoda2v2.zusttn);
			if (strlen(fpekoda2v2.kundnr) == 0 || strcmp(fpekoda2v2.kundnr, "SUPP") == 0) {		/* empty or SUPP */
				fpekoda2v2_found = 1;
				memcpy(&fpekoda2v2_data, &fpekoda2v2, sizeof(FPEKODA2V2));				/* copy last found */
			} else {										/* real customer */
				if (strcmp(fpekoda2v2.kundnr, fpekodatv2_p->kundnr) == 0) {
					fpekoda2v2_found = 2;		/* best match real customer, so while ends */
					memcpy(&fpekoda2v2_data, &fpekoda2v2, sizeof(FPEKODA2V2));
				}
			}
			if (fpekoda2v2_found) {
				DBG_log(3, "D610_crimp_exp_part: fpekoda2v2 found match (%d) zusttn [%s] kundnr [%s]",
						fpekoda2v2_found, fpekoda2v2_data.zusttn, fpekoda2v2_data.kundnr);
			} else {
				DBG_log(1, "D610_crimp_exp_part: WARN no fpekoda2v2 found for teilnr [%s] leit01 [%s] kundnr [%s]",
						fpekodatv2_p->teilnr, fpekodatv2_p->leit01, fpekodatv2_p->kundnr);
				rc |= D610_CRIMP_EXP_WARN_NOFPEKODA2V2;
			}
		}


		/* get fbwparam DEFTOL  for PullOffForce */
		/* TODO speedup by reading fbwparam[DEFTOL] once in linked_list */
		if (counter == 1){
			memset(&fbwparam, 0, sizeof(FBWPARAM));
			memset(&fbwparam_data, 0, sizeof(FBWPARAM));
			fbwparam.firmnr = fpekodatv2_p->firmnr;
			fbwparam.werknr = fpekodatv2_p->werknr;
			strcpy(fbwparam.schlgr, "DEFTOL");	/* schlsl format (fpeco001): leityp[5] + awgtyp[5] + kundnr */
			EXEC SQL declare c_fbwparam cursor with hold for
				select * into :fbwparam from fbwparam
				where   firmnr = :fbwparam.firmnr
				and werknr = :fbwparam.werknr
				and schlgr = :fbwparam.schlgr;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare cursor c_fbwparam");
			EXEC SQL open c_fbwparam;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open cursor c_fbwparam");
			while (fbwparam_found != 2) {
				EXEC SQL fetch c_fbwparam;
				SQLCODE_ERROR_LOGONLY(__LINE__, "LOG", "caofors.ec", "fetch cursor c_fbwparam");
				if (SQLCODE < 0 || SQLCODE == SQLNOTFOUND) {     /* error or not found */
					break;
				}
				memset(&fbwparam_schlsl, 0, sizeof(fbwparam_schlsl));
				strncpy(fbwparam_schlsl.leityp, &fbwparam.schlsl[0],  5);
				strncpy(fbwparam_schlsl.awgtyp, &fbwparam.schlsl[5],  5);
				strncpy(fbwparam_schlsl.kundnr, &fbwparam.schlsl[10], 15);
				kill_all_blanks(fbwparam_schlsl.leityp);
				kill_all_blanks(fbwparam_schlsl.awgtyp);
				kill_all_blanks(fbwparam_schlsl.kundnr);
				string_trans(fbwparam_schlsl.leityp, ",", ".");
				fbwparam_schlsl.leityp_f = atof(fbwparam_schlsl.leityp);
				DBG_log(5, "D610_crimp_exp_part: found c_fbwparam DEFTOL schlsl [%s] [%s(%.2lf),%s,%s]",
						fbwparam.schlsl, fbwparam_schlsl.leityp, fbwparam_schlsl.leityp_f,
						fbwparam_schlsl.awgtyp, fbwparam_schlsl.kundnr);

				if (fbwparam_schlsl.leityp_f != fpekodatv2_p->quer01) continue;

				if (strlen(fbwparam_schlsl.kundnr) == 0 || strcmp(fbwparam_schlsl.kundnr, "SUPP") == 0) { /* empty or SUPP */
					fbwparam_found = 1;						/* continue to search of better match */
					memcpy(&fbwparam_data, &fbwparam, sizeof(FBWPARAM));				/* copy last found */
				} else {										/* real customer */
					if (strcmp(fbwparam_schlsl.kundnr, fpekodatv2_p->kundnr) == 0) {
						fbwparam_found = 2;					/* best match real customer, so while ends */
						memcpy(&fbwparam_data, &fbwparam, sizeof(FBWPARAM));				/* copy last found */
					}
				}
			}
			EXEC SQL close c_fbwparam;
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_fbwparam");
			if (fbwparam_found) {
				kill_all_blanks(fbwparam_data.datenf);
				DBG_log(3, "D610_crimp_exp_part: fbwparam found match (%d) [%s] datentf [%s]",
						fbwparam_found, fbwparam_data.schlsl, fbwparam_data.datenf);
			} else {		/* ERROR */
				DBG_log(1, "D610_crimp_exp_part: WARN no fbwparam[DEFTOL] found for quer01: %.2lf kundnr [%s]",
						fpekodatv2_p->quer01, fpekodatv2_p->kundnr);
				rc |= D610_CRIMP_EXP_WARN_NOFBWPARAM;
			}
		}

#if 0			/* not needed, wrong in spec */
		/* get matr */
		memset(&matr, 0, sizeof(MATR));
		matr.firmnr       = fpekodatv2_p->firmnr;
		matr.fewerk       = fpekodatv2_p->werknr;
		strcpy(matr.teilnr, fpekodatv2_p->teilnr);
		strcpy(matr.resgrp, "***");			/* if not found */
		EXEC SQL
			select * into :matr from matr
			where   firmnr = :matr.firmnr
			and fewerk = :matr.fewerk
			and teilnr = :matr.teilnr;
		if (SQLCODE == -284) {				/* if multiple rows per teilnr (resgrp) */
			strcpy(matr.resgrp, "*-*");
		} else {
			SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "matr");
		}
		if (SQLCODE == SQLNOTFOUND) {
			DBG_log(3, "D610_crimp_exp_part: WARN no matr teilnr [%s]", matr.teilnr);
			/* continue, matr unchanged initialized above */
		}
#endif

		memset(&crimp_dat, 0, sizeof(crimp_dat));
		snprintf(crimp_dat.termkey,    MAXLEN_CRIMPE_TERMKEY,   "%s", fpekodatv2_p->teilnr);
		/* TODO only for magd.matken = EDICH (Seal) and/or CKONT (terminal) ? */
		snprintf(crimp_dat.sealkey,    MAXLEN_CRIMPE_SEALKEY,   "%s", "");
		snprintf(crimp_dat.wiretype1,  MAXLEN_CRIMPE_WIRETYPE,  "%s", fpekodatv2_p->leit01);
		crimp_dat.crosssect1					= fpekodatv2_p->quer01;
		snprintf(crimp_dat.wiretype2,  MAXLEN_CRIMPE_WIRETYPE,  "%s", fpekodatv2_p->leit02);
		crimp_dat.crosssect2					= fpekodatv2_p->quer02;
		snprintf(crimp_dat.inventno,   MAXLEN_CRIMPE_INVENTNO, "%s",  "*");
		/* snprintf(crimp_dat.inventno,   MAXLEN_CRIMPE_INVENTNO, "%s",  matr.resgrp); */

		crimp_dat.strip_len						= atof(magd_cmp.indiv0);
		crimp_dat.strip_len_min					= 0.0;
		crimp_dat.strip_len_plus					= 0.0;

		crimp_dat.crimp_height					= fpekodatv2_p->dracrh;
		crimp_dat.crimp_height_tol					= fpekodatv2_p->tlcrhp;
		crimp_dat.crimp_height_tolmin				= fpekodatv2_p->tlcrhm;
		crimp_dat.crimp_width					= fpekodatv2_p->dracrb;
		crimp_dat.crimp_width_tol					= fpekodatv2_p->tlcrbp;
		crimp_dat.crimp_width_tolmin				= fpekodatv2_p->tlcrbm;

		crimp_dat.isocrimp_height					= fpekodatv2_p->isocrh;
		crimp_dat.isocrimp_height_tol				= fpekodatv2_p->ticrhp;
		crimp_dat.isocrimp_height_tolmin				= fpekodatv2_p->ticrhm;
		crimp_dat.isocrimp_width					= fpekodatv2_p->isocrb;
		crimp_dat.isocrimp_width_tol				= fpekodatv2_p->ticrbp;
		crimp_dat.isocrimp_width_tolmin				= fpekodatv2_p->ticrbm;

		if (fbwparam_found) {
			crimp_dat.pulloff_force					= atol(fbwparam_data.datenf);
		} else {
			crimp_dat.pulloff_force					= 0;			/* ERROR ? */
		}
		if (!strcmp(fpekodatv2_p->kundnr, "SUPP")) {
			snprintf(crimp_dat.customer,   MAXLEN_CRIMPE_CUSTOMER, "%s", "*");
		} else {
			snprintf(crimp_dat.customer,   MAXLEN_CRIMPE_CUSTOMER, "%s", fpekodatv2_p->kundnr);
		}
		snprintf(crimp_dat.project,    MAXLEN_CRIMPE_PROJECT , "%s", "");			/* TODO */
		snprintf(crimp_dat.validfrom_date, 9 , "%ld",                fpekodatv2_p->giltab);

		/* write line without term - seal relation but not if fpekoda2v2_data.zusttn is empty (then use only this) */
		if (!written) {
			if (! (fpekoda2v2_found && strlen(fpekoda2v2_data.zusttn) == 0)) {
				rc |= D620_crimp_exp_line(fd, fname, &crimp_dat);
			}
		}
		written++;
		/* write add. line */ 
		if (fpekoda2v2_found) {		/* term - seal relation */
			snprintf(crimp_dat.sealkey, MAXLEN_CRIMPE_SEALKEY,  "%s", fpekoda2v2_data.zusttn);
			crimp_dat.isocrimp_height				= fpekoda2v2_data.isocrh;
			crimp_dat.isocrimp_height_tol				= fpekoda2v2_data.ticrhp;
			crimp_dat.isocrimp_height_tolmin			= fpekoda2v2_data.ticrhm;
			crimp_dat.isocrimp_width				= fpekoda2v2_data.isocrb;
			crimp_dat.isocrimp_width_tol				= fpekoda2v2_data.ticrbp;
			crimp_dat.isocrimp_width_tolmin				= fpekoda2v2_data.ticrbm;

			rc |= D620_crimp_exp_line(fd, fname, &crimp_dat);
		}
	}
	EXEC SQL close c_fpekoda2v2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_fpekoda2v2");
	return rc;
}
/**********************************************************************
 *   Name            : D220_handling_unit_exp_line
 *   Funktion        : write one CSV line in file
 *   Parameter       :
 *   Return 	     : OK, or bitmask see #define D220_HANDLING_UNIT_EXP_ERROR*
 **********************************************************************/
int D220_handling_unit_exp_line(int fd, const char * fname, record_handling_unit_exp_t *handling_unit_dat_p)
{
	int rc = OK, rw, rlen;
	char buffer_line [MAX_CSVLINE_LEN+1];

	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,"%s;%s;%s;%s;%s;%s;%lf;%d;%s;%d\n",
			handling_unit_dat_p->plant_id,handling_unit_dat_p->sloc,			
			handling_unit_dat_p->snr,handling_unit_dat_p->material,
			handling_unit_dat_p->handling_unit,handling_unit_dat_p->charge,	  	
			handling_unit_dat_p->quantity,handling_unit_dat_p->unit,
			handling_unit_dat_p->bme,handling_unit_dat_p->sender
				);
	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D220_handling_unit_exp_line: csv line too long");
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		rc |= D220_HANDLING_UNIT_EXP_ERROR;
		return rc;
	}
	rw = G_write_line(fd, buffer_line);
	DBG_log(5, "D220_handling_unit_exp_line written: %d", rw);
	if (rw == ERROR) rc |= D220_HANDLING_UNIT_EXP_ERROR;

	return rc;
}

/**********************************************************************
 *   Name            : D600_crimp_exp_line
 *   Funktion        : write one CSV line in file
 *   Parameter       :
 *   Return 	     : OK, or bitmask see #define D610_CRIMP_EXP_*
 **********************************************************************/
int D620_crimp_exp_line(int fd, const char * fname, record_crimp_exp_t *crimp_dat_p)
{
	int rc = OK, rw, rlen;
	char buffer_line [MAX_CSVLINE_LEN+1];

	rlen = snprintf(buffer_line, MAX_CSVLINE_LEN,
			"%s;%s;%s;%.2lf;%s;%.2lf;%s;"
			"%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;"
			"%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;%.2lf;"
			"%ld;%s;%s;%s\n",
			crimp_dat_p->termkey,		crimp_dat_p->sealkey,
			crimp_dat_p->wiretype1,		crimp_dat_p->crosssect1,
			crimp_dat_p->wiretype2,		crimp_dat_p->crosssect2,
			crimp_dat_p->inventno,

			crimp_dat_p->strip_len,
			crimp_dat_p->strip_len_min,		crimp_dat_p->strip_len_plus,
			crimp_dat_p->crimp_height,		crimp_dat_p->crimp_height_tol,
			crimp_dat_p->crimp_height_tolmin,	crimp_dat_p->crimp_width,
			crimp_dat_p->crimp_width_tol,	crimp_dat_p->crimp_width_tolmin,

			crimp_dat_p->isocrimp_height,	crimp_dat_p->isocrimp_height_tol,
			crimp_dat_p->isocrimp_height_tolmin,crimp_dat_p->isocrimp_width,
			crimp_dat_p->isocrimp_width_tol,	crimp_dat_p->isocrimp_width_tolmin,

			crimp_dat_p->pulloff_force,		crimp_dat_p->customer,
			crimp_dat_p->project,		crimp_dat_p->validfrom_date
				);
	if (rlen == MAX_CSVLINE_LEN) {
		DBG_err(1, "D610_crimp_exp_line: csv line too long");
		buffer_line[MAX_CSVLINE_LEN] = '\0';            /* ensure \0 if line too long */
		rc |= D610_CRIMP_EXP_ERROR;
		return rc;
	}
	rw = G_write_line(fd, buffer_line);
	DBG_log(5, "D610_crimp_exp_line written: %d", rw);
	if (rw == ERROR) rc |= D610_CRIMP_EXP_ERROR;

	return rc;
}

/**********************************************************************
 *   Name            : D700_crimp_upd_magi customer & wireclass
 *   Funktion        : update MAGI indi MAGI_INDI_CUST MAGI_INDI_WIRECL
 *   Parameter       :
 *   Return 	     : OK, ERROR
 **********************************************************************/
int D700_crimp_upd_magi_custom(void)
{
	int rc = OK;
	int ffscust_cnt = 0;


	DBG_log(1, "D700_crimp_upd_magi_custom");
	set_fonatran("CRPU");

	/* update magi.MAGI_INDI_CUST  from ffsso002cust.kundnr [teilvo .. teilbi] */
	EXEC SQL declare c_ffsso002cust cursor with hold for
		select * into :ffsso002cust from ffsso002cust
		--WHERE aendat >= :fbwstort.lexdat
		order by teilvo;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare ffsso002cust");
	EXEC SQL open c_ffsso002cust;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_ffsso002cust");
	while (1) {
		EXEC SQL fetch c_ffsso002cust;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_ffsso002cust");
		if (SQLCODE == SQLNOTFOUND) {
			break;
		}
		ffscust_cnt++;
		DBG_log(4, "D700_crimp_upd_magi_custom: ffsso002cust %d teilvo-bi [%s]-[%s] kundnr [%s]", ffscust_cnt,
				ffsso002cust.teilvo, ffsso002cust.teilbi, ffsso002cust.kundnr);
	/*	EXEC SQL update magi set S_MAGI_INDI_CUST = :ffsso002cust.kundnr
			where   S_MAGI_INDI_CUST = ' '
			and teilnr >= :ffsso002cust.teilvo
			and teilnr <= :ffsso002cust.teilbi;*/
			gettoday (check_date, "JJJJMMTT");
		  date_update = atol (check_date);
			gettimeofday (check_time, "HHMMSSSS");
			real_time = atol (check_time);
			EXEC SQL update magi set S_MAGI_INDI_CUST = :ffsso002cust.kundnr, aendat=:date_update, aenzei=:real_time
			where teilnr >= :ffsso002cust.teilvo
			and teilnr <= :ffsso002cust.teilbi
			and S_MAGI_INDI_CUST != :ffsso002cust.kundnr;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi.indi_cust upd");
	}
	DBG_log(1, "D700_crimp_upd_magi_custom: found ffsso002cust total: %d", ffscust_cnt);
	EXEC SQL close c_ffsso002cust;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_ffsso002cust");

	/* count customer indi empty */
	EXEC SQL select count(teilnr) into :sql_tmpint from magi where S_MAGI_INDI_CUST = ' ';
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi.indi_cust count");
	DBG_log(1, "D700_crimp_upd_magi_custom: WARN magi with no customer (%s): %d",
			_STRDEF(MAGI_INDI_CUST), sql_tmpint);

	return rc;
}

int D700_crimp_upd_magi_wirecl(void)
{
	int rc = OK;
	int ffswgv2_cnt = 0;

	DBG_log(1, "D700_crimp_upd_magi_wirecl");
	set_fonatran("CRPU");
	/* update magi.indi Wireclass  from ffsso002wgv2.wiregroup [] */
	EXEC SQL declare c_ffsso002wgv2 cursor with hold for
		select * into :ffsso002wgv2 from ffsso002wgv2
		WHERE aendat >= :fbwstort.lexdat
		OR    erfdat >= :fbwstort.lexdat
		order by wiretype, ltgque;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "declare ffsso002wgv2");
	EXEC SQL open c_ffsso002wgv2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "open c_ffsso002wgv2");
	while (1) {
		EXEC SQL fetch c_ffsso002wgv2;
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "fetch c_ffsso002wgv2");
		if (SQLCODE == SQLNOTFOUND) {
			break;
		}
		ffswgv2_cnt++;
		if ((ffswgv2_cnt % 50)==0) DBG_log(1, "D700_crimp_upd_magi_wirecl: ===== ffsso002wgv2 processed count %d",ffswgv2_cnt);
		sprintf(sql_tmpstr, "%.2lf", ffsso002wgv2.ltgque);		/* as string in magd.indiv1 xx.yy */
		DBG_log(4, "D700_crimp_upd_magi_wirecl: ffsso002wgv2 %d wiregroup [%s] wiretype [%s] ltgque [%lf]->[%s]",
				ffswgv2_cnt, ffsso002wgv2.wiregroup, ffsso002wgv2.wiretype, ffsso002wgv2.ltgque, sql_tmpstr);
		/* update magi.indi_wirecl */ 	/* e.g. klassf/indiv1 FLRY-B / 0.35 */
		/* update by magd loop with one magi.teilnr instead of list is slower */
		/*EXEC SQL update magi set S_MAGI_INDI_WIRECL = :ffsso002wgv2.wiregroup
			where   S_MAGI_INDI_WIRECL = ' '
			and teilnr in
			(select teilnr from magd where klassf = :ffsso002wgv2.wiretype and indiv1 =:sql_tmpstr);*/
		gettoday (check_date, "JJJJMMTT");
		date_update = atol (check_date);
		gettimeofday (check_time, "HHMMSSSS");
		real_time = atol (check_time);
	  EXEC SQL update magi set S_MAGI_INDI_WIRECL = :ffsso002wgv2.wiregroup , aendat=:date_update, aenzei=:real_time
			where indi43 != :ffsso002wgv2.wiregroup 
			and teilnr in (select teilnr from magd where klassf = :ffsso002wgv2.wiretype and indiv1 =:sql_tmpstr);
	
		SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi.indi_wirecl upd");
	}
	DBG_log(1, "D700_crimp_upd_magi_wirecl: found ffsso002wgv2 total: %d", ffswgv2_cnt);
	EXEC SQL close c_ffsso002wgv2;
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "close c_ffsso002wgv2");

	/* count indi wireclass empty */
	EXEC SQL select count(teilnr) into :sql_tmpint from magi where S_MAGI_INDI_WIRECL =' ';
	SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "magi.indi_wirecl count");
	DBG_log(1, "D700_crimp_upd_magi: WARN magi with no WireKlassKey (%s): %d",
			_STRDEF(MAGI_INDI_WIRECL),  sql_tmpint);

	return rc;
}

/**********************************************************************
 *  globals
 **********************************************************************/

/**********************************************************************
 *   Name            :  G_get_color_code
 *   Funktion        :  convert forscolor -> caocolor (DB.caocolors)
 *   Parameter       :  forscolor
 *   			caocolp  : ptr to return DB row
 *   Return Code     : ptr to caocol_list_t
 * 			NULL not found
 **********************************************************************/
caocol_list_t * G_get_color_code(char *forscolor)
{
	int col_cnt = 0;
	char *keystr;
	caocol_list_t caocol_list;
	caocol_list_t *caocol_list_p;

	DBG_log(5, "G_get_color_code: forscolor [%s]", forscolor);

	if ((! g.caocol_list) && (done == 'N')) {				/* read all colors on 1. use, and build linked list */
		DBG_log(3, "G_get_color_code: read caocolors");
		memset(&caocolors, 0, sizeof(CAOCOLORS));		/* clear all if not found */
		caocolors.firmnr = parm_firma;
		caocolors.werknr = parm_werk; 
		EXEC SQL declare c_caocol cursor with hold for
			select * into :caocolors from caocolors
			where 	caocolors.firmnr = :caocolors.firmnr
			and caocolors.werknr = :caocolors.werknr
			order by urspc;

		SQLCODE_ERROR_PUTERRDB_RETVAL(__LINE__, "LOG", "caofors.ec", "caocolors", (caocol_list_t*)NULL);
		EXEC SQL open c_caocol;
		SQLCODE_ERROR_PUTERRDB_RETVAL(__LINE__, "LOG", "caofors.ec", "caocolors", (caocol_list_t*)NULL);
		while (1) {
			EXEC SQL fetch c_caocol;
			SQLCODE_ERROR_PUTERRDB_RETVAL(__LINE__, "LOG", "caofors.ec", "fetch c_caocol", NULL);
			if (SQLCODE == SQLNOTFOUND) {
				if(!col_cnt){
					done = 'Y' ;
					caocolors.firmnr = parm_firma ;
					caocolors.werknr = parm_werk ;
					strcpy(caocolors.urspc , "101") ;
					strcpy(caocolors.color1 , "10") ;
					strcpy(caocolors.color2 , " ") ; 
					strcpy(caocolors.color3 , " ") ;
					strcpy(caocolors.color4 , " ") ;
					keystr = caocolors.urspc;
					memcpy(&caocol_list.caocolors, &caocolors, sizeof(CAOCOLORS));
					caocol_list_p = list_link_add(&g.caocol_list, 's', keystr, sizeof(caocol_list_t), &caocol_list);
				}
				DBG_log(3, "G_get_color_code: c_caocol fetch end (found total: %d)", col_cnt);
				break;
			}
			col_cnt++;
			DBG_log(5, "G_get_color_code: got caocolor urspc [%s]", caocolors.urspc);
			keystr = caocolors.urspc;
			memcpy(&caocol_list.caocolors, &caocolors, sizeof(CAOCOLORS));
			caocol_list_p = list_link_add(&g.caocol_list, 's', keystr, sizeof(caocol_list_t), &caocol_list);
		}
		EXEC SQL close c_caocol;
		SQLCODE_ERROR_PUTERRDB_RETVAL(__LINE__, "LOG", "caofors.ec", "caocolors", (caocol_list_t*)NULL);
	}
	keystr = forscolor;
	caocol_list_p = list_link_search(&g.caocol_list, 's', keystr);
	if (!caocol_list_p) { 
		/*update 05.04.2017 hakh1013
		  if a color is not fond in table caocolors,
		  then the colors data as default is “white” keystr=010 */
		DBG_err(1, "G_get_color_code: forscol [%s] NOT found in DB.caocolors, setting the default color to 010 (white)", forscolor);
		strcpy(keystr,"101");
		caocol_list_p = list_link_search(&g.caocol_list, 's', keystr);
	}

	DBG_log(4, "G_get_color_code: CAOCOLORS forscol [%s] ->[%s,%s,%s,%s]",
			forscolor, caocol_list_p->caocolors.color1, caocol_list_p->caocolors.color2,
			caocol_list_p->caocolors.color3, caocol_list_p->caocolors.color4);
	return caocol_list_p;
}

/**********************************************************************
 *   Name            : G_fona_msg
 *   Funktion        : write fona msg
 *   Parameter       : srcline: source code line in meldid
 *   Return Code     : -
 **********************************************************************/
void G_fona_msg (int srcline, const char * infomsg)
{
	char timebuf[20];

	get_current_time(2, timebuf, NULL);		/* yyyymmdd-hhmmss */

	/* probably set SRCLINE into (unused) aktstz, (need to add pbpmsgblk) */
	strcpy (msg.userid, CAO_USERFONA);
	msg.jobdat = atol(&timebuf[0]);		/* until '-' */
	msg.jobtim = atol(&timebuf[9]);
	sprintf(msg.meldid, "L:%d", srcline);
	snprintf(msg.meldtx, sizeof(msg.meldtx)-1, "%s", infomsg);
	msg.meldtx[sizeof(msg.meldtx)-1] = '\0';
	put_fona("PUT");
}

/**********************************************************************
 *   Name            :  G_write_line
 *   Funktion        :  write line to file
 *   Parameter       :  fdg : open file descriptor
 *   Return Code     : OK, ERROR
 **********************************************************************/
int G_write_line (int fdg, char *line)
{
	int rlen;
	int len = strlen(line);

	rlen = write(fdg, line, len);
	if (rlen != len) {
		DBG_err(1, "ERROR errno (%d, %s) - write in file (len: %d instead of %d)",
				errno, strerror(errno), rlen, len);
		return ERROR;
	}
	return 0;
}

/**********************************************************************
 *   Name            :  G_write_fileappend
 *   Funktion        :  write data to file
 *   Parameter       :  filename : full filename
 *			data: data to write
 *   Return Code     : OK, ERROR
 **********************************************************************/
int G_write_fileappend (const char *filename, char *data, int data_len)
{
	int rlen, fd, rc;


	DBG_log(4, "G_write_fileappend [%s] : (%d)", filename, data_len);
	fd = open(filename, O_CREAT|O_APPEND|O_WRONLY, 0666);
	if (!fd) {
		DBG_err(1, "ERROR errno (%d, %s) - open write file (%s)",
				errno, strerror(errno), filename);
		return ERROR;
	}
	rlen = write(fd, data, data_len);
	if (rlen != data_len) {
		DBG_err(1, "ERROR errno (%d, %s) - write in file (%s) (len: %d instead of %d)",
				errno, strerror(errno), filename, rlen, data_len);
		rc = close(fd); 		/* try close */
		return ERROR;
	}
	rc = close(fd);
	if (rc != OK) {
		DBG_err(1, "ERROR errno (%d, %s) - close write file (%s)",
				errno, strerror(errno), filename);
	}
	DBG_log(5, "G_write_fileappend [%s]: written (%d)", filename, rlen);
	return rc;
}

/* -------------------------------------------------------------------------- *
 * Definition  : int G_copy_file()
 * Description : Copy file to the remote server or a directory
 * Parameters:
 *               src_file   : src files (incl path !)
 *               server     : server name
 *               dst_dir   : dest path
 * Return:  OK, ERROR
 * -------------------------------------------------------------------------- */
int G_copy_file(char * src_file, char * server, char * dst_dir)
{
	int status;
	char *srcfile_basename;	/* filename without path */
	char *xpos;

	char destFile [MAX_FILENAME_LEN + EN];

	DBG_log(5, "G_copy_file: %s, %s, %s", src_file, server, dst_dir);
	xpos = strrchr(src_file, '/');
	if (xpos) {
		srcfile_basename = ++xpos;	/* skip last '/' */
	} else {
		srcfile_basename = src_file;	/* no path */
	}
	sprintf(destFile, "%s/%s", dst_dir, srcfile_basename);

	if (g.dont_copy) {
		DBG_log(1, "G_copy_file TEST:NOTCOPIED %s -> %s @ %s", src_file, server, destFile);
		return OK;
	}
	DBG_log(1, "G_copy_file %s -> %s @ %s", src_file, server, destFile);
	if ((status = transfer_file(src_file, server, destFile, PUT_FILE)) != OK) {
		DBG_err(1, "G_copy_file %s -> %s @ %s", src_file, server, destFile);
		return ERROR;
	}
	return OK;
}
/*******************************************************************************
 * function: G_lock_process
 *   check if you have another process; prevent double run of same task!
 * parameters:
 *	task:  task to lock MAT_TO_CAO | ORDER_LOOP (only mode=0)
 *      mode:  0:check-running & create
 *             1:delete
 *             2:check only, not create !
 * return: 0: ok, 1: already runnning (LOCKED!), ERROR
 ******************************************************************************/
int G_lock_process (const char * task)
{
	FILE *fp;
	char command[100];
	char count[5];
sprintf (command, "ps -C caofors -opid,user,cmd | grep %s |grep %s | wc -l\n",task,parm_dbid);
		fp = popen(command, "r");
		if (fp == NULL) {
			log_ent("Failed to run command '%s'", command );
			return -1;
		}
		fgets(count, sizeof(count), fp);
		if( atoi(count) > 1 ){
			DBG_err(1, "CAOFORS ERROR task [%s] locked (already runs ). EXIT.", task);
			pclose(fp);
			return 1;
		}
		pclose(fp);
		return 0;
}
/*******************************************************************************
 * function: G_create_lock_file
 *    create lock file in /tmp prevent double run of same task!
 * parameters:
 *	task:  task to lock MAT_TO_CAO | ORDER_LOOP (only mode=0)
 *      mode:  0:check-running & create
 *             1:delete
 *             2:check only, not create !
 * return: 0: ok, 1: already runnning (LOCKED!), ERROR
 ******************************************************************************/
int G_lock_file (const char * task, int mode)
{
	int rc = OK, rs, rl;
	struct stat fstat;
	char lbuf[2048];

	if (mode == 0 || mode == 2) {
		sprintf(g.lock_file, "%s/caofors.%s.lock", g.tmpdir, task);
		rs = stat(g.lock_file, &fstat);
		if (rs == 0) {		/* file exists */
			DBG_err(1, "CAOFORS ERROR task [%s] locked (already runs %s). EXIT.", task, g.lock_file);
			return 1;
		}
		if (mode == 2) return 0;
		/* create LOCK-file (3 lines info: prog version & args, who, when, WERK environ) */
		rl = sprintf(lbuf, "%s\nstart: PID: %d date: %s %s\nFirma Werk: %d %d DB-ID:%s\n",
				g.start_msg, g.mypid, start_date, start_time, parm_firma, parm_werk, parm_dbid);
		rs = G_write_fileappend(g.lock_file, lbuf, rl);
		g.has_locked = 1;
		if (rs != OK) {
			DBG_err(1, "CAOFORS ERROR create lock [%s]. EXIT.", g.lock_file);
			return ERROR;
		}
		return 0;

	} else if (mode == 1) {
		rc = G_delete_file(g.lock_file, 1);
	}
	return rc;
}

/*******************************************************************************
 * function: G_check_stop_file
 * 	check if stop file exists (ORDER_LOOP stop)
 * parameters:
 *	filename: filename to check (caofors.stop)
 *      check in /tmp/                (AVOID no delete permission)
 *      check in /tmp/cao.tmp$WERK/   (USE THIS ONLY)
 *   ATTN: use only in /tmp/cao.tmp$WERK/
 *         as proc is running as VSR1 and user touch file
 *         no permission to delete in /tmp/ but in /tmp/cao.tmp$WERK
 * return: 0: ok, 1: stop file existed
 ******************************************************************************/
int G_check_stop_file (const char * filename)
{
	int rc = OK, rs;
	struct stat fstat;
	char fname[256];

	/* global /tmp/caofors.stop       (!! AVOID due to permissions in /tmp) */
	sprintf(fname, "%s/%s", g.tmpdir, filename);

	rs = stat(fname, &fstat);
	if (rs == 0) {		/* file exists */
		DBG_log(1, "CAOFORS found STOP file [%s] (WARN permissions better use %s/%s)", fname, g.tmpdir, filename);
		G_delete_file(fname, 1);
		return 1;
	}

	/* /tmp/caofors.stop$WERK         (!! AVOID due to permissions in /tmp) */
	sprintf(fname, "/tmp/%s%d", filename, parm_werk);

	rs = stat(fname, &fstat);
	if (rs == 0) {		/* file exists */
		DBG_log(1, "CAOFORS found STOP file [%s] (WARN permissions better use %s/%s)", fname, g.tmpdir, filename);
		G_delete_file(fname, 1);
		return 1;
	}
	/* /tmp/cao.tmp$WERK/caofors.stop (!! USE this, have delete permission) */
	sprintf(fname, "%s/%s", g.tmpdir, filename);

	rs = stat(fname, &fstat);
	if (rs == 0) {		/* file exists */
		DBG_log(1, "CAOFORS found STOP file [%s]", fname);
		G_delete_file(fname, 1);
		return 1;
	}
	return rc;
}


/*******************************************************************************
 * function: G_delete_file
 *    delete file
 * parameters:
 *      filename:  filename to delete
 *      force_delete: 1 (ignore -k option)
 * return: 0: ok, else ERROR
 ******************************************************************************/

int G_delete_file(const char *filename, int force_delete)
{
	int rc = OK;

	if (g.keep_files && !force_delete) {
		DBG_log(1, "G_delete_file TEST:NOTDELETED %s", filename);
		return OK;
	}
	DBG_log(4, "G_delete_file %s", filename);
	if ( (rc = unlink(filename)) != OK) {
		if (errno != ENOENT) {	/* file not exist, ignore */
			DBG_err(1, "ERROR: delete file %s ERROR (%d, %s)", filename, errno, strerror(errno));
			rc = ERROR;
		} else {	/* ignore not exist file */
			rc = OK;
		}
	}
	return rc;
}
/*******************************************************************************
 * function: G_rename_file
 *    delete file
 * parameters:
 *      filename:  filename to delete
 *      force_delete: 1 (ignore -k option)
 * return: 0: ok, else ERROR
 ******************************************************************************/

int G_rename_file(const char *filename, const char *new_filename)
{
	int rc = OK;

	DBG_log(3, "G_rename_file %s -> %s", filename, new_filename);
	if ( (rc = rename(filename, new_filename)) != OK) {
		if (errno != ENOENT) {	/* file not exist, ignore */
			DBG_err(1, "ERROR: rename file %s ERROR (%d, %s)", filename, errno, strerror(errno));
			rc = ERROR;
		} else {	/* ignore not exist file */
			rc = OK;
		}
	}
	return rc;
}

/*******************************************************************************
 * function: sigact_handler
 *    signal handler for extended sigaction()
 *
 *   ATTENTION: this funcion is called asynchronous
 *              e.g. using DB functions inside here will produce SQL errors
 *		and even coredumps in informix shared library
 * parameters:
 *      sig: signal number
 *      siginfo, contextp: additional signal info
 * return:
 ******************************************************************************/

void sigact_handler(int sig, siginfo_t *siginfo, void * contextp)
{

	DBG_log(3, "sigact_handler: got signal %d", sig);
	switch (sig) {
		case SIGHUP:
		case SIGTERM:
		case SIGINT:
			G_terminate(3);
			break;
	}
}

/*******************************************************************************
 * function: G_terminate
 *    exit, cleanup lock
 * parameters:
 *	exit code (0: OK, >0 ERROR)
 * return: -
 ******************************************************************************/
int G_terminate (int retcode)
{

	switch(g.prog_step) {		 /* fall through's ! */
		/*
		   case TOUCH_RT:
		   get_date_time(check_date,check_time);
		   touch_runtable(check_date,check_time,FALSE);
		 */
		case DB_OPEN:
			close_database();
		default:
		case START:
			break;
	}
	if (retcode == 0) {			/* OK */
		DBG_log(1, "CAOFORS end");
	} else if (retcode == 1) {		/* ERROR */
		DBG_err(1, "CAOFORS ERROR end");
		fprintf(stderr, "\n\n\nCAOFORS ERROR !!!!\n\n");
		fprintf(stderr, "\tSee Logs :\n");
		fprintf(stderr, "\t\t%s\n",errlog_name);
		fprintf(stderr, "\t &\n");
		fprintf(stderr, "\t\t%s\n\n",logfile_name);
	} else if (retcode == 2) {		/* usage ERROR */
		fprintf(stderr, "\n\nCAOFORS ERROR program usage\n\n");
		DBG_err(1, "CAOFORS ERROR end. Wrong usage.");
	} else if (retcode == 3) {		/* got signal */
		DBG_err(1, "CAOFORS end. interrupted signal");
	}

	//if (g.has_locked) {		/* delete lockfile if created */
		//G_lock_file(NULL, 1);
	//}
	exit(retcode);
}

/* -------------------------------------------------------------------------- *
 * Definition  : int A150_check_logfile ()
 * Description : Check if the logfile needs to be reset.
 * Author      : 08.07.2003,
 * Modified    : 04.12.2006, C.Marx - vsr1 - expand logging mechanism.
 *             : Archieved logfiles are deleted after some days.
 * Modified    :
 * -------------------------------------------------------------------------- */

void A150_check_logfile (void)
{
	char buffer[200],buffer1[200],buffer2[200];
	char * ptr;
	int fs = 0;

	ptr = getenv ( "LOGPATH");
	if (ptr == NULL)
	{
		sprintf (logpath, "%s", "./");
	}
	else
	{
		sprintf (logpath, "%s/", ptr);
	}
	caps_on(env_user, strlen(env_user));
	if (g.no_pidlogname) {
		if (!strcasecmp(g.do_task, "MAT_P1")){
			sprintf(buffer2,"%s_P1.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "MAT_P2")){
			sprintf(buffer2,"%s_P2.%s",PROGNAME_L, env_user);
		}
		else if (!strcasecmp(g.do_task, "ORDER_LOOP")){
			sprintf(buffer2,"%s_ORDER.%s",PROGNAME_L, env_user);
		}
	} else {
		if (!strcasecmp(g.do_task, "MAT_P1")){
			sprintf(buffer2,"%s_P1.%d.%s.log",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "MAT_P2")){
			sprintf(buffer2,"%s_P2.%d.%s.log",PROGNAME_L, g.mypid, env_user);
		}
		else if (!strcasecmp(g.do_task, "ORDER_LOOP")){
			sprintf(buffer2,"%s_ORDER.%d.%s.log",PROGNAME_L, g.mypid, env_user);
		}
		//sprintf(buffer2,"%s.%d.%s.log",PROGNAME_L, g.mypid, env_user);
	}    
	strcat (logpath, buffer2);

	fs = file_size (logpath);

	if ( fs > MAX_FILE_SIZE )
	{ 
		//snprintf (buffer, sizeof (buffer), "find %s.* -mtime +14 | xargs rm -f", logpath);
		//execute (buffer);
		reset(buffer2,SAVE,LOGFILE);


		sprintf (buffer, "%s.%08ld.%06ld", logpath, get_dbdate (), get_dbtime());
		sprintf (buffer1, "%s.old", logpath);

		rename (buffer1, buffer);

		//set_logf (PROGNAME_L);
	}
}
