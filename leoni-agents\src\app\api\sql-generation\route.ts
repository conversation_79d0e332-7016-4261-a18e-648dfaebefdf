import { NextRequest, NextResponse } from 'next/server';
import { SQLGenerationService } from '@/agents/sqlGenerationAgent';
import { SQLGenerationRequest } from '@/types';
import { prepareSpecificationForAPI } from '@/lib/textUtils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { specification, databaseType, includeComments }: SQLGenerationRequest = body;

    if (!specification || !specification.trim()) {
      return NextResponse.json(
        { error: 'La spécification est requise' },
        { status: 400 }
      );
    }

    // Préparer la spécification en gérant la longueur
    const preparedSpec = prepareSpecificationForAPI(specification);

    console.log(`API SQL: Spécification ${preparedSpec.wasTruncated ? 'tronquée' : 'normale'}`);
    console.log(`Longueur: ${preparedSpec.originalLength} → ${preparedSpec.processedLength} caractères`);
    console.log(`Tokens estimés: ${preparedSpec.estimatedTokens}`);

    const sqlGenerationService = new SQLGenerationService();
    const result = await sqlGenerationService.generateSQL({
      specification: preparedSpec.processedText,
      databaseType: databaseType || 'mysql',
      includeComments: includeComments !== false
    });

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erreur lors de la génération SQL:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  const sqlGenerationService = new SQLGenerationService();
  const agentInfo = sqlGenerationService.getAgentInfo();

  return NextResponse.json({
    success: true,
    data: agentInfo,
    timestamp: new Date().toISOString()
  });
}
