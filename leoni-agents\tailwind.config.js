/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Couleurs corporate Leoni
        'leoni': {
          'navy': '#002857',      // Bleu marine profond
          'orange': '#ff7514',    // Orange vibrant
          'navy-light': '#003d7a', // Variante plus claire du navy
          'navy-dark': '#001a3d',  // Variante plus foncée du navy
          'orange-light': '#ff8c42', // Variante plus claire de l'orange
          'orange-dark': '#e6650f',  // Variante plus foncée de l'orange
        },
        // Gradients personnalisés
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      backgroundImage: {
        'leoni-gradient': 'linear-gradient(135deg, #002857 0%, #ff7514 100%)',
        'leoni-gradient-reverse': 'linear-gradient(135deg, #ff7514 0%, #002857 100%)',
        'leoni-navy-gradient': 'linear-gradient(135deg, #001a3d 0%, #003d7a 100%)',
        'leoni-orange-gradient': 'linear-gradient(135deg, #e6650f 0%, #ff8c42 100%)',
      },
      boxShadow: {
        'leoni-navy': '0 10px 40px rgba(0, 40, 87, 0.3)',
        'leoni-orange': '0 10px 40px rgba(255, 117, 20, 0.3)',
        'leoni-glow': '0 0 30px rgba(0, 40, 87, 0.4)',
      },
      animation: {
        'leoni-pulse': 'leoni-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'leoni-float': 'leoni-float 3s ease-in-out infinite',
      },
      keyframes: {
        'leoni-pulse': {
          '0%, 100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
          '50%': {
            opacity: '0.8',
            transform: 'scale(1.05)',
          },
        },
        'leoni-float': {
          '0%, 100%': {
            transform: 'translateY(0px)',
          },
          '50%': {
            transform: 'translateY(-10px)',
          },
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [],
}
