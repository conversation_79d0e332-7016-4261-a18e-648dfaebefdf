import { NextRequest, NextResponse } from 'next/server';
import { ErrorAnalysisService } from '@/agents/errorAnalysisAgent';
import { ProgramFile } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { programFile, errorFile }: { programFile: ProgramFile; errorFile: ProgramFile } = body;

    if (!programFile || !errorFile) {
      return NextResponse.json(
        { error: 'Les fichiers programme et erreur sont requis' },
        { status: 400 }
      );
    }

    if (!programFile.content || !errorFile.content) {
      return NextResponse.json(
        { error: 'Le contenu des fichiers ne peut pas être vide' },
        { status: 400 }
      );
    }

    const errorAnalysisService = new ErrorAnalysisService();
    const result = await errorAnalysisService.analyzeFiles(programFile, errorFile);

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erreur lors de l\'analyse d\'erreurs:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  const errorAnalysisService = new ErrorAnalysisService();
  const agentInfo = errorAnalysisService.getAgentInfo();

  return NextResponse.json({
    success: true,
    data: agentInfo,
    timestamp: new Date().toISOString()
  });
}
